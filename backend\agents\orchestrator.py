"""
Orchestrator Agent

- Receives user requests and decomposes them into subtasks.
- Delegates subtasks to specialized agents (coding, research, reasoning, debug, ask-user, etc.).
- Aggregates results, manages workflow, and ensures security/approval checkpoints.
"""

from ..services import openrouter_client
from .schema import LLMResponse, ToolCall
from .tool_registry import run_tool
from langchain.prompts import PromptTemplate
import json
import re

SYSTEM_PROMPT = """
You are Co<PERSON> Buddy, an agentic coding assistant. You can answer questions or call tools.
If you want to use a tool, respond ONLY with a JSON object: {"tool_call": {"tool_name": ..., "arguments": {...}}}
If you want to answer directly, respond ONLY with a JSON object: {"answer": "..."}
Available tools:
- read_file(rel_path: str)
- list_dir(rel_path: str = "")
- read_lines(rel_path: str, start_line: int, end_line: int)
- write_file(rel_path: str, content: str)
"""

class OrchestratorAgent:
    def __init__(self):
        pass

    async def handle_request(self, user_message: str, project_id: str = "demo", model: str = "openrouter/auto"):
        messages = [
            {"role": "system", "content": SYSTEM_PROMPT},
            {"role": "user", "content": user_message},
        ]

        max_turns = 20
        for _ in range(max_turns):
            raw = await openrouter_client.chat_completion(messages, model=model)
            
            # Extract JSON from the response. It might be in a markdown block or just embedded.
            json_str = None
            match = re.search(r"```json\s*(\{.*?\})\s*```", raw, re.DOTALL)
            if match:
                json_str = match.group(1)
            else:
                # If not in a block, maybe it's embedded in the text
                json_match = re.search(r"\{.*\}", raw, re.DOTALL)
                if json_match:
                    json_str = json_match.group(0)

            if not json_str:
                # If no JSON is found, assume it's a direct answer from the LLM.
                return {"answer": raw, "file_edit": None}

            try:
                data = json.loads(json_str)
                resp = LLMResponse.from_json(data)
            except Exception as e:
                return {"answer": f"[Error parsing LLM output: {e}]\nRaw: {raw}", "file_edit": None}

            if resp.answer:
                return {"answer": resp.answer, "file_edit": None}
            
            if resp.tool_call:
                messages.append({"role": "assistant", "content": json.dumps(data)})

                if resp.tool_call.tool_name == 'write_file':
                    args = resp.tool_call.arguments
                    return {
                        "answer": f"I have proposed changes for `{args.get('rel_path', 'a file')}`. Please review them.",
                        "file_edit": {"rel_path": args.get('rel_path'), "content": args.get('content')}
                    }

                tool_args = dict(resp.tool_call.arguments)
                tool_args["project_id"] = project_id
                try:
                    tool_result = run_tool(resp.tool_call.tool_name, tool_args)
                except Exception as e:
                    tool_result = f"[Tool error: {e}]"
                
                messages.append({"role": "tool", "content": str(tool_result)})
            else:
                return {"answer": f"[LLM response was valid JSON but had no 'answer' or 'tool_call'] Raw: {raw}", "file_edit": None}

        return {"answer": "[Agent stopped after reaching max turns]", "file_edit": None} 