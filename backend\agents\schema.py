from pydantic import BaseModel, Field
from typing import Optional, Dict, Any, Literal

class ToolCall(BaseModel):
    tool_name: str = Field(..., description="Name of the tool to call")
    arguments: Dict[str, Any] = Field(..., description="Arguments for the tool")

class LLMResponse(BaseModel):
    answer: Optional[str] = None
    tool_call: Optional[ToolCall] = None

    @classmethod
    def from_json(cls, data: dict):
        # Accepts either {"answer": ...} or {"tool_call": {...}}
        return cls(**data) 