from typing import Callable, Dict
from ..projects import file_ops

# Registry of available tools
TOOLS: Dict[str, Callable] = {
    "read_file": file_ops.read_file,
    "list_dir": file_ops.list_dir,
    "read_lines": file_ops.read_lines,
    "write_file": file_ops.write_file,
    # Add more tools as needed
}

# The orchestrator will not call these directly, but will inject project_id before calling the real function.

def run_tool(tool_name: str, arguments: dict):
    if tool_name not in TOOLS:
        raise ValueError(f"Tool '{tool_name}' not found")
    return TOOLS[tool_name](**arguments) 