from fastapi import APIRouter, HTTPException
from pydantic import BaseModel
from typing import List, Dict, Optional

from ..services import openrouter_client
from ..agents.orchestrator import OrchestratorAgent

router = APIRouter(prefix="/api/chat", tags=["chat"])

class Message(BaseModel):
    role: str  # "user" or "assistant"
    content: str

class ChatRequest(BaseModel):
    messages: List[Message]
    model: str = "openrouter/auto"

class ChatResponse(BaseModel):
    content: str

agent = OrchestratorAgent()

class FileEdit(BaseModel):
    rel_path: str
    content: str

class AgentChatRequest(BaseModel):
    question: str
    project_id: str = "demo"
    model: str = "openrouter/auto"

class AgentChatResponse(BaseModel):
    answer: str
    file_edit: Optional[FileEdit] = None

@router.post("/completion", response_model=ChatResponse)
async def chat_completion(req: ChatRequest):
    try:
        messages = [m.dict() for m in req.messages]
        content = await openrouter_client.chat_completion(messages, model=req.model)
        return ChatResponse(content=content)
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/agent", response_model=AgentChatResponse)
async def chat_agent(req: AgentChatRequest):
    response_data = await agent.handle_request(req.question, req.project_id, req.model)
    return AgentChatResponse(**response_data) 