import asyncio
from fastapi import APIRouter, HTTPException
from pydantic import BaseModel

from ..security.command_whitelist import is_command_safe

router = APIRouter(prefix="/api/terminal", tags=["terminal"])

class CommandRequest(BaseModel):
    command: str

class CommandResponse(BaseModel):
    stdout: str
    stderr: str
    code: int

@router.post("/run", response_model=CommandResponse)
async def run_command(req: CommandRequest):
    if not is_command_safe(req.command):
        raise HTTPException(status_code=400, detail="Command not allowed")

    # Run command asynchronously, capture output
    proc = await asyncio.create_subprocess_shell(
        req.command,
        stdout=asyncio.subprocess.PIPE,
        stderr=asyncio.subprocess.PIPE,
    )
    try:
        stdout_bytes, stderr_bytes = await asyncio.wait_for(proc.communicate(), timeout=30)
    except asyncio.TimeoutError:
        proc.kill()
        raise HTTPException(status_code=500, detail="Command timed out")

    stdout = stdout_bytes.decode(errors="ignore")
    stderr = stderr_bytes.decode(errors="ignore")
    return CommandResponse(stdout=stdout, stderr=stderr, code=proc.returncode) 