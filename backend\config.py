import os

# Base directory of the repo
BASE_DIR = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))

# Root directory where user projects are stored
PROJECTS_ROOT = os.path.join(BASE_DIR, "projects")

# Ensure the directory exists
os.makedirs(PROJECTS_ROOT, exist_ok=True)

# Create a default demo project with a sample file so the frontend isn\'t empty
DEMO_PROJECT_ID = "demo"
DEMO_ROOT = os.path.join(PROJECTS_ROOT, DEMO_PROJECT_ID)

if not os.path.isdir(DEMO_ROOT):
    os.makedirs(DEMO_ROOT, exist_ok=True)

README_PATH = os.path.join(DEMO_ROOT, "README.md")
if not os.path.exists(README_PATH):
    with open(README_PATH, "w", encoding="utf-8") as f:
        f.write("# Demo Project\n\nThis is a sample project created automatically by Coding <PERSON>. Feel free to edit or replace these files.") 