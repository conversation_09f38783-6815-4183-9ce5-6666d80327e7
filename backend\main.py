import os
from dotenv import load_dotenv

# Load environment variables first, before any other imports
load_dotenv()

from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware
from fastapi.staticfiles import StaticFiles
from .api import projects as projects_router
from .api import chat as chat_router
from .api import terminal as terminal_router

app = FastAPI()

# CORS for frontend dev
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

@app.get("/")
def read_root():
    return {"message": "Coding Buddy backend is running!"}

# Static files (React build)
static_dir = os.path.join(os.path.dirname(__file__), '..', 'frontend', 'build')
if os.path.isdir(static_dir):
    app.mount("/static", StaticFiles(directory=static_dir), name="static")

app.include_router(projects_router.router)
app.include_router(chat_router.router)
app.include_router(terminal_router.router) 