"""
Project file operations (read, write, list, etc.)
- All operations are restricted to the project root for isolation.
"""

from pathlib import Path
from typing import List, Dict
import os

from ..config import PROJECTS_ROOT
from ..security.sandbox import validate_project_path


def _resolve_project_path(project_id: str, rel_path: str = "") -> Path:
    """Return absolute path within the project root, ensuring isolation."""
    root = Path(PROJECTS_ROOT) / project_id
    root.mkdir(parents=True, exist_ok=True)
    abs_path = root / rel_path
    # Normalize path to remove .. or .
    abs_path = abs_path.resolve()
    try:
        abs_path.relative_to(root.resolve())
    except ValueError:
        raise ValueError("Invalid path: outside project root")
    if not validate_project_path(str(abs_path), str(root)):
        raise ValueError("Path validation failed")
    return abs_path


def list_dir(project_id: str, rel_path: str = "") -> List[Dict]:
    path = _resolve_project_path(project_id, rel_path)
    if not path.exists():
        return []
    entries = []
    for p in path.iterdir():
        entries.append({"name": p.name, "is_dir": p.is_dir()})
    return entries


def read_file(project_id: str, rel_path: str) -> str:
    path = _resolve_project_path(project_id, rel_path)
    if not path.is_file():
        raise FileNotFoundError("File not found")
    with open(path, "r", encoding="utf-8") as f:
        return f.read()


def read_lines(project_id: str, rel_path: str, start_line: int, end_line: int) -> str:
    path = _resolve_project_path(project_id, rel_path)
    if not path.is_file():
        raise FileNotFoundError("File not found")
    with open(path, "r", encoding="utf-8") as f:
        lines = f.readlines()
    
    # Line numbers are 1-based, list index is 0-based
    start_index = start_line - 1
    
    # Ensure indices are within bounds
    if start_index < 0 or start_index >= len(lines) or end_line > len(lines):
        raise ValueError(f"Line range ({start_line}-{end_line}) is out of bounds for file with {len(lines)} lines.")
        
    return "".join(lines[start_index:end_line])


def write_file(project_id: str, rel_path: str, content: str, *, approved: bool = True) -> None:
    if not approved:
        # This is the "first save" from the user, just a signal to show diff.
        # It's also used by the agent to propose changes, which the orchestrator intercepts.
        # We don't need to do anything on the backend for the unapproved step.
        return
    path = _resolve_project_path(project_id, rel_path)
    path.parent.mkdir(parents=True, exist_ok=True)
    with open(path, "w", encoding="utf-8") as f:
        f.write(content) 