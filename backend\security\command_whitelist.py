"""
Command whitelisting logic.
- Only allows safe commands (ls, cat, grep, find, pip install, python, npm, git).
- Blocks dangerous commands (rm -rf, sudo, chmod, dd, etc.).
"""

SAFE_COMMANDS = [
    'ls', 'cat', 'grep', 'find', 'pip', 'python', 'npm', 'git'
]

BLOCKED_COMMANDS = [
    'rm', 'sudo', 'chmod', 'dd'
]

def is_command_safe(command: str) -> bool:
    # TODO: Implement command parsing and safety check
    return any(command.startswith(cmd) for cmd in SAFE_COMMANDS) 