[{"D:\\Documents_2\\New Coding\\CoderBuddy\\CoderBuddy\\frontend\\src\\index.js": "1", "D:\\Documents_2\\New Coding\\CoderBuddy\\CoderBuddy\\frontend\\src\\App.js": "2", "D:\\Documents_2\\New Coding\\CoderBuddy\\CoderBuddy\\frontend\\src\\components\\EditorTabs.js": "3", "D:\\Documents_2\\New Coding\\CoderBuddy\\CoderBuddy\\frontend\\src\\components\\FileExplorer.js": "4", "D:\\Documents_2\\New Coding\\CoderBuddy\\CoderBuddy\\frontend\\src\\services\\api.js": "5", "D:\\Documents_2\\New Coding\\CoderBuddy\\CoderBuddy\\frontend\\src\\components\\SaveDiffModal.js": "6", "D:\\Documents_2\\New Coding\\CoderBuddy\\CoderBuddy\\frontend\\src\\monacoLoaderConfig.js": "7", "D:\\Documents_2\\New Coding\\CoderBuddy\\CoderBuddy\\frontend\\src\\components\\ChatPanel.js": "8", "D:\\Documents_2\\New Coding\\CoderBuddy\\CoderBuddy\\frontend\\src\\components\\TerminalPanel.js": "9", "D:\\Documents_2\\New Coding\\CoderBuddy\\CoderBuddy\\frontend\\src\\components\\ProjectSelector.js": "10", "D:\\Documents_2\\New Coding\\CoderBuddy\\CoderBuddy\\frontend\\src\\components\\SettingsModal.js": "11"}, {"size": 404, "mtime": 1750691257420, "results": "12", "hashOfConfig": "13"}, {"size": 2223, "mtime": 1750704358410, "results": "14", "hashOfConfig": "13"}, {"size": 6077, "mtime": 1750704373331, "results": "15", "hashOfConfig": "13"}, {"size": 1982, "mtime": 1750701238882, "results": "16", "hashOfConfig": "13"}, {"size": 1731, "mtime": 1750701331118, "results": "17", "hashOfConfig": "13"}, {"size": 1454, "mtime": 1750689505319, "results": "18", "hashOfConfig": "13"}, {"size": 147, "mtime": 1750691243497, "results": "19", "hashOfConfig": "13"}, {"size": 3086, "mtime": 1750704341558, "results": "20", "hashOfConfig": "13"}, {"size": 1748, "mtime": 1750694400917, "results": "21", "hashOfConfig": "13"}, {"size": 2223, "mtime": 1750700838401, "results": "22", "hashOfConfig": "13"}, {"size": 2260, "mtime": 1750702659560, "results": "23", "hashOfConfig": "13"}, {"filePath": "24", "messages": "25", "suppressedMessages": "26", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "x9f002", {"filePath": "27", "messages": "28", "suppressedMessages": "29", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "30", "messages": "31", "suppressedMessages": "32", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "33", "messages": "34", "suppressedMessages": "35", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "36", "messages": "37", "suppressedMessages": "38", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "39", "messages": "40", "suppressedMessages": "41", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "42", "messages": "43", "suppressedMessages": "44", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "45", "messages": "46", "suppressedMessages": "47", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "48", "messages": "49", "suppressedMessages": "50", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "51", "messages": "52", "suppressedMessages": "53", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "54", "messages": "55", "suppressedMessages": "56", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "D:\\Documents_2\\New Coding\\CoderBuddy\\CoderBuddy\\frontend\\src\\index.js", [], [], "D:\\Documents_2\\New Coding\\CoderBuddy\\CoderBuddy\\frontend\\src\\App.js", [], [], "D:\\Documents_2\\New Coding\\CoderBuddy\\CoderBuddy\\frontend\\src\\components\\EditorTabs.js", ["57", "58"], [], "D:\\Documents_2\\New Coding\\CoderBuddy\\CoderBuddy\\frontend\\src\\components\\FileExplorer.js", [], [], "D:\\Documents_2\\New Coding\\CoderBuddy\\CoderBuddy\\frontend\\src\\services\\api.js", [], [], "D:\\Documents_2\\New Coding\\CoderBuddy\\CoderBuddy\\frontend\\src\\components\\SaveDiffModal.js", [], [], "D:\\Documents_2\\New Coding\\CoderBuddy\\CoderBuddy\\frontend\\src\\monacoLoaderConfig.js", [], [], "D:\\Documents_2\\New Coding\\CoderBuddy\\CoderBuddy\\frontend\\src\\components\\ChatPanel.js", [], [], "D:\\Documents_2\\New Coding\\CoderBuddy\\CoderBuddy\\frontend\\src\\components\\TerminalPanel.js", [], [], "D:\\Documents_2\\New Coding\\CoderBuddy\\CoderBuddy\\frontend\\src\\components\\ProjectSelector.js", [], [], "D:\\Documents_2\\New Coding\\CoderBuddy\\CoderBuddy\\frontend\\src\\components\\SettingsModal.js", [], [], {"ruleId": "59", "message": "60", "line": 16, "column": 5, "endLine": 16, "endColumn": 60, "severity": 2, "nodeType": null}, {"ruleId": "59", "message": "60", "line": 24, "column": 5, "endLine": 24, "endColumn": 60, "severity": 2, "nodeType": null}, "react-hooks/exhaustive-deps", "Definition for rule 'react-hooks/exhaustive-deps' was not found."]