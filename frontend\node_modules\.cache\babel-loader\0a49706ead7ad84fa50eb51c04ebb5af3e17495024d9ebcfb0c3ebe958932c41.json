{"ast": null, "code": "import { useContext, createContext, useState, useCallback, useRef, useLayoutEffect, useEffect } from 'react';\nimport { jsx } from 'react/jsx-runtime';\nfunction _extends() {\n  return _extends = Object.assign ? Object.assign.bind() : function (n) {\n    for (var e = 1; e < arguments.length; e++) {\n      var t = arguments[e];\n      for (var r in t) ({}).hasOwnProperty.call(t, r) && (n[r] = t[r]);\n    }\n    return n;\n  }, _extends.apply(null, arguments);\n}\nvar reservedModifierKeywords = ['shift', 'alt', 'meta', 'mod', 'ctrl'];\nvar mappedKeys = {\n  esc: 'escape',\n  \"return\": 'enter',\n  '.': 'period',\n  ',': 'comma',\n  '-': 'slash',\n  ' ': 'space',\n  '`': 'backquote',\n  '#': 'backslash',\n  '+': 'bracketright',\n  ShiftLeft: 'shift',\n  ShiftRight: 'shift',\n  AltLeft: 'alt',\n  AltRight: 'alt',\n  MetaLeft: 'meta',\n  MetaRight: 'meta',\n  OSLeft: 'meta',\n  OSRight: 'meta',\n  ControlLeft: 'ctrl',\n  ControlRight: 'ctrl'\n};\nfunction mapKey(key) {\n  return (key && mappedKeys[key] || key || '').trim().toLowerCase().replace(/key|digit|numpad|arrow/, '');\n}\nfunction isHotkeyModifier(key) {\n  return reservedModifierKeywords.includes(key);\n}\nfunction parseKeysHookInput(keys, splitKey) {\n  if (splitKey === void 0) {\n    splitKey = ',';\n  }\n  return keys.split(splitKey);\n}\nfunction parseHotkey(hotkey, combinationKey, description) {\n  if (combinationKey === void 0) {\n    combinationKey = '+';\n  }\n  var keys = hotkey.toLocaleLowerCase().split(combinationKey).map(function (k) {\n    return mapKey(k);\n  });\n  var modifiers = {\n    alt: keys.includes('alt'),\n    ctrl: keys.includes('ctrl') || keys.includes('control'),\n    shift: keys.includes('shift'),\n    meta: keys.includes('meta'),\n    mod: keys.includes('mod')\n  };\n  var singleCharKeys = keys.filter(function (k) {\n    return !reservedModifierKeywords.includes(k);\n  });\n  return _extends({}, modifiers, {\n    keys: singleCharKeys,\n    description: description,\n    hotkey: hotkey\n  });\n}\n(function () {\n  if (typeof document !== 'undefined') {\n    document.addEventListener('keydown', function (e) {\n      if (e.key === undefined) {\n        // Synthetic event (e.g., Chrome autofill).  Ignore.\n        return;\n      }\n      pushToCurrentlyPressedKeys([mapKey(e.key), mapKey(e.code)]);\n    });\n    document.addEventListener('keyup', function (e) {\n      if (e.key === undefined) {\n        // Synthetic event (e.g., Chrome autofill).  Ignore.\n        return;\n      }\n      removeFromCurrentlyPressedKeys([mapKey(e.key), mapKey(e.code)]);\n    });\n  }\n  if (typeof window !== 'undefined') {\n    window.addEventListener('blur', function () {\n      currentlyPressedKeys.clear();\n    });\n  }\n})();\nvar currentlyPressedKeys = /*#__PURE__*/new Set();\n// https://github.com/microsoft/TypeScript/issues/17002\nfunction isReadonlyArray(value) {\n  return Array.isArray(value);\n}\nfunction isHotkeyPressed(key, splitKey) {\n  if (splitKey === void 0) {\n    splitKey = ',';\n  }\n  var hotkeyArray = isReadonlyArray(key) ? key : key.split(splitKey);\n  return hotkeyArray.every(function (hotkey) {\n    return currentlyPressedKeys.has(hotkey.trim().toLowerCase());\n  });\n}\nfunction pushToCurrentlyPressedKeys(key) {\n  var hotkeyArray = Array.isArray(key) ? key : [key];\n  /*\r\n  Due to a weird behavior on macOS we need to clear the set if the user pressed down the meta key and presses another key.\r\n  https://stackoverflow.com/questions/11818637/why-does-javascript-drop-keyup-events-when-the-metakey-is-pressed-on-mac-browser\r\n  Otherwise the set will hold all ever pressed keys while the meta key is down which leads to wrong results.\r\n   */\n  if (currentlyPressedKeys.has('meta')) {\n    currentlyPressedKeys.forEach(function (key) {\n      return !isHotkeyModifier(key) && currentlyPressedKeys[\"delete\"](key.toLowerCase());\n    });\n  }\n  hotkeyArray.forEach(function (hotkey) {\n    return currentlyPressedKeys.add(hotkey.toLowerCase());\n  });\n}\nfunction removeFromCurrentlyPressedKeys(key) {\n  var hotkeyArray = Array.isArray(key) ? key : [key];\n  /*\r\n  Due to a weird behavior on macOS we need to clear the set if the user pressed down the meta key and presses another key.\r\n  https://stackoverflow.com/questions/11818637/why-does-javascript-drop-keyup-events-when-the-metakey-is-pressed-on-mac-browser\r\n  Otherwise the set will hold all ever pressed keys while the meta key is down which leads to wrong results.\r\n   */\n  if (key === 'meta') {\n    currentlyPressedKeys.clear();\n  } else {\n    hotkeyArray.forEach(function (hotkey) {\n      return currentlyPressedKeys[\"delete\"](hotkey.toLowerCase());\n    });\n  }\n}\nfunction maybePreventDefault(e, hotkey, preventDefault) {\n  if (typeof preventDefault === 'function' && preventDefault(e, hotkey) || preventDefault === true) {\n    e.preventDefault();\n  }\n}\nfunction isHotkeyEnabled(e, hotkey, enabled) {\n  if (typeof enabled === 'function') {\n    return enabled(e, hotkey);\n  }\n  return enabled === true || enabled === undefined;\n}\nfunction isKeyboardEventTriggeredByInput(ev) {\n  return isHotkeyEnabledOnTag(ev, ['input', 'textarea', 'select']);\n}\nfunction isHotkeyEnabledOnTag(event, enabledOnTags) {\n  if (enabledOnTags === void 0) {\n    enabledOnTags = false;\n  }\n  var target = event.target,\n    composed = event.composed;\n  var targetTagName = null;\n  if (isCustomElement(target) && composed) {\n    targetTagName = event.composedPath()[0] && event.composedPath()[0].tagName;\n  } else {\n    targetTagName = target && target.tagName;\n  }\n  if (isReadonlyArray(enabledOnTags)) {\n    return Boolean(targetTagName && enabledOnTags && enabledOnTags.some(function (tag) {\n      var _targetTagName;\n      return tag.toLowerCase() === ((_targetTagName = targetTagName) == null ? void 0 : _targetTagName.toLowerCase());\n    }));\n  }\n  return Boolean(targetTagName && enabledOnTags && enabledOnTags);\n}\nfunction isCustomElement(element) {\n  // We just do a basic check w/o any complex RegEx or validation against the list of legacy names containing a hyphen,\n  // as none of them is likely to be an event target, and it won't hurt anyway if we miss.\n  // see: https://html.spec.whatwg.org/multipage/custom-elements.html#prod-potentialcustomelementname\n  return !!element.tagName && !element.tagName.startsWith(\"-\") && element.tagName.includes(\"-\");\n}\nfunction isScopeActive(activeScopes, scopes) {\n  if (activeScopes.length === 0 && scopes) {\n    console.warn('A hotkey has the \"scopes\" option set, however no active scopes were found. If you want to use the global scopes feature, you need to wrap your app in a <HotkeysProvider>');\n    return true;\n  }\n  if (!scopes) {\n    return true;\n  }\n  return activeScopes.some(function (scope) {\n    return scopes.includes(scope);\n  }) || activeScopes.includes('*');\n}\nvar isHotkeyMatchingKeyboardEvent = function isHotkeyMatchingKeyboardEvent(e, hotkey, ignoreModifiers) {\n  if (ignoreModifiers === void 0) {\n    ignoreModifiers = false;\n  }\n  var alt = hotkey.alt,\n    meta = hotkey.meta,\n    mod = hotkey.mod,\n    shift = hotkey.shift,\n    ctrl = hotkey.ctrl,\n    keys = hotkey.keys;\n  var pressedKeyUppercase = e.key,\n    code = e.code,\n    ctrlKey = e.ctrlKey,\n    metaKey = e.metaKey,\n    shiftKey = e.shiftKey,\n    altKey = e.altKey;\n  var keyCode = mapKey(code);\n  var pressedKey = pressedKeyUppercase.toLowerCase();\n  if (!(keys != null && keys.includes(keyCode)) && !(keys != null && keys.includes(pressedKey)) && !['ctrl', 'control', 'unknown', 'meta', 'alt', 'shift', 'os'].includes(keyCode)) {\n    return false;\n  }\n  if (!ignoreModifiers) {\n    // We check the pressed keys for compatibility with the keyup event. In keyup events the modifier flags are not set.\n    if (alt === !altKey && pressedKey !== 'alt') {\n      return false;\n    }\n    if (shift === !shiftKey && pressedKey !== 'shift') {\n      return false;\n    }\n    // Mod is a special key name that is checking for meta on macOS and ctrl on other platforms\n    if (mod) {\n      if (!metaKey && !ctrlKey) {\n        return false;\n      }\n    } else {\n      if (meta === !metaKey && pressedKey !== 'meta' && pressedKey !== 'os') {\n        return false;\n      }\n      if (ctrl === !ctrlKey && pressedKey !== 'ctrl' && pressedKey !== 'control') {\n        return false;\n      }\n    }\n  }\n  // All modifiers are correct, now check the key\n  // If the key is set, we check for the key\n  if (keys && keys.length === 1 && (keys.includes(pressedKey) || keys.includes(keyCode))) {\n    return true;\n  } else if (keys) {\n    // Check if all keys are present in pressedDownKeys set\n    return isHotkeyPressed(keys);\n  } else if (!keys) {\n    // If the key is not set, we only listen for modifiers, that check went alright, so we return true\n    return true;\n  }\n  // There is nothing that matches.\n  return false;\n};\nvar BoundHotkeysProxyProvider = /*#__PURE__*/createContext(undefined);\nvar useBoundHotkeysProxy = function useBoundHotkeysProxy() {\n  return useContext(BoundHotkeysProxyProvider);\n};\nfunction BoundHotkeysProxyProviderProvider(_ref) {\n  var addHotkey = _ref.addHotkey,\n    removeHotkey = _ref.removeHotkey,\n    children = _ref.children;\n  return /*#__PURE__*/jsx(BoundHotkeysProxyProvider.Provider, {\n    value: {\n      addHotkey: addHotkey,\n      removeHotkey: removeHotkey\n    },\n    children: children\n  });\n}\nfunction deepEqual(x, y) {\n  //@ts-ignore\n  return x && y && typeof x === 'object' && typeof y === 'object' ? Object.keys(x).length === Object.keys(y).length &&\n  //@ts-ignore\n  Object.keys(x).reduce(function (isEqual, key) {\n    return isEqual && deepEqual(x[key], y[key]);\n  }, true) : x === y;\n}\nvar HotkeysContext = /*#__PURE__*/createContext({\n  hotkeys: [],\n  enabledScopes: [],\n  toggleScope: function toggleScope() {},\n  enableScope: function enableScope() {},\n  disableScope: function disableScope() {}\n});\nvar useHotkeysContext = function useHotkeysContext() {\n  return useContext(HotkeysContext);\n};\nvar HotkeysProvider = function HotkeysProvider(_ref) {\n  var _ref$initiallyActiveS = _ref.initiallyActiveScopes,\n    initiallyActiveScopes = _ref$initiallyActiveS === void 0 ? ['*'] : _ref$initiallyActiveS,\n    children = _ref.children;\n  var _useState = useState((initiallyActiveScopes == null ? void 0 : initiallyActiveScopes.length) > 0 ? initiallyActiveScopes : ['*']),\n    internalActiveScopes = _useState[0],\n    setInternalActiveScopes = _useState[1];\n  var _useState2 = useState([]),\n    boundHotkeys = _useState2[0],\n    setBoundHotkeys = _useState2[1];\n  var enableScope = useCallback(function (scope) {\n    setInternalActiveScopes(function (prev) {\n      if (prev.includes('*')) {\n        return [scope];\n      }\n      return Array.from(new Set([].concat(prev, [scope])));\n    });\n  }, []);\n  var disableScope = useCallback(function (scope) {\n    setInternalActiveScopes(function (prev) {\n      if (prev.filter(function (s) {\n        return s !== scope;\n      }).length === 0) {\n        return ['*'];\n      } else {\n        return prev.filter(function (s) {\n          return s !== scope;\n        });\n      }\n    });\n  }, []);\n  var toggleScope = useCallback(function (scope) {\n    setInternalActiveScopes(function (prev) {\n      if (prev.includes(scope)) {\n        if (prev.filter(function (s) {\n          return s !== scope;\n        }).length === 0) {\n          return ['*'];\n        } else {\n          return prev.filter(function (s) {\n            return s !== scope;\n          });\n        }\n      } else {\n        if (prev.includes('*')) {\n          return [scope];\n        }\n        return Array.from(new Set([].concat(prev, [scope])));\n      }\n    });\n  }, []);\n  var addBoundHotkey = useCallback(function (hotkey) {\n    setBoundHotkeys(function (prev) {\n      return [].concat(prev, [hotkey]);\n    });\n  }, []);\n  var removeBoundHotkey = useCallback(function (hotkey) {\n    setBoundHotkeys(function (prev) {\n      return prev.filter(function (h) {\n        return !deepEqual(h, hotkey);\n      });\n    });\n  }, []);\n  return /*#__PURE__*/jsx(HotkeysContext.Provider, {\n    value: {\n      enabledScopes: internalActiveScopes,\n      hotkeys: boundHotkeys,\n      enableScope: enableScope,\n      disableScope: disableScope,\n      toggleScope: toggleScope\n    },\n    children: /*#__PURE__*/jsx(BoundHotkeysProxyProviderProvider, {\n      addHotkey: addBoundHotkey,\n      removeHotkey: removeBoundHotkey,\n      children: children\n    })\n  });\n};\nfunction useDeepEqualMemo(value) {\n  var ref = useRef(undefined);\n  if (!deepEqual(ref.current, value)) {\n    ref.current = value;\n  }\n  return ref.current;\n}\nvar stopPropagation = function stopPropagation(e) {\n  e.stopPropagation();\n  e.preventDefault();\n  e.stopImmediatePropagation();\n};\nvar useSafeLayoutEffect = typeof window !== 'undefined' ? useLayoutEffect : useEffect;\nfunction useHotkeys(keys, callback, options, dependencies) {\n  var _useState = useState(null),\n    ref = _useState[0],\n    setRef = _useState[1];\n  var hasTriggeredRef = useRef(false);\n  var _options = !(options instanceof Array) ? options : !(dependencies instanceof Array) ? dependencies : undefined;\n  var _keys = isReadonlyArray(keys) ? keys.join(_options == null ? void 0 : _options.splitKey) : keys;\n  var _deps = options instanceof Array ? options : dependencies instanceof Array ? dependencies : undefined;\n  var memoisedCB = useCallback(callback, _deps != null ? _deps : []);\n  var cbRef = useRef(memoisedCB);\n  if (_deps) {\n    cbRef.current = memoisedCB;\n  } else {\n    cbRef.current = callback;\n  }\n  var memoisedOptions = useDeepEqualMemo(_options);\n  var _useHotkeysContext = useHotkeysContext(),\n    enabledScopes = _useHotkeysContext.enabledScopes;\n  var proxy = useBoundHotkeysProxy();\n  useSafeLayoutEffect(function () {\n    if ((memoisedOptions == null ? void 0 : memoisedOptions.enabled) === false || !isScopeActive(enabledScopes, memoisedOptions == null ? void 0 : memoisedOptions.scopes)) {\n      return;\n    }\n    var listener = function listener(e, isKeyUp) {\n      var _e$target;\n      if (isKeyUp === void 0) {\n        isKeyUp = false;\n      }\n      if (isKeyboardEventTriggeredByInput(e) && !isHotkeyEnabledOnTag(e, memoisedOptions == null ? void 0 : memoisedOptions.enableOnFormTags)) {\n        return;\n      }\n      // TODO: SINCE THE EVENT IS NOW ATTACHED TO THE REF, THE ACTIVE ELEMENT CAN NEVER BE INSIDE THE REF. THE HOTKEY ONLY TRIGGERS IF THE\n      // REF IS THE ACTIVE ELEMENT. THIS IS A PROBLEM SINCE FOCUSED SUB COMPONENTS WON'T TRIGGER THE HOTKEY.\n      if (ref !== null) {\n        var rootNode = ref.getRootNode();\n        if ((rootNode instanceof Document || rootNode instanceof ShadowRoot) && rootNode.activeElement !== ref && !ref.contains(rootNode.activeElement)) {\n          stopPropagation(e);\n          return;\n        }\n      }\n      if ((_e$target = e.target) != null && _e$target.isContentEditable && !(memoisedOptions != null && memoisedOptions.enableOnContentEditable)) {\n        return;\n      }\n      parseKeysHookInput(_keys, memoisedOptions == null ? void 0 : memoisedOptions.splitKey).forEach(function (key) {\n        var _hotkey$keys;\n        var hotkey = parseHotkey(key, memoisedOptions == null ? void 0 : memoisedOptions.combinationKey);\n        if (isHotkeyMatchingKeyboardEvent(e, hotkey, memoisedOptions == null ? void 0 : memoisedOptions.ignoreModifiers) || (_hotkey$keys = hotkey.keys) != null && _hotkey$keys.includes('*')) {\n          if (memoisedOptions != null && memoisedOptions.ignoreEventWhen != null && memoisedOptions.ignoreEventWhen(e)) {\n            return;\n          }\n          if (isKeyUp && hasTriggeredRef.current) {\n            return;\n          }\n          maybePreventDefault(e, hotkey, memoisedOptions == null ? void 0 : memoisedOptions.preventDefault);\n          if (!isHotkeyEnabled(e, hotkey, memoisedOptions == null ? void 0 : memoisedOptions.enabled)) {\n            stopPropagation(e);\n            return;\n          }\n          // Execute the user callback for that hotkey\n          cbRef.current(e, hotkey);\n          if (!isKeyUp) {\n            hasTriggeredRef.current = true;\n          }\n        }\n      });\n    };\n    var handleKeyDown = function handleKeyDown(event) {\n      if (event.key === undefined) {\n        // Synthetic event (e.g., Chrome autofill).  Ignore.\n        return;\n      }\n      pushToCurrentlyPressedKeys(mapKey(event.code));\n      if ((memoisedOptions == null ? void 0 : memoisedOptions.keydown) === undefined && (memoisedOptions == null ? void 0 : memoisedOptions.keyup) !== true || memoisedOptions != null && memoisedOptions.keydown) {\n        listener(event);\n      }\n    };\n    var handleKeyUp = function handleKeyUp(event) {\n      if (event.key === undefined) {\n        // Synthetic event (e.g., Chrome autofill).  Ignore.\n        return;\n      }\n      removeFromCurrentlyPressedKeys(mapKey(event.code));\n      hasTriggeredRef.current = false;\n      if (memoisedOptions != null && memoisedOptions.keyup) {\n        listener(event, true);\n      }\n    };\n    var domNode = ref || (_options == null ? void 0 : _options.document) || document;\n    // @ts-ignore\n    domNode.addEventListener('keyup', handleKeyUp, _options == null ? void 0 : _options.eventListenerOptions);\n    // @ts-ignore\n    domNode.addEventListener('keydown', handleKeyDown, _options == null ? void 0 : _options.eventListenerOptions);\n    if (proxy) {\n      parseKeysHookInput(_keys, memoisedOptions == null ? void 0 : memoisedOptions.splitKey).forEach(function (key) {\n        return proxy.addHotkey(parseHotkey(key, memoisedOptions == null ? void 0 : memoisedOptions.combinationKey, memoisedOptions == null ? void 0 : memoisedOptions.description));\n      });\n    }\n    return function () {\n      // @ts-ignore\n      domNode.removeEventListener('keyup', handleKeyUp, _options == null ? void 0 : _options.eventListenerOptions);\n      // @ts-ignore\n      domNode.removeEventListener('keydown', handleKeyDown, _options == null ? void 0 : _options.eventListenerOptions);\n      if (proxy) {\n        parseKeysHookInput(_keys, memoisedOptions == null ? void 0 : memoisedOptions.splitKey).forEach(function (key) {\n          return proxy.removeHotkey(parseHotkey(key, memoisedOptions == null ? void 0 : memoisedOptions.combinationKey, memoisedOptions == null ? void 0 : memoisedOptions.description));\n        });\n      }\n    };\n  }, [ref, _keys, memoisedOptions, enabledScopes]);\n  return setRef;\n}\nfunction useRecordHotkeys() {\n  var _useState = useState(new Set()),\n    keys = _useState[0],\n    setKeys = _useState[1];\n  var _useState2 = useState(false),\n    isRecording = _useState2[0],\n    setIsRecording = _useState2[1];\n  var handler = useCallback(function (event) {\n    if (event.key === undefined) {\n      // Synthetic event (e.g., Chrome autofill).  Ignore.\n      return;\n    }\n    event.preventDefault();\n    event.stopPropagation();\n    setKeys(function (prev) {\n      var newKeys = new Set(prev);\n      newKeys.add(mapKey(event.code));\n      return newKeys;\n    });\n  }, []);\n  var stop = useCallback(function () {\n    if (typeof document !== 'undefined') {\n      document.removeEventListener('keydown', handler);\n      setIsRecording(false);\n    }\n  }, [handler]);\n  var start = useCallback(function () {\n    setKeys(new Set());\n    if (typeof document !== 'undefined') {\n      stop();\n      document.addEventListener('keydown', handler);\n      setIsRecording(true);\n    }\n  }, [handler, stop]);\n  var resetKeys = useCallback(function () {\n    setKeys(new Set());\n  }, []);\n  return [keys, {\n    start: start,\n    stop: stop,\n    resetKeys: resetKeys,\n    isRecording: isRecording\n  }];\n}\nexport { HotkeysProvider, isHotkeyPressed, useHotkeys, useHotkeysContext, useRecordHotkeys };", "map": {"version": 3, "names": ["reservedModifierKeywords", "mapped<PERSON>eys", "esc", "ShiftLeft", "ShiftRight", "AltLeft", "AltRight", "MetaLeft", "MetaRight", "OSLeft", "OSRight", "ControlLeft", "ControlRight", "mapKey", "key", "trim", "toLowerCase", "replace", "isHotkeyModifier", "includes", "parseKeysHookInput", "keys", "splitKey", "split", "parseHotkey", "hotkey", "combinationKey", "description", "toLocaleLowerCase", "map", "k", "modifiers", "alt", "ctrl", "shift", "meta", "mod", "singleCharKeys", "filter", "_extends", "document", "addEventListener", "e", "undefined", "pushToCurrentlyPressedKeys", "code", "removeFromCurrentlyPressedKeys", "window", "currentlyPressedKeys", "clear", "Set", "isReadonlyArray", "value", "Array", "isArray", "isHotkeyPressed", "hotkeyArray", "every", "has", "for<PERSON>ach", "add", "maybePreventDefault", "preventDefault", "isHotkeyEnabled", "enabled", "isKeyboardEventTriggeredByInput", "ev", "isHotkeyEnabledOnTag", "event", "enabledOnTags", "target", "composed", "targetTagName", "isCustomElement", "<PERSON><PERSON><PERSON>", "tagName", "Boolean", "some", "tag", "_targetTagName", "element", "startsWith", "isScopeActive", "activeScopes", "scopes", "length", "console", "warn", "scope", "isHotkeyMatchingKeyboardEvent", "ignoreModifiers", "pressedKeyUppercase", "ctrl<PERSON>ey", "metaKey", "shift<PERSON>ey", "altKey", "keyCode", "<PERSON><PERSON><PERSON>", "BoundHotkeysProxyProvider", "createContext", "useBoundHotkeysProxy", "useContext", "BoundHotkeysProxyProviderProvider", "_ref", "addHotkey", "removeHotkey", "children", "jsx", "Provider", "deepEqual", "x", "y", "Object", "reduce", "isEqual", "HotkeysContext", "hotkeys", "enabledScopes", "toggleScope", "enableScope", "disableScope", "useHotkeysContext", "HotkeysProvider", "initiallyActiveScopes", "_ref$initiallyActiveS", "_useState", "useState", "internalActiveScopes", "setInternalActiveScopes", "_useState2", "boundHotkeys", "setBoundHotkeys", "useCallback", "prev", "from", "concat", "s", "addBoundHotkey", "removeBoundHotkey", "h", "useDeepEqualMemo", "ref", "useRef", "current", "stopPropagation", "stopImmediatePropagation", "useSafeLayoutEffect", "useLayoutEffect", "useEffect", "useHotkeys", "callback", "options", "dependencies", "setRef", "hasTriggeredRef", "_options", "_keys", "join", "_deps", "memoisedCB", "cbRef", "memoisedOptions", "_useHotkeysContext", "proxy", "listener", "isKeyUp", "enableOnFormTags", "rootNode", "getRootNode", "Document", "ShadowRoot", "activeElement", "contains", "_e$target", "isContentEditable", "enableOnContentEditable", "_hotkey$keys", "ignoreEventWhen", "handleKeyDown", "keydown", "keyup", "handleKeyUp", "domNode", "eventListenerOptions", "removeEventListener", "useRecordHotkeys", "set<PERSON><PERSON><PERSON>", "isRecording", "setIsRecording", "handler", "newKeys", "stop", "start", "resetKeys"], "sources": ["D:\\Documents_2\\New Coding\\CoderBuddy\\CoderBuddy\\frontend\\node_modules\\react-hotkeys-hook\\src\\parseHotkeys.ts", "D:\\Documents_2\\New Coding\\CoderBuddy\\CoderBuddy\\frontend\\node_modules\\react-hotkeys-hook\\src\\isHotkeyPressed.ts", "D:\\Documents_2\\New Coding\\CoderBuddy\\CoderBuddy\\frontend\\node_modules\\react-hotkeys-hook\\src\\validators.ts", "D:\\Documents_2\\New Coding\\CoderBuddy\\CoderBuddy\\frontend\\node_modules\\react-hotkeys-hook\\src\\BoundHotkeysProxyProvider.tsx", "D:\\Documents_2\\New Coding\\CoderBuddy\\CoderBuddy\\frontend\\node_modules\\react-hotkeys-hook\\src\\deepEqual.ts", "D:\\Documents_2\\New Coding\\CoderBuddy\\CoderBuddy\\frontend\\node_modules\\react-hotkeys-hook\\src\\HotkeysProvider.tsx", "D:\\Documents_2\\New Coding\\CoderBuddy\\CoderBuddy\\frontend\\node_modules\\react-hotkeys-hook\\src\\useDeepEqualMemo.ts", "D:\\Documents_2\\New Coding\\CoderBuddy\\CoderBuddy\\frontend\\node_modules\\react-hotkeys-hook\\src\\useHotkeys.ts", "D:\\Documents_2\\New Coding\\CoderBuddy\\CoderBuddy\\frontend\\node_modules\\react-hotkeys-hook\\src\\useRecordHotkeys.ts"], "sourcesContent": ["import { Hotkey, KeyboardModifiers } from './types'\n\nconst reservedModifierKeywords = ['shift', 'alt', 'meta', 'mod', 'ctrl']\n\nconst mappedKeys: Record<string, string> = {\n  esc: 'escape',\n  return: 'enter',\n  '.': 'period',\n  ',': 'comma',\n  '-': 'slash',\n  ' ': 'space',\n  '`': 'backquote',\n  '#': 'backslash',\n  '+': 'bracketright',\n  ShiftLeft: 'shift',\n  ShiftRight: 'shift',\n  AltLeft: 'alt',\n  AltRight: 'alt',\n  MetaLeft: 'meta',\n  MetaRight: 'meta',\n  OSLeft: 'meta',\n  OSRight: 'meta',\n  ControlLeft: 'ctrl',\n  ControlRight: 'ctrl',\n}\n\nexport function mapKey(key?: string): string {\n  return ((key && mappedKeys[key]) || key || '')\n    .trim()\n    .toLowerCase()\n    .replace(/key|digit|numpad|arrow/, '')\n}\n\nexport function isHotkeyModifier(key: string) {\n  return reservedModifierKeywords.includes(key)\n}\n\nexport function parseKeysHookInput(keys: string, splitKey = ','): string[] {\n  return keys.split(splitKey)\n}\n\nexport function parseHotkey(hotkey: string, combinationKey = '+', description?: string): Hotkey {\n  const keys = hotkey\n    .toLocaleLowerCase()\n    .split(combinationKey)\n    .map((k) => mapKey(k))\n\n  const modifiers: KeyboardModifiers = {\n    alt: keys.includes('alt'),\n    ctrl: keys.includes('ctrl') || keys.includes('control'),\n    shift: keys.includes('shift'),\n    meta: keys.includes('meta'),\n    mod: keys.includes('mod'),\n  }\n\n  const singleCharKeys = keys.filter((k) => !reservedModifierKeywords.includes(k))\n\n  return {\n    ...modifiers,\n    keys: singleCharKeys,\n    description,\n    hotkey,\n  }\n}\n", "import { isHotkeyModifier, mapKey } from './parseHotkeys'\n;(() => {\n  if (typeof document !== 'undefined') {\n    document.addEventListener('keydown', (e) => {\n      if (e.key === undefined) {\n        // Synthetic event (e.g., Chrome autofill).  Ignore.\n        return\n      }\n\n      pushToCurrentlyPressedKeys([mapKey(e.key), mapKey(e.code)])\n    })\n\n    document.addEventListener('keyup', (e) => {\n      if (e.key === undefined) {\n        // Synthetic event (e.g., Chrome autofill).  Ignore.\n        return\n      }\n\n      removeFromCurrentlyPressedKeys([mapKey(e.key), mapKey(e.code)])\n    })\n  }\n\n  if (typeof window !== 'undefined') {\n    window.addEventListener('blur', () => {\n      currentlyPressedKeys.clear()\n    })\n  }\n})()\n\nconst currentlyPressedKeys: Set<string> = new Set<string>()\n\n// https://github.com/microsoft/TypeScript/issues/17002\nexport function isReadonlyArray(value: unknown): value is readonly unknown[] {\n  return Array.isArray(value)\n}\n\nexport function isHotkeyPressed(key: string | readonly string[], splitKey = ','): boolean {\n  const hotkeyArray = isReadonlyArray(key) ? key : key.split(splitKey)\n\n  return hotkeyArray.every((hotkey) => currentlyPressedKeys.has(hotkey.trim().toLowerCase()))\n}\n\nexport function pushToCurrentlyPressedKeys(key: string | string[]): void {\n  const hotkeyArray = Array.isArray(key) ? key : [key]\n\n  /*\n  Due to a weird behavior on macOS we need to clear the set if the user pressed down the meta key and presses another key.\n  https://stackoverflow.com/questions/11818637/why-does-javascript-drop-keyup-events-when-the-metakey-is-pressed-on-mac-browser\n  Otherwise the set will hold all ever pressed keys while the meta key is down which leads to wrong results.\n   */\n  if (currentlyPressedKeys.has('meta')) {\n    currentlyPressedKeys.forEach((key) => !isHotkeyModifier(key) && currentlyPressedKeys.delete(key.toLowerCase()))\n  }\n\n  hotkeyArray.forEach((hotkey) => currentlyPressedKeys.add(hotkey.toLowerCase()))\n}\n\nexport function removeFromCurrentlyPressedKeys(key: string | string[]): void {\n  const hotkeyArray = Array.isArray(key) ? key : [key]\n\n  /*\n  Due to a weird behavior on macOS we need to clear the set if the user pressed down the meta key and presses another key.\n  https://stackoverflow.com/questions/11818637/why-does-javascript-drop-keyup-events-when-the-metakey-is-pressed-on-mac-browser\n  Otherwise the set will hold all ever pressed keys while the meta key is down which leads to wrong results.\n   */\n  if (key === 'meta') {\n    currentlyPressedKeys.clear()\n  } else {\n    hotkeyArray.forEach((hotkey) => currentlyPressedKeys.delete(hotkey.toLowerCase()))\n  }\n}\n", "import { FormT<PERSON><PERSON>, Hotkey, Scopes, Trigger } from './types'\nimport { isHotkeyPressed, isReadonlyArray } from './isHotkeyPressed'\nimport { mapKey } from './parseHotkeys'\n\nexport function maybePreventDefault(e: KeyboardEvent, hotkey: Hotkey, preventDefault?: Trigger): void {\n  if ((typeof preventDefault === 'function' && preventDefault(e, hotkey)) || preventDefault === true) {\n    e.preventDefault()\n  }\n}\n\nexport function isHotkeyEnabled(e: KeyboardEvent, hotkey: Hotkey, enabled?: Trigger): boolean {\n  if (typeof enabled === 'function') {\n    return enabled(e, hotkey)\n  }\n\n  return enabled === true || enabled === undefined\n}\n\nexport function isKeyboardEventTriggeredByInput(ev: KeyboardEvent): boolean {\n  return isHotkeyEnabledOnTag(ev, ['input', 'textarea', 'select'])\n}\n\nexport function isHotkeyEnabledOnTag(\n  event: KeyboardEvent,\n  enabledOnTags: readonly FormTags[] | boolean = false\n): boolean {\n  const {target, composed} = event;\n\n  let targetTagName: string | null = null\n\n  if (isCustomElement(target as HTMLElement) && composed) {\n    targetTagName = event.composedPath()[0] && (event.composedPath()[0] as HTMLElement).tagName;\n  } else {\n    targetTagName = target && (target as HTMLElement).tagName;\n  }\n\n  if (isReadonlyArray(enabledOnTags)) {\n    return Boolean(\n      targetTagName && enabledOnTags && enabledOnTags.some((tag) => tag.toLowerCase() === targetTagName?.toLowerCase())\n    )\n  }\n\n  return Boolean(targetTagName && enabledOnTags && enabledOnTags)\n}\n\nexport function isCustomElement(element: HTMLElement): boolean {\n  // We just do a basic check w/o any complex RegEx or validation against the list of legacy names containing a hyphen,\n  // as none of them is likely to be an event target, and it won't hurt anyway if we miss.\n  // see: https://html.spec.whatwg.org/multipage/custom-elements.html#prod-potentialcustomelementname\n  return !!element.tagName && !element.tagName.startsWith(\"-\") && element.tagName.includes(\"-\");\n}\n\nexport function isScopeActive(activeScopes: string[], scopes?: Scopes): boolean {\n  if (activeScopes.length === 0 && scopes) {\n    console.warn(\n      'A hotkey has the \"scopes\" option set, however no active scopes were found. If you want to use the global scopes feature, you need to wrap your app in a <HotkeysProvider>'\n    )\n\n    return true\n  }\n\n  if (!scopes) {\n    return true\n  }\n\n  return activeScopes.some((scope) => scopes.includes(scope)) || activeScopes.includes('*')\n}\n\nexport const isHotkeyMatchingKeyboardEvent = (e: KeyboardEvent, hotkey: Hotkey, ignoreModifiers = false): boolean => {\n  const { alt, meta, mod, shift, ctrl, keys } = hotkey\n  const { key: pressedKeyUppercase, code, ctrlKey, metaKey, shiftKey, altKey } = e\n\n  const keyCode = mapKey(code)\n  const pressedKey = pressedKeyUppercase.toLowerCase()\n\n  if (\n    !keys?.includes(keyCode) &&\n    !keys?.includes(pressedKey) &&\n    !['ctrl', 'control', 'unknown', 'meta', 'alt', 'shift', 'os'].includes(keyCode)\n  ) {\n    return false\n  }\n\n  if (!ignoreModifiers) {\n    // We check the pressed keys for compatibility with the keyup event. In keyup events the modifier flags are not set.\n    if (alt === !altKey && pressedKey !== 'alt') {\n      return false\n    }\n\n    if (shift === !shiftKey && pressedKey !== 'shift') {\n      return false\n    }\n\n    // Mod is a special key name that is checking for meta on macOS and ctrl on other platforms\n    if (mod) {\n      if (!metaKey && !ctrlKey) {\n        return false\n      }\n    } else {\n      if (meta === !metaKey && pressedKey !== 'meta' && pressedKey !== 'os') {\n        return false\n      }\n\n      if (ctrl === !ctrlKey && pressedKey !== 'ctrl' && pressedKey !== 'control') {\n        return false\n      }\n    }\n  }\n\n  // All modifiers are correct, now check the key\n  // If the key is set, we check for the key\n  if (keys && keys.length === 1 && (keys.includes(pressedKey) || keys.includes(keyCode))) {\n    return true\n  } else if (keys) {\n    // Check if all keys are present in pressedDownKeys set\n    return isHotkeyPressed(keys)\n  } else if (!keys) {\n    // If the key is not set, we only listen for modifiers, that check went alright, so we return true\n    return true\n  }\n\n  // There is nothing that matches.\n  return false\n}\n", "import { createContext, ReactNode, useContext } from 'react'\nimport { Hotkey } from './types'\n\ntype BoundHotkeysProxyProviderType = {\n  addHotkey: (hotkey: Hotkey) => void\n  removeHotkey: (hotkey: Hotkey) => void\n}\n\nconst BoundHotkeysProxyProvider = createContext<BoundHotkeysProxyProviderType | undefined>(undefined)\n\nexport const useBoundHotkeysProxy = () => {\n  return useContext(BoundHotkeysProxyProvider)\n}\n\ninterface Props {\n  children: ReactNode\n  addHotkey: (hotkey: Hotkey) => void\n  removeHotkey: (hotkey: Hotkey) => void\n}\n\nexport default function BoundHotkeysProxyProviderProvider({ addHotkey, removeHotkey, children }: Props) {\n  return (\n    <BoundHotkeysProxyProvider.Provider value={{ addHotkey, removeHotkey }}>\n      {children}\n    </BoundHotkeysProxyProvider.Provider>\n  )\n}\n", "export default function deepEqual(x: any, y: any): boolean {\n  //@ts-ignore\n  return x && y && typeof x === 'object' && typeof y === 'object'\n    ? Object.keys(x).length === Object.keys(y).length &&\n        //@ts-ignore\n        Object.keys(x).reduce((isEqual, key) => isEqual && deepEqual(x[key], y[key]), true)\n    : x === y\n}\n", "import { Hotkey } from './types'\nimport { createContext, ReactNode, useState, useContext, useCallback } from 'react'\nimport BoundHotkeysProxyProviderProvider from './BoundHotkeysProxyProvider'\nimport deepEqual from './deepEqual'\n\nexport type HotkeysContextType = {\n  hotkeys: ReadonlyArray<Hotkey>\n  enabledScopes: string[]\n  toggleScope: (scope: string) => void\n  enableScope: (scope: string) => void\n  disableScope: (scope: string) => void\n}\n\n// The context is only needed for special features like global scoping, so we use a graceful default fallback\nconst HotkeysContext = createContext<HotkeysContextType>({\n  hotkeys: [],\n  enabledScopes: [], // This array has to be empty instead of containing '*' as default, to check if the provider is set or not\n  toggleScope: () => {},\n  enableScope: () => {},\n  disableScope: () => {},\n})\n\nexport const useHotkeysContext = () => {\n  return useContext(HotkeysContext)\n}\n\ninterface Props {\n  initiallyActiveScopes?: string[]\n  children: ReactNode\n}\n\nexport const HotkeysProvider = ({ initiallyActiveScopes = ['*'], children }: Props) => {\n  const [internalActiveScopes, setInternalActiveScopes] = useState(\n    initiallyActiveScopes?.length > 0 ? initiallyActiveScopes : ['*']\n  )\n  const [boundHotkeys, setBoundHotkeys] = useState<Hotkey[]>([])\n\n  const enableScope = useCallback((scope: string) => {\n    setInternalActiveScopes((prev) => {\n      if (prev.includes('*')) {\n        return [scope]\n      }\n\n      return Array.from(new Set([...prev, scope]))\n    })\n  }, [])\n\n  const disableScope = useCallback((scope: string) => {\n    setInternalActiveScopes((prev) => {\n      if (prev.filter((s) => s !== scope).length === 0) {\n        return ['*']\n      } else {\n        return prev.filter((s) => s !== scope)\n      }\n    })\n  }, [])\n\n  const toggleScope = useCallback((scope: string) => {\n    setInternalActiveScopes((prev) => {\n      if (prev.includes(scope)) {\n        if (prev.filter((s) => s !== scope).length === 0) {\n          return ['*']\n        } else {\n          return prev.filter((s) => s !== scope)\n        }\n      } else {\n        if (prev.includes('*')) {\n          return [scope]\n        }\n\n        return Array.from(new Set([...prev, scope]))\n      }\n    })\n  }, [])\n\n  const addBoundHotkey = useCallback((hotkey: Hotkey) => {\n    setBoundHotkeys((prev) => [...prev, hotkey])\n  }, [])\n\n  const removeBoundHotkey = useCallback((hotkey: Hotkey) => {\n    setBoundHotkeys((prev) => prev.filter((h) => !deepEqual(h, hotkey)))\n  }, [])\n\n  return (\n    <HotkeysContext.Provider\n      value={{ enabledScopes: internalActiveScopes, hotkeys: boundHotkeys, enableScope, disableScope, toggleScope }}\n    >\n      <BoundHotkeysProxyProviderProvider addHotkey={addBoundHotkey} removeHotkey={removeBoundHotkey}>\n        {children}\n      </BoundHotkeysProxyProviderProvider>\n    </HotkeysContext.Provider>\n  )\n}\n", "import { useRef } from 'react'\nimport deepEqual from './deepEqual'\n\nexport default function useDeepEqualMemo<T>(value: T) {\n  const ref = useRef<T | undefined>(undefined)\n\n  if (!deepEqual(ref.current, value)) {\n    ref.current = value\n  }\n\n  return ref.current\n}\n", "import { HotkeyCallback, Keys, Options, OptionsOrDependencyArray, RefType } from './types'\nimport { DependencyList, RefCallback, useCallback, useEffect, useState, useLayoutEffect, useRef } from 'react'\nimport { mapKey, parseHotkey, parseKeysHookInput } from './parseHotkeys'\nimport {\n  isHotkeyEnabled,\n  isHotkeyEnabledOnTag,\n  isHotkeyMatchingKeyboardEvent,\n  isKeyboardEventTriggeredByInput,\n  isScopeActive,\n  maybePreventDefault,\n} from './validators'\nimport { useHotkeysContext } from './HotkeysProvider'\nimport { useBoundHotkeysProxy } from './BoundHotkeysProxyProvider'\nimport useDeepEqualMemo from './useDeepEqualMemo'\nimport { isReadonlyArray, pushToCurrentlyPressedKeys, removeFromCurrentlyPressedKeys } from './isHotkeyPressed'\n\nconst stopPropagation = (e: KeyboardEvent): void => {\n  e.stopPropagation()\n  e.preventDefault()\n  e.stopImmediatePropagation()\n}\n\nconst useSafeLayoutEffect = typeof window !== 'undefined' ? useLayoutEffect : useEffect\n\nexport default function useHotkeys<T extends HTMLElement>(\n  keys: Keys,\n  callback: HotkeyCallback,\n  options?: OptionsOrDependencyArray,\n  dependencies?: OptionsOrDependencyArray\n) {\n  const [ref, setRef] = useState<RefType<T>>(null)\n  const hasTriggeredRef = useRef(false)\n\n  const _options: Options | undefined = !(options instanceof Array)\n    ? (options as Options)\n    : !(dependencies instanceof Array)\n    ? (dependencies as Options)\n    : undefined\n  const _keys: string = isReadonlyArray(keys) ? keys.join(_options?.splitKey) : keys\n  const _deps: DependencyList | undefined =\n    options instanceof Array ? options : dependencies instanceof Array ? dependencies : undefined\n\n  const memoisedCB = useCallback(callback, _deps ?? [])\n  const cbRef = useRef<HotkeyCallback>(memoisedCB)\n\n  if (_deps) {\n    cbRef.current = memoisedCB\n  } else {\n    cbRef.current = callback\n  }\n\n  const memoisedOptions = useDeepEqualMemo(_options)\n\n  const { enabledScopes } = useHotkeysContext()\n  const proxy = useBoundHotkeysProxy()\n\n  useSafeLayoutEffect(() => {\n    if (memoisedOptions?.enabled === false || !isScopeActive(enabledScopes, memoisedOptions?.scopes)) {\n      return\n    }\n\n    const listener = (e: KeyboardEvent, isKeyUp = false) => {\n      if (isKeyboardEventTriggeredByInput(e) && !isHotkeyEnabledOnTag(e, memoisedOptions?.enableOnFormTags)) {\n        return\n      }\n\n      // TODO: SINCE THE EVENT IS NOW ATTACHED TO THE REF, THE ACTIVE ELEMENT CAN NEVER BE INSIDE THE REF. THE HOTKEY ONLY TRIGGERS IF THE\n      // REF IS THE ACTIVE ELEMENT. THIS IS A PROBLEM SINCE FOCUSED SUB COMPONENTS WON'T TRIGGER THE HOTKEY.\n      if (ref !== null) {\n        const rootNode = ref.getRootNode()\n        if (\n          (rootNode instanceof Document || rootNode instanceof ShadowRoot) &&\n          rootNode.activeElement !== ref &&\n          !ref.contains(rootNode.activeElement)\n        ) {\n          stopPropagation(e)\n          return\n        }\n      }\n\n      if ((e.target as HTMLElement)?.isContentEditable && !memoisedOptions?.enableOnContentEditable) {\n        return\n      }\n\n      parseKeysHookInput(_keys, memoisedOptions?.splitKey).forEach((key) => {\n        const hotkey = parseHotkey(key, memoisedOptions?.combinationKey)\n\n        if (isHotkeyMatchingKeyboardEvent(e, hotkey, memoisedOptions?.ignoreModifiers) || hotkey.keys?.includes('*')) {\n          if (memoisedOptions?.ignoreEventWhen?.(e)) {\n            return\n          }\n\n          if (isKeyUp && hasTriggeredRef.current) {\n            return\n          }\n\n          maybePreventDefault(e, hotkey, memoisedOptions?.preventDefault)\n\n          if (!isHotkeyEnabled(e, hotkey, memoisedOptions?.enabled)) {\n            stopPropagation(e)\n\n            return\n          }\n\n          // Execute the user callback for that hotkey\n          cbRef.current(e, hotkey)\n\n          if (!isKeyUp) {\n            hasTriggeredRef.current = true\n          }\n        }\n      })\n    }\n\n    const handleKeyDown = (event: KeyboardEvent) => {\n      if (event.key === undefined) {\n        // Synthetic event (e.g., Chrome autofill).  Ignore.\n        return\n      }\n\n      pushToCurrentlyPressedKeys(mapKey(event.code))\n\n      if ((memoisedOptions?.keydown === undefined && memoisedOptions?.keyup !== true) || memoisedOptions?.keydown) {\n        listener(event)\n      }\n    }\n\n    const handleKeyUp = (event: KeyboardEvent) => {\n      if (event.key === undefined) {\n        // Synthetic event (e.g., Chrome autofill).  Ignore.\n        return\n      }\n\n      removeFromCurrentlyPressedKeys(mapKey(event.code))\n\n      hasTriggeredRef.current = false\n\n      if (memoisedOptions?.keyup) {\n        listener(event, true)\n      }\n    }\n\n    const domNode = ref || _options?.document || document\n\n    // @ts-ignore\n    domNode.addEventListener('keyup', handleKeyUp, _options?.eventListenerOptions)\n    // @ts-ignore\n    domNode.addEventListener('keydown', handleKeyDown, _options?.eventListenerOptions)\n\n    if (proxy) {\n      parseKeysHookInput(_keys, memoisedOptions?.splitKey).forEach((key) =>\n        proxy.addHotkey(parseHotkey(key, memoisedOptions?.combinationKey, memoisedOptions?.description))\n      )\n    }\n\n    return () => {\n      // @ts-ignore\n      domNode.removeEventListener('keyup', handleKeyUp, _options?.eventListenerOptions)\n      // @ts-ignore\n      domNode.removeEventListener('keydown', handleKeyDown, _options?.eventListenerOptions)\n\n      if (proxy) {\n        parseKeysHookInput(_keys, memoisedOptions?.splitKey).forEach((key) =>\n          proxy.removeHotkey(parseHotkey(key, memoisedOptions?.combinationKey, memoisedOptions?.description))\n        )\n      }\n    }\n  }, [ref, _keys, memoisedOptions, enabledScopes])\n\n  return setRef as RefCallback<T>\n}\n", "import { useCallback, useState } from 'react'\nimport { mapKey } from './parseHotkeys'\n\nexport default function useRecordHotkeys() {\n  const [keys, setKeys] = useState(new Set<string>())\n  const [isRecording, setIsRecording] = useState(false)\n\n  const handler = useCallback((event: KeyboardEvent) => {\n    if (event.key === undefined) {\n      // Synthetic event (e.g., Chrome autofill).  Ignore.\n      return\n    }\n\n    event.preventDefault()\n    event.stopPropagation()\n\n    setKeys((prev) => {\n      const newKeys = new Set(prev)\n\n      newKeys.add(mapKey(event.code))\n\n      return newKeys\n    })\n  }, [])\n\n  const stop = useCallback(() => {\n    if (typeof document !== 'undefined') {\n      document.removeEventListener('keydown', handler)\n\n      setIsRecording(false)\n    }\n  }, [handler])\n\n  const start = useCallback(() => {\n    setKeys(new Set<string>())\n\n    if (typeof document !== 'undefined') {\n      stop()\n\n      document.addEventListener('keydown', handler)\n\n      setIsRecording(true)\n    }\n  }, [handler, stop])\n\n  const resetKeys = useCallback(() => {\n    setKeys(new Set<string>())\n  }, [])\n\n  return [keys, { start, stop, resetKeys, isRecording }] as const\n}\n"], "mappings": ";;;;;;;;;;;AAEA,IAAMA,wBAAwB,GAAG,CAAC,OAAO,EAAE,KAAK,EAAE,MAAM,EAAE,KAAK,EAAE,MAAM,CAAC;AAExE,IAAMC,UAAU,GAA2B;EACzCC,GAAG,EAAE,QAAQ;EACb,UAAQ,OAAO;EACf,GAAG,EAAE,QAAQ;EACb,GAAG,EAAE,OAAO;EACZ,GAAG,EAAE,OAAO;EACZ,GAAG,EAAE,OAAO;EACZ,GAAG,EAAE,WAAW;EAChB,GAAG,EAAE,WAAW;EAChB,GAAG,EAAE,cAAc;EACnBC,SAAS,EAAE,OAAO;EAClBC,UAAU,EAAE,OAAO;EACnBC,OAAO,EAAE,KAAK;EACdC,QAAQ,EAAE,KAAK;EACfC,QAAQ,EAAE,MAAM;EAChBC,SAAS,EAAE,MAAM;EACjBC,MAAM,EAAE,MAAM;EACdC,OAAO,EAAE,MAAM;EACfC,WAAW,EAAE,MAAM;EACnBC,YAAY,EAAE;CACf;SAEeC,MAAMA,CAACC,GAAY;EACjC,OAAO,CAAEA,GAAG,IAAIb,UAAU,CAACa,GAAG,CAAC,IAAKA,GAAG,IAAI,EAAE,EAC1CC,IAAI,EAAE,CACNC,WAAW,EAAE,CACbC,OAAO,CAAC,wBAAwB,EAAE,EAAE,CAAC;AAC1C;SAEgBC,gBAAgBA,CAACJ,GAAW;EAC1C,OAAOd,wBAAwB,CAACmB,QAAQ,CAACL,GAAG,CAAC;AAC/C;SAEgBM,kBAAkBA,CAACC,IAAY,EAAEC,QAAQ;MAARA,QAAQ;IAARA,QAAQ,GAAG,GAAG;;EAC7D,OAAOD,IAAI,CAACE,KAAK,CAACD,QAAQ,CAAC;AAC7B;SAEgBE,WAAWA,CAACC,MAAc,EAAEC,cAAc,EAAQC,WAAoB;MAA1CD,cAAc;IAAdA,cAAc,GAAG,GAAG;;EAC9D,IAAML,IAAI,GAAGI,MAAM,CAChBG,iBAAiB,EAAE,CACnBL,KAAK,CAACG,cAAc,CAAC,CACrBG,GAAG,CAAC,UAACC,CAAC;IAAA,OAAKjB,MAAM,CAACiB,CAAC,CAAC;IAAC;EAExB,IAAMC,SAAS,GAAsB;IACnCC,GAAG,EAAEX,IAAI,CAACF,QAAQ,CAAC,KAAK,CAAC;IACzBc,IAAI,EAAEZ,IAAI,CAACF,QAAQ,CAAC,MAAM,CAAC,IAAIE,IAAI,CAACF,QAAQ,CAAC,SAAS,CAAC;IACvDe,KAAK,EAAEb,IAAI,CAACF,QAAQ,CAAC,OAAO,CAAC;IAC7BgB,IAAI,EAAEd,IAAI,CAACF,QAAQ,CAAC,MAAM,CAAC;IAC3BiB,GAAG,EAAEf,IAAI,CAACF,QAAQ,CAAC,KAAK;GACzB;EAED,IAAMkB,cAAc,GAAGhB,IAAI,CAACiB,MAAM,CAAC,UAACR,CAAC;IAAA,OAAK,CAAC9B,wBAAwB,CAACmB,QAAQ,CAACW,CAAC,CAAC;IAAC;EAEhF,OAAAS,QAAA,KACKR,SAAS;IACZV,IAAI,EAAEgB,cAAc;IACpBV,WAAW,EAAXA,WAAW;IACXF,MAAM,EAANA;;AAEJ;AC9DC,CAAC;EACA,IAAI,OAAOe,QAAQ,KAAK,WAAW,EAAE;IACnCA,QAAQ,CAACC,gBAAgB,CAAC,SAAS,EAAE,UAACC,CAAC;MACrC,IAAIA,CAAC,CAAC5B,GAAG,KAAK6B,SAAS,EAAE;;QAEvB;;MAGFC,0BAA0B,CAAC,CAAC/B,MAAM,CAAC6B,CAAC,CAAC5B,GAAG,CAAC,EAAED,MAAM,CAAC6B,CAAC,CAACG,IAAI,CAAC,CAAC,CAAC;KAC5D,CAAC;IAEFL,QAAQ,CAACC,gBAAgB,CAAC,OAAO,EAAE,UAACC,CAAC;MACnC,IAAIA,CAAC,CAAC5B,GAAG,KAAK6B,SAAS,EAAE;;QAEvB;;MAGFG,8BAA8B,CAAC,CAACjC,MAAM,CAAC6B,CAAC,CAAC5B,GAAG,CAAC,EAAED,MAAM,CAAC6B,CAAC,CAACG,IAAI,CAAC,CAAC,CAAC;KAChE,CAAC;;EAGJ,IAAI,OAAOE,MAAM,KAAK,WAAW,EAAE;IACjCA,MAAM,CAACN,gBAAgB,CAAC,MAAM,EAAE;MAC9BO,oBAAoB,CAACC,KAAK,EAAE;KAC7B,CAAC;;AAEN,CAAC,GAAG;AAEJ,IAAMD,oBAAoB,gBAAgB,IAAIE,GAAG,EAAU;AAE3D;AACA,SAAgBC,eAAeA,CAACC,KAAc;EAC5C,OAAOC,KAAK,CAACC,OAAO,CAACF,KAAK,CAAC;AAC7B;AAEA,SAAgBG,eAAeA,CAACzC,GAA+B,EAAEQ,QAAQ;MAARA,QAAQ;IAARA,QAAQ,GAAG,GAAG;;EAC7E,IAAMkC,WAAW,GAAGL,eAAe,CAACrC,GAAG,CAAC,GAAGA,GAAG,GAAGA,GAAG,CAACS,KAAK,CAACD,QAAQ,CAAC;EAEpE,OAAOkC,WAAW,CAACC,KAAK,CAAC,UAAChC,MAAM;IAAA,OAAKuB,oBAAoB,CAACU,GAAG,CAACjC,MAAM,CAACV,IAAI,EAAE,CAACC,WAAW,EAAE,CAAC;IAAC;AAC7F;AAEA,SAAgB4B,0BAA0BA,CAAC9B,GAAsB;EAC/D,IAAM0C,WAAW,GAAGH,KAAK,CAACC,OAAO,CAACxC,GAAG,CAAC,GAAGA,GAAG,GAAG,CAACA,GAAG,CAAC;;;;;;EAOpD,IAAIkC,oBAAoB,CAACU,GAAG,CAAC,MAAM,CAAC,EAAE;IACpCV,oBAAoB,CAACW,OAAO,CAAC,UAAC7C,GAAG;MAAA,OAAK,CAACI,gBAAgB,CAACJ,GAAG,CAAC,IAAIkC,oBAAoB,UAAO,CAAClC,GAAG,CAACE,WAAW,EAAE,CAAC;MAAC;;EAGjHwC,WAAW,CAACG,OAAO,CAAC,UAAClC,MAAM;IAAA,OAAKuB,oBAAoB,CAACY,GAAG,CAACnC,MAAM,CAACT,WAAW,EAAE,CAAC;IAAC;AACjF;AAEA,SAAgB8B,8BAA8BA,CAAChC,GAAsB;EACnE,IAAM0C,WAAW,GAAGH,KAAK,CAACC,OAAO,CAACxC,GAAG,CAAC,GAAGA,GAAG,GAAG,CAACA,GAAG,CAAC;;;;;;EAOpD,IAAIA,GAAG,KAAK,MAAM,EAAE;IAClBkC,oBAAoB,CAACC,KAAK,EAAE;GAC7B,MAAM;IACLO,WAAW,CAACG,OAAO,CAAC,UAAClC,MAAM;MAAA,OAAKuB,oBAAoB,UAAO,CAACvB,MAAM,CAACT,WAAW,EAAE,CAAC;MAAC;;AAEtF;SClEgB6C,mBAAmBA,CAACnB,CAAgB,EAAEjB,MAAc,EAAEqC,cAAwB;EAC5F,IAAK,OAAOA,cAAc,KAAK,UAAU,IAAIA,cAAc,CAACpB,CAAC,EAAEjB,MAAM,CAAC,IAAKqC,cAAc,KAAK,IAAI,EAAE;IAClGpB,CAAC,CAACoB,cAAc,EAAE;;AAEtB;AAEA,SAAgBC,eAAeA,CAACrB,CAAgB,EAAEjB,MAAc,EAAEuC,OAAiB;EACjF,IAAI,OAAOA,OAAO,KAAK,UAAU,EAAE;IACjC,OAAOA,OAAO,CAACtB,CAAC,EAAEjB,MAAM,CAAC;;EAG3B,OAAOuC,OAAO,KAAK,IAAI,IAAIA,OAAO,KAAKrB,SAAS;AAClD;AAEA,SAAgBsB,+BAA+BA,CAACC,EAAiB;EAC/D,OAAOC,oBAAoB,CAACD,EAAE,EAAE,CAAC,OAAO,EAAE,UAAU,EAAE,QAAQ,CAAC,CAAC;AAClE;AAEA,SAAgBC,oBAAoBA,CAClCC,KAAoB,EACpBC,aAAA;MAAAA,aAAA;IAAAA,aAAA,GAA+C,KAAK;;EAEpD,IAAOC,MAAM,GAAcF,KAAK,CAAzBE,MAAM;IAAEC,QAAQ,GAAIH,KAAK,CAAjBG,QAAQ;EAEvB,IAAIC,aAAa,GAAkB,IAAI;EAEvC,IAAIC,eAAe,CAACH,MAAqB,CAAC,IAAIC,QAAQ,EAAE;IACtDC,aAAa,GAAGJ,KAAK,CAACM,YAAY,EAAE,CAAC,CAAC,CAAC,IAAKN,KAAK,CAACM,YAAY,EAAE,CAAC,CAAC,CAAiB,CAACC,OAAO;GAC5F,MAAM;IACLH,aAAa,GAAGF,MAAM,IAAKA,MAAsB,CAACK,OAAO;;EAG3D,IAAIxB,eAAe,CAACkB,aAAa,CAAC,EAAE;IAClC,OAAOO,OAAO,CACZJ,aAAa,IAAIH,aAAa,IAAIA,aAAa,CAACQ,IAAI,CAAC,UAACC,GAAG;MAAA,IAAAC,cAAA;MAAA,OAAKD,GAAG,CAAC9D,WAAW,EAAE,OAAA+D,cAAA,GAAKP,aAAa,qBAAbO,cAAA,CAAe/D,WAAW,EAAE;MAAC,CAClH;;EAGH,OAAO4D,OAAO,CAACJ,aAAa,IAAIH,aAAa,IAAIA,aAAa,CAAC;AACjE;AAEA,SAAgBI,eAAeA,CAACO,OAAoB;;;;EAIlD,OAAO,CAAC,CAACA,OAAO,CAACL,OAAO,IAAI,CAACK,OAAO,CAACL,OAAO,CAACM,UAAU,CAAC,GAAG,CAAC,IAAID,OAAO,CAACL,OAAO,CAACxD,QAAQ,CAAC,GAAG,CAAC;AAC/F;AAEA,SAAgB+D,aAAaA,CAACC,YAAsB,EAAEC,MAAe;EACnE,IAAID,YAAY,CAACE,MAAM,KAAK,CAAC,IAAID,MAAM,EAAE;IACvCE,OAAO,CAACC,IAAI,CACV,2KAA2K,CAC5K;IAED,OAAO,IAAI;;EAGb,IAAI,CAACH,MAAM,EAAE;IACX,OAAO,IAAI;;EAGb,OAAOD,YAAY,CAACN,IAAI,CAAC,UAACW,KAAK;IAAA,OAAKJ,MAAM,CAACjE,QAAQ,CAACqE,KAAK,CAAC;IAAC,IAAIL,YAAY,CAAChE,QAAQ,CAAC,GAAG,CAAC;AAC3F;AAEA,IAAasE,6BAA6B,GAAG,SAAhCA,6BAA6BA,CAAI/C,CAAgB,EAAEjB,MAAc,EAAEiE,eAAe;MAAfA,eAAe;IAAfA,eAAe,GAAG,KAAK;;EACrG,IAAQ1D,GAAG,GAAmCP,MAAM,CAA5CO,GAAG;IAAEG,IAAI,GAA6BV,MAAM,CAAvCU,IAAI;IAAEC,GAAG,GAAwBX,MAAM,CAAjCW,GAAG;IAAEF,KAAK,GAAiBT,MAAM,CAA5BS,KAAK;IAAED,IAAI,GAAWR,MAAM,CAArBQ,IAAI;IAAEZ,IAAI,GAAKI,MAAM,CAAfJ,IAAI;EACzC,IAAasE,mBAAmB,GAA+CjD,CAAC,CAAxE5B,GAAG;IAAuB+B,IAAI,GAAyCH,CAAC,CAA9CG,IAAI;IAAE+C,OAAO,GAAgClD,CAAC,CAAxCkD,OAAO;IAAEC,OAAO,GAAuBnD,CAAC,CAA/BmD,OAAO;IAAEC,QAAQ,GAAapD,CAAC,CAAtBoD,QAAQ;IAAEC,MAAM,GAAKrD,CAAC,CAAZqD,MAAM;EAE1E,IAAMC,OAAO,GAAGnF,MAAM,CAACgC,IAAI,CAAC;EAC5B,IAAMoD,UAAU,GAAGN,mBAAmB,CAAC3E,WAAW,EAAE;EAEpD,IACE,EAACK,IAAI,YAAJA,IAAI,CAAEF,QAAQ,CAAC6E,OAAO,CAAC,KACxB,EAAC3E,IAAI,YAAJA,IAAI,CAAEF,QAAQ,CAAC8E,UAAU,CAAC,KAC3B,CAAC,CAAC,MAAM,EAAE,SAAS,EAAE,SAAS,EAAE,MAAM,EAAE,KAAK,EAAE,OAAO,EAAE,IAAI,CAAC,CAAC9E,QAAQ,CAAC6E,OAAO,CAAC,EAC/E;IACA,OAAO,KAAK;;EAGd,IAAI,CAACN,eAAe,EAAE;;IAEpB,IAAI1D,GAAG,KAAK,CAAC+D,MAAM,IAAIE,UAAU,KAAK,KAAK,EAAE;MAC3C,OAAO,KAAK;;IAGd,IAAI/D,KAAK,KAAK,CAAC4D,QAAQ,IAAIG,UAAU,KAAK,OAAO,EAAE;MACjD,OAAO,KAAK;;;IAId,IAAI7D,GAAG,EAAE;MACP,IAAI,CAACyD,OAAO,IAAI,CAACD,OAAO,EAAE;QACxB,OAAO,KAAK;;KAEf,MAAM;MACL,IAAIzD,IAAI,KAAK,CAAC0D,OAAO,IAAII,UAAU,KAAK,MAAM,IAAIA,UAAU,KAAK,IAAI,EAAE;QACrE,OAAO,KAAK;;MAGd,IAAIhE,IAAI,KAAK,CAAC2D,OAAO,IAAIK,UAAU,KAAK,MAAM,IAAIA,UAAU,KAAK,SAAS,EAAE;QAC1E,OAAO,KAAK;;;;;;EAOlB,IAAI5E,IAAI,IAAIA,IAAI,CAACgE,MAAM,KAAK,CAAC,KAAKhE,IAAI,CAACF,QAAQ,CAAC8E,UAAU,CAAC,IAAI5E,IAAI,CAACF,QAAQ,CAAC6E,OAAO,CAAC,CAAC,EAAE;IACtF,OAAO,IAAI;GACZ,MAAM,IAAI3E,IAAI,EAAE;;IAEf,OAAOkC,eAAe,CAAClC,IAAI,CAAC;GAC7B,MAAM,IAAI,CAACA,IAAI,EAAE;;IAEhB,OAAO,IAAI;;;EAIb,OAAO,KAAK;AACd,CAAC;ACnHD,IAAM6E,yBAAyB,gBAAGC,aAAa,CAA4CxD,SAAS,CAAC;AAErG,IAAayD,oBAAoB,GAAG,SAAvBA,oBAAoBA,CAAA;EAC/B,OAAOC,UAAU,CAACH,yBAAyB,CAAC;AAC9C,CAAC;AAQD,SAAwBI,iCAAiCA,CAAAC,IAAA;MAAGC,SAAS,GAAAD,IAAA,CAATC,SAAS;IAAEC,YAAY,GAAAF,IAAA,CAAZE,YAAY;IAAEC,QAAQ,GAAAH,IAAA,CAARG,QAAQ;EAC3F,oBACEC,GAAA,CAACT,yBAAyB,CAACU,QAAQ;IAACxD,KAAK,EAAE;MAAEoD,SAAS,EAATA,SAAS;MAAEC,YAAY,EAAZA;KAAe;IAAAC,QAAA,EACpEA;GACiC,CAAC;AAEzC;SC1BwBG,SAASA,CAACC,CAAM,EAAEC,CAAM;;EAE9C,OAAOD,CAAC,IAAIC,CAAC,IAAI,OAAOD,CAAC,KAAK,QAAQ,IAAI,OAAOC,CAAC,KAAK,QAAQ,GAC3DC,MAAM,CAAC3F,IAAI,CAACyF,CAAC,CAAC,CAACzB,MAAM,KAAK2B,MAAM,CAAC3F,IAAI,CAAC0F,CAAC,CAAC,CAAC1B,MAAM;;EAE7C2B,MAAM,CAAC3F,IAAI,CAACyF,CAAC,CAAC,CAACG,MAAM,CAAC,UAACC,OAAO,EAAEpG,GAAG;IAAA,OAAKoG,OAAO,IAAIL,SAAS,CAACC,CAAC,CAAChG,GAAG,CAAC,EAAEiG,CAAC,CAACjG,GAAG,CAAC,CAAC;KAAE,IAAI,CAAC,GACrFgG,CAAC,KAAKC,CAAC;AACb;ACOA,IAAMI,cAAc,gBAAGhB,aAAa,CAAqB;EACvDiB,OAAO,EAAE,EAAE;EACXC,aAAa,EAAE,EAAE;EACjBC,WAAW,EAAE,SAAbA,WAAWA,CAAA,IAAU;EACrBC,WAAW,EAAE,SAAbA,WAAWA,CAAA,IAAU;EACrBC,YAAY,EAAE,SAAdA,YAAYA,CAAA;CACb,CAAC;AAEF,IAAaC,iBAAiB,GAAG,SAApBA,iBAAiBA,CAAA;EAC5B,OAAOpB,UAAU,CAACc,cAAc,CAAC;AACnC,CAAC;AAOD,IAAaO,eAAe,GAAG,SAAlBA,eAAeA,CAAAnB,IAAA;mCAAMoB,qBAAqB;IAArBA,qBAAqB,GAAAC,qBAAA,cAAG,CAAC,GAAG,CAAC,GAAAA,qBAAA;IAAElB,QAAQ,GAAAH,IAAA,CAARG,QAAQ;EACvE,IAAAmB,SAAA,GAAwDC,QAAQ,CAC9D,CAAAH,qBAAqB,oBAArBA,qBAAqB,CAAEtC,MAAM,IAAG,CAAC,GAAGsC,qBAAqB,GAAG,CAAC,GAAG,CAAC,CAClE;IAFMI,oBAAoB,GAAAF,SAAA;IAAEG,uBAAuB,GAAAH,SAAA;EAGpD,IAAAI,UAAA,GAAwCH,QAAQ,CAAW,EAAE,CAAC;IAAvDI,YAAY,GAAAD,UAAA;IAAEE,eAAe,GAAAF,UAAA;EAEpC,IAAMV,WAAW,GAAGa,WAAW,CAAC,UAAC5C,KAAa;IAC5CwC,uBAAuB,CAAC,UAACK,IAAI;MAC3B,IAAIA,IAAI,CAAClH,QAAQ,CAAC,GAAG,CAAC,EAAE;QACtB,OAAO,CAACqE,KAAK,CAAC;;MAGhB,OAAOnC,KAAK,CAACiF,IAAI,CAAC,IAAIpF,GAAG,IAAAqF,MAAA,CAAKF,IAAI,GAAE7C,KAAK,EAAC,CAAC,CAAC;KAC7C,CAAC;GACH,EAAE,EAAE,CAAC;EAEN,IAAMgC,YAAY,GAAGY,WAAW,CAAC,UAAC5C,KAAa;IAC7CwC,uBAAuB,CAAC,UAACK,IAAI;MAC3B,IAAIA,IAAI,CAAC/F,MAAM,CAAC,UAACkG,CAAC;QAAA,OAAKA,CAAC,KAAKhD,KAAK;QAAC,CAACH,MAAM,KAAK,CAAC,EAAE;QAChD,OAAO,CAAC,GAAG,CAAC;OACb,MAAM;QACL,OAAOgD,IAAI,CAAC/F,MAAM,CAAC,UAACkG,CAAC;UAAA,OAAKA,CAAC,KAAKhD,KAAK;UAAC;;KAEzC,CAAC;GACH,EAAE,EAAE,CAAC;EAEN,IAAM8B,WAAW,GAAGc,WAAW,CAAC,UAAC5C,KAAa;IAC5CwC,uBAAuB,CAAC,UAACK,IAAI;MAC3B,IAAIA,IAAI,CAAClH,QAAQ,CAACqE,KAAK,CAAC,EAAE;QACxB,IAAI6C,IAAI,CAAC/F,MAAM,CAAC,UAACkG,CAAC;UAAA,OAAKA,CAAC,KAAKhD,KAAK;UAAC,CAACH,MAAM,KAAK,CAAC,EAAE;UAChD,OAAO,CAAC,GAAG,CAAC;SACb,MAAM;UACL,OAAOgD,IAAI,CAAC/F,MAAM,CAAC,UAACkG,CAAC;YAAA,OAAKA,CAAC,KAAKhD,KAAK;YAAC;;OAEzC,MAAM;QACL,IAAI6C,IAAI,CAAClH,QAAQ,CAAC,GAAG,CAAC,EAAE;UACtB,OAAO,CAACqE,KAAK,CAAC;;QAGhB,OAAOnC,KAAK,CAACiF,IAAI,CAAC,IAAIpF,GAAG,IAAAqF,MAAA,CAAKF,IAAI,GAAE7C,KAAK,EAAC,CAAC,CAAC;;KAE/C,CAAC;GACH,EAAE,EAAE,CAAC;EAEN,IAAMiD,cAAc,GAAGL,WAAW,CAAC,UAAC3G,MAAc;IAChD0G,eAAe,CAAC,UAACE,IAAI;MAAA,UAAAE,MAAA,CAASF,IAAI,GAAE5G,MAAM;KAAC,CAAC;GAC7C,EAAE,EAAE,CAAC;EAEN,IAAMiH,iBAAiB,GAAGN,WAAW,CAAC,UAAC3G,MAAc;IACnD0G,eAAe,CAAC,UAACE,IAAI;MAAA,OAAKA,IAAI,CAAC/F,MAAM,CAAC,UAACqG,CAAC;QAAA,OAAK,CAAC9B,SAAS,CAAC8B,CAAC,EAAElH,MAAM,CAAC;QAAC;MAAC;GACrE,EAAE,EAAE,CAAC;EAEN,oBACEkF,GAAA,CAACQ,cAAc,CAACP,QAAQ;IACtBxD,KAAK,EAAE;MAAEiE,aAAa,EAAEU,oBAAoB;MAAEX,OAAO,EAAEc,YAAY;MAAEX,WAAW,EAAXA,WAAW;MAAEC,YAAY,EAAZA,YAAY;MAAEF,WAAW,EAAXA;KAAc;IAAAZ,QAAA,eAE9GC,GAAA,CAACL,iCAAiC;MAACE,SAAS,EAAEiC,cAAe;MAAChC,YAAY,EAAEiC,iBAAkB;MAAAhC,QAAA,EAC3FA;KACgC;GACZ,CAAC;AAE9B,CAAC;SCzFuBkC,gBAAgBA,CAAIxF,KAAQ;EAClD,IAAMyF,GAAG,GAAGC,MAAM,CAAgBnG,SAAS,CAAC;EAE5C,IAAI,CAACkE,SAAS,CAACgC,GAAG,CAACE,OAAO,EAAE3F,KAAK,CAAC,EAAE;IAClCyF,GAAG,CAACE,OAAO,GAAG3F,KAAK;;EAGrB,OAAOyF,GAAG,CAACE,OAAO;AACpB;ACKA,IAAMC,eAAe,GAAG,SAAlBA,eAAeA,CAAItG,CAAgB;EACvCA,CAAC,CAACsG,eAAe,EAAE;EACnBtG,CAAC,CAACoB,cAAc,EAAE;EAClBpB,CAAC,CAACuG,wBAAwB,EAAE;AAC9B,CAAC;AAED,IAAMC,mBAAmB,GAAG,OAAOnG,MAAM,KAAK,WAAW,GAAGoG,eAAe,GAAGC,SAAS;AAEvF,SAAwBC,UAAUA,CAChChI,IAAU,EACViI,QAAwB,EACxBC,OAAkC,EAClCC,YAAuC;EAEvC,IAAA3B,SAAA,GAAsBC,QAAQ,CAAa,IAAI,CAAC;IAAzCe,GAAG,GAAAhB,SAAA;IAAE4B,MAAM,GAAA5B,SAAA;EAClB,IAAM6B,eAAe,GAAGZ,MAAM,CAAC,KAAK,CAAC;EAErC,IAAMa,QAAQ,GAAwB,EAAEJ,OAAO,YAAYlG,KAAK,CAAC,GAC5DkG,OAAmB,GACpB,EAAEC,YAAY,YAAYnG,KAAK,CAAC,GAC/BmG,YAAwB,GACzB7G,SAAS;EACb,IAAMiH,KAAK,GAAWzG,eAAe,CAAC9B,IAAI,CAAC,GAAGA,IAAI,CAACwI,IAAI,CAACF,QAAQ,oBAARA,QAAQ,CAAErI,QAAQ,CAAC,GAAGD,IAAI;EAClF,IAAMyI,KAAK,GACTP,OAAO,YAAYlG,KAAK,GAAGkG,OAAO,GAAGC,YAAY,YAAYnG,KAAK,GAAGmG,YAAY,GAAG7G,SAAS;EAE/F,IAAMoH,UAAU,GAAG3B,WAAW,CAACkB,QAAQ,EAAEQ,KAAK,WAALA,KAAK,GAAI,EAAE,CAAC;EACrD,IAAME,KAAK,GAAGlB,MAAM,CAAiBiB,UAAU,CAAC;EAEhD,IAAID,KAAK,EAAE;IACTE,KAAK,CAACjB,OAAO,GAAGgB,UAAU;GAC3B,MAAM;IACLC,KAAK,CAACjB,OAAO,GAAGO,QAAQ;;EAG1B,IAAMW,eAAe,GAAGrB,gBAAgB,CAACe,QAAQ,CAAC;EAElD,IAAAO,kBAAA,GAA0BzC,iBAAiB,EAAE;IAArCJ,aAAa,GAAA6C,kBAAA,CAAb7C,aAAa;EACrB,IAAM8C,KAAK,GAAG/D,oBAAoB,EAAE;EAEpC8C,mBAAmB,CAAC;IAClB,IAAI,CAAAe,eAAe,oBAAfA,eAAe,CAAEjG,OAAO,MAAK,KAAK,IAAI,CAACkB,aAAa,CAACmC,aAAa,EAAE4C,eAAe,oBAAfA,eAAe,CAAE7E,MAAM,CAAC,EAAE;MAChG;;IAGF,IAAMgF,QAAQ,GAAG,SAAXA,QAAQA,CAAI1H,CAAgB,EAAE2H,OAAO;;UAAPA,OAAO;QAAPA,OAAO,GAAG,KAAK;;MACjD,IAAIpG,+BAA+B,CAACvB,CAAC,CAAC,IAAI,CAACyB,oBAAoB,CAACzB,CAAC,EAAEuH,eAAe,oBAAfA,eAAe,CAAEK,gBAAgB,CAAC,EAAE;QACrG;;;;MAKF,IAAIzB,GAAG,KAAK,IAAI,EAAE;QAChB,IAAM0B,QAAQ,GAAG1B,GAAG,CAAC2B,WAAW,EAAE;QAClC,IACE,CAACD,QAAQ,YAAYE,QAAQ,IAAIF,QAAQ,YAAYG,UAAU,KAC/DH,QAAQ,CAACI,aAAa,KAAK9B,GAAG,IAC9B,CAACA,GAAG,CAAC+B,QAAQ,CAACL,QAAQ,CAACI,aAAa,CAAC,EACrC;UACA3B,eAAe,CAACtG,CAAC,CAAC;UAClB;;;MAIJ,IAAK,CAAAmI,SAAA,GAAAnI,CAAC,CAAC4B,MAAsB,aAAxBuG,SAAA,CAA0BC,iBAAiB,IAAI,EAACb,eAAe,YAAfA,eAAe,CAAEc,uBAAuB,GAAE;QAC7F;;MAGF3J,kBAAkB,CAACwI,KAAK,EAAEK,eAAe,oBAAfA,eAAe,CAAE3I,QAAQ,CAAC,CAACqC,OAAO,CAAC,UAAC7C,GAAG;;QAC/D,IAAMW,MAAM,GAAGD,WAAW,CAACV,GAAG,EAAEmJ,eAAe,oBAAfA,eAAe,CAAEvI,cAAc,CAAC;QAEhE,IAAI+D,6BAA6B,CAAC/C,CAAC,EAAEjB,MAAM,EAAEwI,eAAe,oBAAfA,eAAe,CAAEvE,eAAe,CAAC,KAAAsF,YAAA,GAAIvJ,MAAM,CAACJ,IAAI,aAAX2J,YAAA,CAAa7J,QAAQ,CAAC,GAAG,CAAC,EAAE;UAC5G,IAAI8I,eAAe,YAAfA,eAAe,CAAEgB,eAAe,YAAhChB,eAAe,CAAEgB,eAAe,CAAGvI,CAAC,CAAC,EAAE;YACzC;;UAGF,IAAI2H,OAAO,IAAIX,eAAe,CAACX,OAAO,EAAE;YACtC;;UAGFlF,mBAAmB,CAACnB,CAAC,EAAEjB,MAAM,EAAEwI,eAAe,oBAAfA,eAAe,CAAEnG,cAAc,CAAC;UAE/D,IAAI,CAACC,eAAe,CAACrB,CAAC,EAAEjB,MAAM,EAAEwI,eAAe,oBAAfA,eAAe,CAAEjG,OAAO,CAAC,EAAE;YACzDgF,eAAe,CAACtG,CAAC,CAAC;YAElB;;;UAIFsH,KAAK,CAACjB,OAAO,CAACrG,CAAC,EAAEjB,MAAM,CAAC;UAExB,IAAI,CAAC4I,OAAO,EAAE;YACZX,eAAe,CAACX,OAAO,GAAG,IAAI;;;OAGnC,CAAC;KACH;IAED,IAAMmC,aAAa,GAAG,SAAhBA,aAAaA,CAAI9G,KAAoB;MACzC,IAAIA,KAAK,CAACtD,GAAG,KAAK6B,SAAS,EAAE;;QAE3B;;MAGFC,0BAA0B,CAAC/B,MAAM,CAACuD,KAAK,CAACvB,IAAI,CAAC,CAAC;MAE9C,IAAK,CAAAoH,eAAe,oBAAfA,eAAe,CAAEkB,OAAO,MAAKxI,SAAS,IAAI,CAAAsH,eAAe,oBAAfA,eAAe,CAAEmB,KAAK,MAAK,IAAI,IAAKnB,eAAe,YAAfA,eAAe,CAAEkB,OAAO,EAAE;QAC3Gf,QAAQ,CAAChG,KAAK,CAAC;;KAElB;IAED,IAAMiH,WAAW,GAAG,SAAdA,WAAWA,CAAIjH,KAAoB;MACvC,IAAIA,KAAK,CAACtD,GAAG,KAAK6B,SAAS,EAAE;;QAE3B;;MAGFG,8BAA8B,CAACjC,MAAM,CAACuD,KAAK,CAACvB,IAAI,CAAC,CAAC;MAElD6G,eAAe,CAACX,OAAO,GAAG,KAAK;MAE/B,IAAIkB,eAAe,YAAfA,eAAe,CAAEmB,KAAK,EAAE;QAC1BhB,QAAQ,CAAChG,KAAK,EAAE,IAAI,CAAC;;KAExB;IAED,IAAMkH,OAAO,GAAGzC,GAAG,KAAIc,QAAQ,oBAARA,QAAQ,CAAEnH,QAAQ,KAAIA,QAAQ;;IAGrD8I,OAAO,CAAC7I,gBAAgB,CAAC,OAAO,EAAE4I,WAAW,EAAE1B,QAAQ,oBAARA,QAAQ,CAAE4B,oBAAoB,CAAC;;IAE9ED,OAAO,CAAC7I,gBAAgB,CAAC,SAAS,EAAEyI,aAAa,EAAEvB,QAAQ,oBAARA,QAAQ,CAAE4B,oBAAoB,CAAC;IAElF,IAAIpB,KAAK,EAAE;MACT/I,kBAAkB,CAACwI,KAAK,EAAEK,eAAe,oBAAfA,eAAe,CAAE3I,QAAQ,CAAC,CAACqC,OAAO,CAAC,UAAC7C,GAAG;QAAA,OAC/DqJ,KAAK,CAAC3D,SAAS,CAAChF,WAAW,CAACV,GAAG,EAAEmJ,eAAe,oBAAfA,eAAe,CAAEvI,cAAc,EAAEuI,eAAe,oBAAfA,eAAe,CAAEtI,WAAW,CAAC,CAAC;QACjG;;IAGH,OAAO;;MAEL2J,OAAO,CAACE,mBAAmB,CAAC,OAAO,EAAEH,WAAW,EAAE1B,QAAQ,oBAARA,QAAQ,CAAE4B,oBAAoB,CAAC;;MAEjFD,OAAO,CAACE,mBAAmB,CAAC,SAAS,EAAEN,aAAa,EAAEvB,QAAQ,oBAARA,QAAQ,CAAE4B,oBAAoB,CAAC;MAErF,IAAIpB,KAAK,EAAE;QACT/I,kBAAkB,CAACwI,KAAK,EAAEK,eAAe,oBAAfA,eAAe,CAAE3I,QAAQ,CAAC,CAACqC,OAAO,CAAC,UAAC7C,GAAG;UAAA,OAC/DqJ,KAAK,CAAC1D,YAAY,CAACjF,WAAW,CAACV,GAAG,EAAEmJ,eAAe,oBAAfA,eAAe,CAAEvI,cAAc,EAAEuI,eAAe,oBAAfA,eAAe,CAAEtI,WAAW,CAAC,CAAC;UACpG;;KAEJ;GACF,EAAE,CAACkH,GAAG,EAAEe,KAAK,EAAEK,eAAe,EAAE5C,aAAa,CAAC,CAAC;EAEhD,OAAOoC,MAAwB;AACjC;SCvKwBgC,gBAAgBA,CAAA;EACtC,IAAA5D,SAAA,GAAwBC,QAAQ,CAAC,IAAI5E,GAAG,EAAU,CAAC;IAA5C7B,IAAI,GAAAwG,SAAA;IAAE6D,OAAO,GAAA7D,SAAA;EACpB,IAAAI,UAAA,GAAsCH,QAAQ,CAAC,KAAK,CAAC;IAA9C6D,WAAW,GAAA1D,UAAA;IAAE2D,cAAc,GAAA3D,UAAA;EAElC,IAAM4D,OAAO,GAAGzD,WAAW,CAAC,UAAChE,KAAoB;IAC/C,IAAIA,KAAK,CAACtD,GAAG,KAAK6B,SAAS,EAAE;;MAE3B;;IAGFyB,KAAK,CAACN,cAAc,EAAE;IACtBM,KAAK,CAAC4E,eAAe,EAAE;IAEvB0C,OAAO,CAAC,UAACrD,IAAI;MACX,IAAMyD,OAAO,GAAG,IAAI5I,GAAG,CAACmF,IAAI,CAAC;MAE7ByD,OAAO,CAAClI,GAAG,CAAC/C,MAAM,CAACuD,KAAK,CAACvB,IAAI,CAAC,CAAC;MAE/B,OAAOiJ,OAAO;KACf,CAAC;GACH,EAAE,EAAE,CAAC;EAEN,IAAMC,IAAI,GAAG3D,WAAW,CAAC;IACvB,IAAI,OAAO5F,QAAQ,KAAK,WAAW,EAAE;MACnCA,QAAQ,CAACgJ,mBAAmB,CAAC,SAAS,EAAEK,OAAO,CAAC;MAEhDD,cAAc,CAAC,KAAK,CAAC;;GAExB,EAAE,CAACC,OAAO,CAAC,CAAC;EAEb,IAAMG,KAAK,GAAG5D,WAAW,CAAC;IACxBsD,OAAO,CAAC,IAAIxI,GAAG,EAAU,CAAC;IAE1B,IAAI,OAAOV,QAAQ,KAAK,WAAW,EAAE;MACnCuJ,IAAI,EAAE;MAENvJ,QAAQ,CAACC,gBAAgB,CAAC,SAAS,EAAEoJ,OAAO,CAAC;MAE7CD,cAAc,CAAC,IAAI,CAAC;;GAEvB,EAAE,CAACC,OAAO,EAAEE,IAAI,CAAC,CAAC;EAEnB,IAAME,SAAS,GAAG7D,WAAW,CAAC;IAC5BsD,OAAO,CAAC,IAAIxI,GAAG,EAAU,CAAC;GAC3B,EAAE,EAAE,CAAC;EAEN,OAAO,CAAC7B,IAAI,EAAE;IAAE2K,KAAK,EAALA,KAAK;IAAED,IAAI,EAAJA,IAAI;IAAEE,SAAS,EAATA,SAAS;IAAEN,WAAW,EAAXA;GAAa,CAAU;AACjE", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}