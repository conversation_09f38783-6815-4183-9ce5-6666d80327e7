{"ast": null, "code": "var _jsxFileName = \"D:\\\\Documents_2\\\\New Coding\\\\CoderBuddy\\\\CoderBuddy\\\\frontend\\\\src\\\\App.js\",\n  _s = $RefreshSig$();\nimport React, { useState } from 'react';\nimport FileExplorer from './components/FileExplorer';\nimport EditorTabs from './components/EditorTabs';\nimport ChatPanel from './components/ChatPanel';\nimport TerminalPanel from './components/TerminalPanel';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nexport default function App() {\n  _s();\n  const [selectedFile, setSelectedFile] = useState(null);\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    style: {\n      display: 'flex',\n      height: '100vh'\n    },\n    children: [/*#__PURE__*/_jsxDEV(FileExplorer, {\n      onSelectFile: setSelectedFile\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 12,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(EditorTabs, {\n      selectedFile: selectedFile\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 13,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        width: '30%',\n        borderLeft: '1px solid #ccc',\n        display: 'flex',\n        flexDirection: 'column'\n      },\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          flex: 1\n        },\n        children: /*#__PURE__*/_jsxDEV(ChatPanel, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 16,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 15,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          flex: 1\n        },\n        children: /*#__PURE__*/_jsxDEV(TerminalPanel, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 19,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 18,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 14,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 11,\n    columnNumber: 5\n  }, this);\n}\n_s(App, \"cz3B5sQOxlrekAnUFUPeMaA2gqY=\");\n_c = App;\nvar _c;\n$RefreshReg$(_c, \"App\");", "map": {"version": 3, "names": ["React", "useState", "FileExplorer", "EditorTabs", "ChatPanel", "TerminalPanel", "jsxDEV", "_jsxDEV", "App", "_s", "selectedFile", "setSelectedFile", "style", "display", "height", "children", "onSelectFile", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "width", "borderLeft", "flexDirection", "flex", "_c", "$RefreshReg$"], "sources": ["D:/Documents_2/New Coding/CoderBuddy/CoderBuddy/frontend/src/App.js"], "sourcesContent": ["import React, { useState } from 'react';\r\nimport FileExplorer from './components/FileExplorer';\r\nimport EditorTabs from './components/EditorTabs';\r\nimport ChatPanel from './components/ChatPanel';\r\nimport TerminalPanel from './components/TerminalPanel';\r\n\r\nexport default function App() {\r\n  const [selectedFile, setSelectedFile] = useState(null);\r\n\r\n  return (\r\n    <div style={{ display: 'flex', height: '100vh' }}>\r\n      <FileExplorer onSelectFile={setSelectedFile} />\r\n      <EditorTabs selectedFile={selectedFile} />\r\n      <div style={{ width: '30%', borderLeft: '1px solid #ccc', display: 'flex', flexDirection: 'column' }}>\r\n        <div style={{ flex: 1 }}>\r\n          <ChatPanel />\r\n        </div>\r\n        <div style={{ flex: 1 }}>\r\n          <TerminalPanel />\r\n        </div>\r\n      </div>\r\n    </div>\r\n  );\r\n} "], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,OAAOC,YAAY,MAAM,2BAA2B;AACpD,OAAOC,UAAU,MAAM,yBAAyB;AAChD,OAAOC,SAAS,MAAM,wBAAwB;AAC9C,OAAOC,aAAa,MAAM,4BAA4B;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEvD,eAAe,SAASC,GAAGA,CAAA,EAAG;EAAAC,EAAA;EAC5B,MAAM,CAACC,YAAY,EAAEC,eAAe,CAAC,GAAGV,QAAQ,CAAC,IAAI,CAAC;EAEtD,oBACEM,OAAA;IAAKK,KAAK,EAAE;MAAEC,OAAO,EAAE,MAAM;MAAEC,MAAM,EAAE;IAAQ,CAAE;IAAAC,QAAA,gBAC/CR,OAAA,CAACL,YAAY;MAACc,YAAY,EAAEL;IAAgB;MAAAM,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eAC/Cb,OAAA,CAACJ,UAAU;MAACO,YAAY,EAAEA;IAAa;MAAAO,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eAC1Cb,OAAA;MAAKK,KAAK,EAAE;QAAES,KAAK,EAAE,KAAK;QAAEC,UAAU,EAAE,gBAAgB;QAAET,OAAO,EAAE,MAAM;QAAEU,aAAa,EAAE;MAAS,CAAE;MAAAR,QAAA,gBACnGR,OAAA;QAAKK,KAAK,EAAE;UAAEY,IAAI,EAAE;QAAE,CAAE;QAAAT,QAAA,eACtBR,OAAA,CAACH,SAAS;UAAAa,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV,CAAC,eACNb,OAAA;QAAKK,KAAK,EAAE;UAAEY,IAAI,EAAE;QAAE,CAAE;QAAAT,QAAA,eACtBR,OAAA,CAACF,aAAa;UAAAY,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACd,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV;AAACX,EAAA,CAjBuBD,GAAG;AAAAiB,EAAA,GAAHjB,GAAG;AAAA,IAAAiB,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}