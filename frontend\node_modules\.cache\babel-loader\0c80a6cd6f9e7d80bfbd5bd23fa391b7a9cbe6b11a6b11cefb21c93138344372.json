{"ast": null, "code": "var _jsxFileName = \"D:\\\\Documents_2\\\\New Coding\\\\CoderBuddy\\\\CoderBuddy\\\\frontend\\\\src\\\\components\\\\FileExplorer.js\",\n  _s = $RefreshSig$();\nimport React, { useEffect, useState } from 'react';\nimport { listDir } from '../services/api';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nexport default function FileExplorer({\n  onSelectFile,\n  projectId\n}) {\n  _s();\n  const [path, setPath] = useState(''); // current dir path\n  const [entries, setEntries] = useState([]);\n  const [error, setError] = useState(null);\n  useEffect(() => {\n    setPath('');\n  }, [projectId]);\n  useEffect(() => {\n    if (!projectId) return;\n    loadEntries(path);\n  }, [path, projectId]);\n  async function loadEntries(relPath) {\n    try {\n      const data = await listDir(relPath, projectId);\n      setEntries(data);\n      setError(null);\n    } catch (e) {\n      setError(e.message);\n    }\n  }\n  function handleClick(entry) {\n    if (entry.is_dir) {\n      setPath(path ? `${path}/${entry.name}` : entry.name);\n    } else {\n      const filePath = path ? `${path}/${entry.name}` : entry.name;\n      onSelectFile(filePath);\n    }\n  }\n  function handleBack() {\n    if (!path) return;\n    const parts = path.split('/');\n    parts.pop();\n    setPath(parts.join('/'));\n  }\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    style: {\n      width: '250px',\n      borderRight: '1px solid #ccc',\n      overflowY: 'auto'\n    },\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        padding: '8px',\n        borderBottom: '1px solid #ccc',\n        fontWeight: 'bold'\n      },\n      children: [\"File Explorer\", path && /*#__PURE__*/_jsxDEV(\"button\", {\n        style: {\n          marginLeft: 8\n        },\n        onClick: handleBack,\n        children: \"Up\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 49,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 46,\n      columnNumber: 7\n    }, this), error && /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        color: 'red'\n      },\n      children: error\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 54,\n      columnNumber: 17\n    }, this), /*#__PURE__*/_jsxDEV(\"ul\", {\n      style: {\n        listStyle: 'none',\n        padding: 0\n      },\n      children: [entries.map(entry => /*#__PURE__*/_jsxDEV(\"li\", {\n        style: {\n          cursor: 'pointer',\n          padding: '4px 8px'\n        },\n        onClick: () => handleClick(entry),\n        children: [entry.is_dir ? '📁' : '📄', \" \", entry.name]\n      }, entry.name, true, {\n        fileName: _jsxFileName,\n        lineNumber: 57,\n        columnNumber: 11\n      }, this)), entries.length === 0 && /*#__PURE__*/_jsxDEV(\"li\", {\n        style: {\n          padding: '4px 8px'\n        },\n        children: \"(empty)\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 65,\n        columnNumber: 34\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 55,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 45,\n    columnNumber: 5\n  }, this);\n}\n_s(FileExplorer, \"sJbhPwGeUmBOj/GbPZayfjA5/YU=\");\n_c = FileExplorer;\nvar _c;\n$RefreshReg$(_c, \"FileExplorer\");", "map": {"version": 3, "names": ["React", "useEffect", "useState", "listDir", "jsxDEV", "_jsxDEV", "FileExplorer", "onSelectFile", "projectId", "_s", "path", "set<PERSON>ath", "entries", "setEntries", "error", "setError", "loadEntries", "re<PERSON><PERSON><PERSON>", "data", "e", "message", "handleClick", "entry", "is_dir", "name", "filePath", "handleBack", "parts", "split", "pop", "join", "style", "width", "borderRight", "overflowY", "children", "padding", "borderBottom", "fontWeight", "marginLeft", "onClick", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "color", "listStyle", "map", "cursor", "length", "_c", "$RefreshReg$"], "sources": ["D:/Documents_2/New Coding/CoderBuddy/CoderBuddy/frontend/src/components/FileExplorer.js"], "sourcesContent": ["import React, { useEffect, useState } from 'react';\r\nimport { listDir } from '../services/api';\r\n\r\nexport default function FileExplorer({ onSelectFile, projectId }) {\r\n  const [path, setPath] = useState(''); // current dir path\r\n  const [entries, setEntries] = useState([]);\r\n  const [error, setError] = useState(null);\r\n\r\n  useEffect(() => {\r\n    setPath('');\r\n  }, [projectId]);\r\n\r\n  useEffect(() => {\r\n    if (!projectId) return;\r\n    loadEntries(path);\r\n  }, [path, projectId]);\r\n\r\n  async function loadEntries(relPath) {\r\n    try {\r\n      const data = await listDir(relPath, projectId);\r\n      setEntries(data);\r\n      setError(null);\r\n    } catch (e) {\r\n      setError(e.message);\r\n    }\r\n  }\r\n\r\n  function handleClick(entry) {\r\n    if (entry.is_dir) {\r\n      setPath(path ? `${path}/${entry.name}` : entry.name);\r\n    } else {\r\n      const filePath = path ? `${path}/${entry.name}` : entry.name;\r\n      onSelectFile(filePath);\r\n    }\r\n  }\r\n\r\n  function handleBack() {\r\n    if (!path) return;\r\n    const parts = path.split('/');\r\n    parts.pop();\r\n    setPath(parts.join('/'));\r\n  }\r\n\r\n  return (\r\n    <div style={{ width: '250px', borderRight: '1px solid #ccc', overflowY: 'auto' }}>\r\n      <div style={{ padding: '8px', borderBottom: '1px solid #ccc', fontWeight: 'bold' }}>\r\n        File Explorer\r\n        {path && (\r\n          <button style={{ marginLeft: 8 }} onClick={handleBack}>\r\n            Up\r\n          </button>\r\n        )}\r\n      </div>\r\n      {error && <div style={{ color: 'red' }}>{error}</div>}\r\n      <ul style={{ listStyle: 'none', padding: 0 }}>\r\n        {entries.map((entry) => (\r\n          <li\r\n            key={entry.name}\r\n            style={{ cursor: 'pointer', padding: '4px 8px' }}\r\n            onClick={() => handleClick(entry)}\r\n          >\r\n            {entry.is_dir ? '📁' : '📄'} {entry.name}\r\n          </li>\r\n        ))}\r\n        {entries.length === 0 && <li style={{ padding: '4px 8px' }}>(empty)</li>}\r\n      </ul>\r\n    </div>\r\n  );\r\n} "], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,SAAS,EAAEC,QAAQ,QAAQ,OAAO;AAClD,SAASC,OAAO,QAAQ,iBAAiB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE1C,eAAe,SAASC,YAAYA,CAAC;EAAEC,YAAY;EAAEC;AAAU,CAAC,EAAE;EAAAC,EAAA;EAChE,MAAM,CAACC,IAAI,EAAEC,OAAO,CAAC,GAAGT,QAAQ,CAAC,EAAE,CAAC,CAAC,CAAC;EACtC,MAAM,CAACU,OAAO,EAAEC,UAAU,CAAC,GAAGX,QAAQ,CAAC,EAAE,CAAC;EAC1C,MAAM,CAACY,KAAK,EAAEC,QAAQ,CAAC,GAAGb,QAAQ,CAAC,IAAI,CAAC;EAExCD,SAAS,CAAC,MAAM;IACdU,OAAO,CAAC,EAAE,CAAC;EACb,CAAC,EAAE,CAACH,SAAS,CAAC,CAAC;EAEfP,SAAS,CAAC,MAAM;IACd,IAAI,CAACO,SAAS,EAAE;IAChBQ,WAAW,CAACN,IAAI,CAAC;EACnB,CAAC,EAAE,CAACA,IAAI,EAAEF,SAAS,CAAC,CAAC;EAErB,eAAeQ,WAAWA,CAACC,OAAO,EAAE;IAClC,IAAI;MACF,MAAMC,IAAI,GAAG,MAAMf,OAAO,CAACc,OAAO,EAAET,SAAS,CAAC;MAC9CK,UAAU,CAACK,IAAI,CAAC;MAChBH,QAAQ,CAAC,IAAI,CAAC;IAChB,CAAC,CAAC,OAAOI,CAAC,EAAE;MACVJ,QAAQ,CAACI,CAAC,CAACC,OAAO,CAAC;IACrB;EACF;EAEA,SAASC,WAAWA,CAACC,KAAK,EAAE;IAC1B,IAAIA,KAAK,CAACC,MAAM,EAAE;MAChBZ,OAAO,CAACD,IAAI,GAAG,GAAGA,IAAI,IAAIY,KAAK,CAACE,IAAI,EAAE,GAAGF,KAAK,CAACE,IAAI,CAAC;IACtD,CAAC,MAAM;MACL,MAAMC,QAAQ,GAAGf,IAAI,GAAG,GAAGA,IAAI,IAAIY,KAAK,CAACE,IAAI,EAAE,GAAGF,KAAK,CAACE,IAAI;MAC5DjB,YAAY,CAACkB,QAAQ,CAAC;IACxB;EACF;EAEA,SAASC,UAAUA,CAAA,EAAG;IACpB,IAAI,CAAChB,IAAI,EAAE;IACX,MAAMiB,KAAK,GAAGjB,IAAI,CAACkB,KAAK,CAAC,GAAG,CAAC;IAC7BD,KAAK,CAACE,GAAG,CAAC,CAAC;IACXlB,OAAO,CAACgB,KAAK,CAACG,IAAI,CAAC,GAAG,CAAC,CAAC;EAC1B;EAEA,oBACEzB,OAAA;IAAK0B,KAAK,EAAE;MAAEC,KAAK,EAAE,OAAO;MAAEC,WAAW,EAAE,gBAAgB;MAAEC,SAAS,EAAE;IAAO,CAAE;IAAAC,QAAA,gBAC/E9B,OAAA;MAAK0B,KAAK,EAAE;QAAEK,OAAO,EAAE,KAAK;QAAEC,YAAY,EAAE,gBAAgB;QAAEC,UAAU,EAAE;MAAO,CAAE;MAAAH,QAAA,GAAC,eAElF,EAACzB,IAAI,iBACHL,OAAA;QAAQ0B,KAAK,EAAE;UAAEQ,UAAU,EAAE;QAAE,CAAE;QAACC,OAAO,EAAEd,UAAW;QAAAS,QAAA,EAAC;MAEvD;QAAAM,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CACT;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC,EACL9B,KAAK,iBAAIT,OAAA;MAAK0B,KAAK,EAAE;QAAEc,KAAK,EAAE;MAAM,CAAE;MAAAV,QAAA,EAAErB;IAAK;MAAA2B,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAM,CAAC,eACrDvC,OAAA;MAAI0B,KAAK,EAAE;QAAEe,SAAS,EAAE,MAAM;QAAEV,OAAO,EAAE;MAAE,CAAE;MAAAD,QAAA,GAC1CvB,OAAO,CAACmC,GAAG,CAAEzB,KAAK,iBACjBjB,OAAA;QAEE0B,KAAK,EAAE;UAAEiB,MAAM,EAAE,SAAS;UAAEZ,OAAO,EAAE;QAAU,CAAE;QACjDI,OAAO,EAAEA,CAAA,KAAMnB,WAAW,CAACC,KAAK,CAAE;QAAAa,QAAA,GAEjCb,KAAK,CAACC,MAAM,GAAG,IAAI,GAAG,IAAI,EAAC,GAAC,EAACD,KAAK,CAACE,IAAI;MAAA,GAJnCF,KAAK,CAACE,IAAI;QAAAiB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAKb,CACL,CAAC,EACDhC,OAAO,CAACqC,MAAM,KAAK,CAAC,iBAAI5C,OAAA;QAAI0B,KAAK,EAAE;UAAEK,OAAO,EAAE;QAAU,CAAE;QAAAD,QAAA,EAAC;MAAO;QAAAM,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACtE,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACF,CAAC;AAEV;AAACnC,EAAA,CAjEuBH,YAAY;AAAA4C,EAAA,GAAZ5C,YAAY;AAAA,IAAA4C,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}