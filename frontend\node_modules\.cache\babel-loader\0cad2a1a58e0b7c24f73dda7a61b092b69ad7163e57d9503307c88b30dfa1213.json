{"ast": null, "code": "var _jsxFileName = \"D:\\\\Documents_2\\\\New Coding\\\\CoderBuddy\\\\CoderBuddy\\\\frontend\\\\src\\\\App.js\",\n  _s = $RefreshSig$();\nimport React, { useState } from 'react';\nimport FileExplorer from './components/FileExplorer';\nimport EditorTabs from './components/EditorTabs';\nimport ChatPanel from './components/ChatPanel';\nimport TerminalPanel from './components/TerminalPanel';\nimport ProjectSelector from './components/ProjectSelector';\nimport SettingsModal from './components/SettingsModal';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nexport default function App() {\n  _s();\n  const [selectedFile, setSelectedFile] = useState(null);\n  const [projectId, setProjectId] = useState('demo');\n  const [settingsOpen, setSettingsOpen] = useState(false);\n  const [model, setModel] = useState('openrouter/auto');\n  const [proposedEdit, setProposedEdit] = useState(null);\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    style: {\n      display: 'flex',\n      flexDirection: 'column',\n      height: '100vh'\n    },\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        padding: 8,\n        borderBottom: '1px solid #ccc',\n        display: 'flex',\n        alignItems: 'center',\n        gap: 16\n      },\n      children: [/*#__PURE__*/_jsxDEV(ProjectSelector, {\n        projectId: projectId,\n        setProjectId: setProjectId\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 19,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n        onClick: () => setSettingsOpen(true),\n        style: {\n          background: 'none',\n          border: 'none',\n          cursor: 'pointer',\n          fontSize: 22\n        },\n        title: \"Settings\",\n        children: /*#__PURE__*/_jsxDEV(\"span\", {\n          role: \"img\",\n          \"aria-label\": \"settings\",\n          children: \"\\u2699\\uFE0F\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 21,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 20,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 18,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(SettingsModal, {\n      open: settingsOpen,\n      onClose: () => setSettingsOpen(false),\n      model: model,\n      setModel: setModel\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 24,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        display: 'flex',\n        flex: 1,\n        minHeight: 0\n      },\n      children: [/*#__PURE__*/_jsxDEV(FileExplorer, {\n        onSelectFile: setSelectedFile,\n        projectId: projectId\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 26,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          flex: 1,\n          display: 'flex',\n          flexDirection: 'column'\n        },\n        children: /*#__PURE__*/_jsxDEV(EditorTabs, {\n          selectedFile: selectedFile,\n          projectId: projectId,\n          proposedEdit: proposedEdit,\n          onEditApplied: () => setProposedEdit(null)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 28,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 27,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          width: '30%',\n          borderLeft: '1px solid #ccc',\n          display: 'flex',\n          flexDirection: 'column'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            flex: 1\n          },\n          children: /*#__PURE__*/_jsxDEV(ChatPanel, {\n            model: model,\n            projectId: projectId,\n            onProposeEdit: setProposedEdit\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 37,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 36,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            flex: 1\n          },\n          children: /*#__PURE__*/_jsxDEV(TerminalPanel, {\n            projectId: projectId\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 40,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 39,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 35,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 25,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 17,\n    columnNumber: 5\n  }, this);\n}\n_s(App, \"ye7lMa8ebVqtE0JWA9OVBQegrT8=\");\n_c = App;\nvar _c;\n$RefreshReg$(_c, \"App\");", "map": {"version": 3, "names": ["React", "useState", "FileExplorer", "EditorTabs", "ChatPanel", "TerminalPanel", "ProjectSelector", "SettingsModal", "jsxDEV", "_jsxDEV", "App", "_s", "selectedFile", "setSelectedFile", "projectId", "setProjectId", "settingsOpen", "setSettingsOpen", "model", "setModel", "proposedEdit", "setProposedEdit", "style", "display", "flexDirection", "height", "children", "padding", "borderBottom", "alignItems", "gap", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onClick", "background", "border", "cursor", "fontSize", "title", "role", "open", "onClose", "flex", "minHeight", "onSelectFile", "onEditApplied", "width", "borderLeft", "onProposeEdit", "_c", "$RefreshReg$"], "sources": ["D:/Documents_2/New Coding/CoderBuddy/CoderBuddy/frontend/src/App.js"], "sourcesContent": ["import React, { useState } from 'react';\r\nimport FileExplorer from './components/FileExplorer';\r\nimport EditorTabs from './components/EditorTabs';\r\nimport ChatPanel from './components/ChatPanel';\r\nimport TerminalPanel from './components/TerminalPanel';\r\nimport ProjectSelector from './components/ProjectSelector';\r\nimport SettingsModal from './components/SettingsModal';\r\n\r\nexport default function App() {\r\n  const [selectedFile, setSelectedFile] = useState(null);\r\n  const [projectId, setProjectId] = useState('demo');\r\n  const [settingsOpen, setSettingsOpen] = useState(false);\r\n  const [model, setModel] = useState('openrouter/auto');\r\n  const [proposedEdit, setProposedEdit] = useState(null);\r\n\r\n  return (\r\n    <div style={{ display: 'flex', flexDirection: 'column', height: '100vh' }}>\r\n      <div style={{ padding: 8, borderBottom: '1px solid #ccc', display: 'flex', alignItems: 'center', gap: 16 }}>\r\n        <ProjectSelector projectId={projectId} setProjectId={setProjectId} />\r\n        <button onClick={() => setSettingsOpen(true)} style={{ background: 'none', border: 'none', cursor: 'pointer', fontSize: 22 }} title=\"Settings\">\r\n          <span role=\"img\" aria-label=\"settings\">⚙️</span>\r\n        </button>\r\n      </div>\r\n      <SettingsModal open={settingsOpen} onClose={() => setSettingsOpen(false)} model={model} setModel={setModel} />\r\n      <div style={{ display: 'flex', flex: 1, minHeight: 0 }}>\r\n        <FileExplorer onSelectFile={setSelectedFile} projectId={projectId} />\r\n        <div style={{ flex: 1, display: 'flex', flexDirection: 'column' }}>\r\n          <EditorTabs\r\n            selectedFile={selectedFile}\r\n            projectId={projectId}\r\n            proposedEdit={proposedEdit}\r\n            onEditApplied={() => setProposedEdit(null)}\r\n          />\r\n        </div>\r\n        <div style={{ width: '30%', borderLeft: '1px solid #ccc', display: 'flex', flexDirection: 'column' }}>\r\n          <div style={{ flex: 1 }}>\r\n            <ChatPanel model={model} projectId={projectId} onProposeEdit={setProposedEdit} />\r\n          </div>\r\n          <div style={{ flex: 1 }}>\r\n            <TerminalPanel projectId={projectId} />\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  );\r\n} "], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,OAAOC,YAAY,MAAM,2BAA2B;AACpD,OAAOC,UAAU,MAAM,yBAAyB;AAChD,OAAOC,SAAS,MAAM,wBAAwB;AAC9C,OAAOC,aAAa,MAAM,4BAA4B;AACtD,OAAOC,eAAe,MAAM,8BAA8B;AAC1D,OAAOC,aAAa,MAAM,4BAA4B;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEvD,eAAe,SAASC,GAAGA,CAAA,EAAG;EAAAC,EAAA;EAC5B,MAAM,CAACC,YAAY,EAAEC,eAAe,CAAC,GAAGZ,QAAQ,CAAC,IAAI,CAAC;EACtD,MAAM,CAACa,SAAS,EAAEC,YAAY,CAAC,GAAGd,QAAQ,CAAC,MAAM,CAAC;EAClD,MAAM,CAACe,YAAY,EAAEC,eAAe,CAAC,GAAGhB,QAAQ,CAAC,KAAK,CAAC;EACvD,MAAM,CAACiB,KAAK,EAAEC,QAAQ,CAAC,GAAGlB,QAAQ,CAAC,iBAAiB,CAAC;EACrD,MAAM,CAACmB,YAAY,EAAEC,eAAe,CAAC,GAAGpB,QAAQ,CAAC,IAAI,CAAC;EAEtD,oBACEQ,OAAA;IAAKa,KAAK,EAAE;MAAEC,OAAO,EAAE,MAAM;MAAEC,aAAa,EAAE,QAAQ;MAAEC,MAAM,EAAE;IAAQ,CAAE;IAAAC,QAAA,gBACxEjB,OAAA;MAAKa,KAAK,EAAE;QAAEK,OAAO,EAAE,CAAC;QAAEC,YAAY,EAAE,gBAAgB;QAAEL,OAAO,EAAE,MAAM;QAAEM,UAAU,EAAE,QAAQ;QAAEC,GAAG,EAAE;MAAG,CAAE;MAAAJ,QAAA,gBACzGjB,OAAA,CAACH,eAAe;QAACQ,SAAS,EAAEA,SAAU;QAACC,YAAY,EAAEA;MAAa;QAAAgB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eACrEzB,OAAA;QAAQ0B,OAAO,EAAEA,CAAA,KAAMlB,eAAe,CAAC,IAAI,CAAE;QAACK,KAAK,EAAE;UAAEc,UAAU,EAAE,MAAM;UAAEC,MAAM,EAAE,MAAM;UAAEC,MAAM,EAAE,SAAS;UAAEC,QAAQ,EAAE;QAAG,CAAE;QAACC,KAAK,EAAC,UAAU;QAAAd,QAAA,eAC5IjB,OAAA;UAAMgC,IAAI,EAAC,KAAK;UAAC,cAAW,UAAU;UAAAf,QAAA,EAAC;QAAE;UAAAK,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC1C,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN,CAAC,eACNzB,OAAA,CAACF,aAAa;MAACmC,IAAI,EAAE1B,YAAa;MAAC2B,OAAO,EAAEA,CAAA,KAAM1B,eAAe,CAAC,KAAK,CAAE;MAACC,KAAK,EAAEA,KAAM;MAACC,QAAQ,EAAEA;IAAS;MAAAY,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eAC9GzB,OAAA;MAAKa,KAAK,EAAE;QAAEC,OAAO,EAAE,MAAM;QAAEqB,IAAI,EAAE,CAAC;QAAEC,SAAS,EAAE;MAAE,CAAE;MAAAnB,QAAA,gBACrDjB,OAAA,CAACP,YAAY;QAAC4C,YAAY,EAAEjC,eAAgB;QAACC,SAAS,EAAEA;MAAU;QAAAiB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eACrEzB,OAAA;QAAKa,KAAK,EAAE;UAAEsB,IAAI,EAAE,CAAC;UAAErB,OAAO,EAAE,MAAM;UAAEC,aAAa,EAAE;QAAS,CAAE;QAAAE,QAAA,eAChEjB,OAAA,CAACN,UAAU;UACTS,YAAY,EAAEA,YAAa;UAC3BE,SAAS,EAAEA,SAAU;UACrBM,YAAY,EAAEA,YAAa;UAC3B2B,aAAa,EAAEA,CAAA,KAAM1B,eAAe,CAAC,IAAI;QAAE;UAAAU,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC5C;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC,eACNzB,OAAA;QAAKa,KAAK,EAAE;UAAE0B,KAAK,EAAE,KAAK;UAAEC,UAAU,EAAE,gBAAgB;UAAE1B,OAAO,EAAE,MAAM;UAAEC,aAAa,EAAE;QAAS,CAAE;QAAAE,QAAA,gBACnGjB,OAAA;UAAKa,KAAK,EAAE;YAAEsB,IAAI,EAAE;UAAE,CAAE;UAAAlB,QAAA,eACtBjB,OAAA,CAACL,SAAS;YAACc,KAAK,EAAEA,KAAM;YAACJ,SAAS,EAAEA,SAAU;YAACoC,aAAa,EAAE7B;UAAgB;YAAAU,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC9E,CAAC,eACNzB,OAAA;UAAKa,KAAK,EAAE;YAAEsB,IAAI,EAAE;UAAE,CAAE;UAAAlB,QAAA,eACtBjB,OAAA,CAACJ,aAAa;YAACS,SAAS,EAAEA;UAAU;YAAAiB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACpC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV;AAACvB,EAAA,CArCuBD,GAAG;AAAAyC,EAAA,GAAHzC,GAAG;AAAA,IAAAyC,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}