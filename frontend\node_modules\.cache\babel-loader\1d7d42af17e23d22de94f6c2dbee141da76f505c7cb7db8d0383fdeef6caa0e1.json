{"ast": null, "code": "var _jsxFileName = \"D:\\\\Documents_2\\\\New Coding\\\\CoderBuddy\\\\CoderBuddy\\\\frontend\\\\src\\\\components\\\\SettingsModal.js\";\nimport React from 'react';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst SUGGESTED_MODELS = ['openrouter/auto', 'openrouter/4o', 'openrouter/4o-mini', 'openrouter/gpt-3.5-turbo', 'openrouter/deepseek-coder', 'openrouter/gemini-pro', 'deepseek/deepseek-r1-0528:free'\n// Add more as desired\n];\nfunction sanitizeModelInput(input) {\n  // Allow only alphanumeric, dash, slash, colon, dot, underscore\n  return input.replace(/[^a-zA-Z0-9\\-\\/:._]/g, '');\n}\nexport default function SettingsModal({\n  open,\n  onClose,\n  model,\n  setModel\n}) {\n  if (!open) return null;\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    style: {\n      position: 'fixed',\n      top: 0,\n      left: 0,\n      width: '100vw',\n      height: '100vh',\n      background: 'rgba(0,0,0,0.3)',\n      zIndex: 1000,\n      display: 'flex',\n      alignItems: 'center',\n      justifyContent: 'center'\n    },\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        background: '#fff',\n        borderRadius: 8,\n        padding: 32,\n        minWidth: 350,\n        boxShadow: '0 2px 16px #0002'\n      },\n      children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n        children: \"Settings\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 27,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          marginBottom: 24\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"label\", {\n          style: {\n            fontWeight: 'bold'\n          },\n          children: \"AI Model\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 29,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 29,\n          columnNumber: 65\n        }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n          type: \"text\",\n          value: model,\n          onChange: e => setModel(sanitizeModelInput(e.target.value)),\n          list: \"model-suggestions\",\n          style: {\n            width: '100%',\n            padding: 8,\n            marginTop: 4\n          },\n          placeholder: \"e.g. deepseek/deepseek-r1-0528:free\",\n          autoComplete: \"off\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 30,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"datalist\", {\n          id: \"model-suggestions\",\n          children: SUGGESTED_MODELS.map(m => /*#__PURE__*/_jsxDEV(\"option\", {\n            value: m\n          }, m, false, {\n            fileName: _jsxFileName,\n            lineNumber: 40,\n            columnNumber: 40\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 39,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            color: '#888',\n            fontSize: 13,\n            marginTop: 4\n          },\n          children: [\"Enter any valid OpenRouter model name. See \", /*#__PURE__*/_jsxDEV(\"a\", {\n            href: \"https://openrouter.ai/models\",\n            target: \"_blank\",\n            rel: \"noopener noreferrer\",\n            children: \"openrouter.ai/models\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 43,\n            columnNumber: 56\n          }, this), \" for options.\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 42,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 28,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          marginBottom: 24\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"label\", {\n          style: {\n            fontWeight: 'bold'\n          },\n          children: \"Other Settings\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 47,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            color: '#888',\n            fontSize: 13\n          },\n          children: \"More settings coming soon...\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 48,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 46,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n        onClick: onClose,\n        style: {\n          marginTop: 8,\n          padding: '8px 24px',\n          borderRadius: 4,\n          border: '1px solid #ccc',\n          background: '#f5f5f5',\n          cursor: 'pointer'\n        },\n        children: \"Close\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 50,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 26,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 22,\n    columnNumber: 5\n  }, this);\n}\n_c = SettingsModal;\nvar _c;\n$RefreshReg$(_c, \"SettingsModal\");", "map": {"version": 3, "names": ["React", "jsxDEV", "_jsxDEV", "SUGGESTED_MODELS", "sanitizeModelInput", "input", "replace", "SettingsModal", "open", "onClose", "model", "setModel", "style", "position", "top", "left", "width", "height", "background", "zIndex", "display", "alignItems", "justifyContent", "children", "borderRadius", "padding", "min<PERSON><PERSON><PERSON>", "boxShadow", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "marginBottom", "fontWeight", "type", "value", "onChange", "e", "target", "list", "marginTop", "placeholder", "autoComplete", "id", "map", "m", "color", "fontSize", "href", "rel", "onClick", "border", "cursor", "_c", "$RefreshReg$"], "sources": ["D:/Documents_2/New Coding/CoderBuddy/CoderBuddy/frontend/src/components/SettingsModal.js"], "sourcesContent": ["import React from 'react';\r\n\r\nconst SUGGESTED_MODELS = [\r\n  'openrouter/auto',\r\n  'openrouter/4o',\r\n  'openrouter/4o-mini',\r\n  'openrouter/gpt-3.5-turbo',\r\n  'openrouter/deepseek-coder',\r\n  'openrouter/gemini-pro',\r\n  'deepseek/deepseek-r1-0528:free',\r\n  // Add more as desired\r\n];\r\n\r\nfunction sanitizeModelInput(input) {\r\n  // Allow only alphanumeric, dash, slash, colon, dot, underscore\r\n  return input.replace(/[^a-zA-Z0-9\\-\\/:._]/g, '');\r\n}\r\n\r\nexport default function SettingsModal({ open, onClose, model, setModel }) {\r\n  if (!open) return null;\r\n  return (\r\n    <div style={{\r\n      position: 'fixed', top: 0, left: 0, width: '100vw', height: '100vh',\r\n      background: 'rgba(0,0,0,0.3)', zIndex: 1000, display: 'flex', alignItems: 'center', justifyContent: 'center'\r\n    }}>\r\n      <div style={{ background: '#fff', borderRadius: 8, padding: 32, minWidth: 350, boxShadow: '0 2px 16px #0002' }}>\r\n        <h2>Settings</h2>\r\n        <div style={{ marginBottom: 24 }}>\r\n          <label style={{ fontWeight: 'bold' }}>AI Model</label><br />\r\n          <input\r\n            type=\"text\"\r\n            value={model}\r\n            onChange={e => setModel(sanitizeModelInput(e.target.value))}\r\n            list=\"model-suggestions\"\r\n            style={{ width: '100%', padding: 8, marginTop: 4 }}\r\n            placeholder=\"e.g. deepseek/deepseek-r1-0528:free\"\r\n            autoComplete=\"off\"\r\n          />\r\n          <datalist id=\"model-suggestions\">\r\n            {SUGGESTED_MODELS.map(m => <option key={m} value={m} />)}\r\n          </datalist>\r\n          <div style={{ color: '#888', fontSize: 13, marginTop: 4 }}>\r\n            Enter any valid OpenRouter model name. See <a href=\"https://openrouter.ai/models\" target=\"_blank\" rel=\"noopener noreferrer\">openrouter.ai/models</a> for options.\r\n          </div>\r\n        </div>\r\n        <div style={{ marginBottom: 24 }}>\r\n          <label style={{ fontWeight: 'bold' }}>Other Settings</label>\r\n          <div style={{ color: '#888', fontSize: 13 }}>More settings coming soon...</div>\r\n        </div>\r\n        <button onClick={onClose} style={{ marginTop: 8, padding: '8px 24px', borderRadius: 4, border: '1px solid #ccc', background: '#f5f5f5', cursor: 'pointer' }}>Close</button>\r\n      </div>\r\n    </div>\r\n  );\r\n} "], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE1B,MAAMC,gBAAgB,GAAG,CACvB,iBAAiB,EACjB,eAAe,EACf,oBAAoB,EACpB,0BAA0B,EAC1B,2BAA2B,EAC3B,uBAAuB,EACvB;AACA;AAAA,CACD;AAED,SAASC,kBAAkBA,CAACC,KAAK,EAAE;EACjC;EACA,OAAOA,KAAK,CAACC,OAAO,CAAC,sBAAsB,EAAE,EAAE,CAAC;AAClD;AAEA,eAAe,SAASC,aAAaA,CAAC;EAAEC,IAAI;EAAEC,OAAO;EAAEC,KAAK;EAAEC;AAAS,CAAC,EAAE;EACxE,IAAI,CAACH,IAAI,EAAE,OAAO,IAAI;EACtB,oBACEN,OAAA;IAAKU,KAAK,EAAE;MACVC,QAAQ,EAAE,OAAO;MAAEC,GAAG,EAAE,CAAC;MAAEC,IAAI,EAAE,CAAC;MAAEC,KAAK,EAAE,OAAO;MAAEC,MAAM,EAAE,OAAO;MACnEC,UAAU,EAAE,iBAAiB;MAAEC,MAAM,EAAE,IAAI;MAAEC,OAAO,EAAE,MAAM;MAAEC,UAAU,EAAE,QAAQ;MAAEC,cAAc,EAAE;IACtG,CAAE;IAAAC,QAAA,eACArB,OAAA;MAAKU,KAAK,EAAE;QAAEM,UAAU,EAAE,MAAM;QAAEM,YAAY,EAAE,CAAC;QAAEC,OAAO,EAAE,EAAE;QAAEC,QAAQ,EAAE,GAAG;QAAEC,SAAS,EAAE;MAAmB,CAAE;MAAAJ,QAAA,gBAC7GrB,OAAA;QAAAqB,QAAA,EAAI;MAAQ;QAAAK,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eACjB7B,OAAA;QAAKU,KAAK,EAAE;UAAEoB,YAAY,EAAE;QAAG,CAAE;QAAAT,QAAA,gBAC/BrB,OAAA;UAAOU,KAAK,EAAE;YAAEqB,UAAU,EAAE;UAAO,CAAE;UAAAV,QAAA,EAAC;QAAQ;UAAAK,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC,eAAA7B,OAAA;UAAA0B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,eAC5D7B,OAAA;UACEgC,IAAI,EAAC,MAAM;UACXC,KAAK,EAAEzB,KAAM;UACb0B,QAAQ,EAAEC,CAAC,IAAI1B,QAAQ,CAACP,kBAAkB,CAACiC,CAAC,CAACC,MAAM,CAACH,KAAK,CAAC,CAAE;UAC5DI,IAAI,EAAC,mBAAmB;UACxB3B,KAAK,EAAE;YAAEI,KAAK,EAAE,MAAM;YAAES,OAAO,EAAE,CAAC;YAAEe,SAAS,EAAE;UAAE,CAAE;UACnDC,WAAW,EAAC,qCAAqC;UACjDC,YAAY,EAAC;QAAK;UAAAd,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACnB,CAAC,eACF7B,OAAA;UAAUyC,EAAE,EAAC,mBAAmB;UAAApB,QAAA,EAC7BpB,gBAAgB,CAACyC,GAAG,CAACC,CAAC,iBAAI3C,OAAA;YAAgBiC,KAAK,EAAEU;UAAE,GAAZA,CAAC;YAAAjB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAa,CAAC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAChD,CAAC,eACX7B,OAAA;UAAKU,KAAK,EAAE;YAAEkC,KAAK,EAAE,MAAM;YAAEC,QAAQ,EAAE,EAAE;YAAEP,SAAS,EAAE;UAAE,CAAE;UAAAjB,QAAA,GAAC,6CACd,eAAArB,OAAA;YAAG8C,IAAI,EAAC,8BAA8B;YAACV,MAAM,EAAC,QAAQ;YAACW,GAAG,EAAC,qBAAqB;YAAA1B,QAAA,EAAC;UAAoB;YAAAK,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC,iBACtJ;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eACN7B,OAAA;QAAKU,KAAK,EAAE;UAAEoB,YAAY,EAAE;QAAG,CAAE;QAAAT,QAAA,gBAC/BrB,OAAA;UAAOU,KAAK,EAAE;YAAEqB,UAAU,EAAE;UAAO,CAAE;UAAAV,QAAA,EAAC;QAAc;UAAAK,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC,eAC5D7B,OAAA;UAAKU,KAAK,EAAE;YAAEkC,KAAK,EAAE,MAAM;YAAEC,QAAQ,EAAE;UAAG,CAAE;UAAAxB,QAAA,EAAC;QAA4B;UAAAK,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC5E,CAAC,eACN7B,OAAA;QAAQgD,OAAO,EAAEzC,OAAQ;QAACG,KAAK,EAAE;UAAE4B,SAAS,EAAE,CAAC;UAAEf,OAAO,EAAE,UAAU;UAAED,YAAY,EAAE,CAAC;UAAE2B,MAAM,EAAE,gBAAgB;UAAEjC,UAAU,EAAE,SAAS;UAAEkC,MAAM,EAAE;QAAU,CAAE;QAAA7B,QAAA,EAAC;MAAK;QAAAK,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACxK;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV;AAACsB,EAAA,GAnCuB9C,aAAa;AAAA,IAAA8C,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}