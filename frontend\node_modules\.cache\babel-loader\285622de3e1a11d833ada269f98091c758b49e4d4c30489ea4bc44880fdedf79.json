{"ast": null, "code": "import _t from \"@monaco-editor/loader\";\nimport { memo as Te } from \"react\";\nimport ke, { useState as re, useRef as S, useCallback as oe, useEffect as ne } from \"react\";\nimport Se from \"@monaco-editor/loader\";\nimport { memo as ye } from \"react\";\nimport K from \"react\";\nvar le = {\n    wrapper: {\n      display: \"flex\",\n      position: \"relative\",\n      textAlign: \"initial\"\n    },\n    fullWidth: {\n      width: \"100%\"\n    },\n    hide: {\n      display: \"none\"\n    }\n  },\n  v = le;\nimport me from \"react\";\nvar ae = {\n    container: {\n      display: \"flex\",\n      height: \"100%\",\n      width: \"100%\",\n      justifyContent: \"center\",\n      alignItems: \"center\"\n    }\n  },\n  Y = ae;\nfunction Me({\n  children: e\n}) {\n  return me.createElement(\"div\", {\n    style: Y.container\n  }, e);\n}\nvar Z = Me;\nvar $ = Z;\nfunction Ee({\n  width: e,\n  height: r,\n  isEditorReady: n,\n  loading: t,\n  _ref: a,\n  className: m,\n  wrapperProps: E\n}) {\n  return K.createElement(\"section\", {\n    style: {\n      ...v.wrapper,\n      width: e,\n      height: r\n    },\n    ...E\n  }, !n && K.createElement($, null, t), K.createElement(\"div\", {\n    ref: a,\n    style: {\n      ...v.fullWidth,\n      ...(!n && v.hide)\n    },\n    className: m\n  }));\n}\nvar ee = Ee;\nvar H = ye(ee);\nimport { useEffect as xe } from \"react\";\nfunction Ce(e) {\n  xe(e, []);\n}\nvar k = Ce;\nimport { useEffect as ge, useRef as Re } from \"react\";\nfunction he(e, r, n = !0) {\n  let t = Re(!0);\n  ge(t.current || !n ? () => {\n    t.current = !1;\n  } : e, r);\n}\nvar l = he;\nfunction D() {}\nfunction h(e, r, n, t) {\n  return De(e, t) || be(e, r, n, t);\n}\nfunction De(e, r) {\n  return e.editor.getModel(te(e, r));\n}\nfunction be(e, r, n, t) {\n  return e.editor.createModel(r, n, t ? te(e, t) : void 0);\n}\nfunction te(e, r) {\n  return e.Uri.parse(r);\n}\nfunction Oe({\n  original: e,\n  modified: r,\n  language: n,\n  originalLanguage: t,\n  modifiedLanguage: a,\n  originalModelPath: m,\n  modifiedModelPath: E,\n  keepCurrentOriginalModel: g = !1,\n  keepCurrentModifiedModel: N = !1,\n  theme: x = \"light\",\n  loading: P = \"Loading...\",\n  options: y = {},\n  height: V = \"100%\",\n  width: z = \"100%\",\n  className: F,\n  wrapperProps: j = {},\n  beforeMount: A = D,\n  onMount: q = D\n}) {\n  let [M, O] = re(!1),\n    [T, s] = re(!0),\n    u = S(null),\n    c = S(null),\n    w = S(null),\n    d = S(q),\n    o = S(A),\n    b = S(!1);\n  k(() => {\n    let i = Se.init();\n    return i.then(f => (c.current = f) && s(!1)).catch(f => f?.type !== \"cancelation\" && console.error(\"Monaco initialization: error:\", f)), () => u.current ? I() : i.cancel();\n  }), l(() => {\n    if (u.current && c.current) {\n      let i = u.current.getOriginalEditor(),\n        f = h(c.current, e || \"\", t || n || \"text\", m || \"\");\n      f !== i.getModel() && i.setModel(f);\n    }\n  }, [m], M), l(() => {\n    if (u.current && c.current) {\n      let i = u.current.getModifiedEditor(),\n        f = h(c.current, r || \"\", a || n || \"text\", E || \"\");\n      f !== i.getModel() && i.setModel(f);\n    }\n  }, [E], M), l(() => {\n    let i = u.current.getModifiedEditor();\n    i.getOption(c.current.editor.EditorOption.readOnly) ? i.setValue(r || \"\") : r !== i.getValue() && (i.executeEdits(\"\", [{\n      range: i.getModel().getFullModelRange(),\n      text: r || \"\",\n      forceMoveMarkers: !0\n    }]), i.pushUndoStop());\n  }, [r], M), l(() => {\n    u.current?.getModel()?.original.setValue(e || \"\");\n  }, [e], M), l(() => {\n    let {\n      original: i,\n      modified: f\n    } = u.current.getModel();\n    c.current.editor.setModelLanguage(i, t || n || \"text\"), c.current.editor.setModelLanguage(f, a || n || \"text\");\n  }, [n, t, a], M), l(() => {\n    c.current?.editor.setTheme(x);\n  }, [x], M), l(() => {\n    u.current?.updateOptions(y);\n  }, [y], M);\n  let L = oe(() => {\n      if (!c.current) return;\n      o.current(c.current);\n      let i = h(c.current, e || \"\", t || n || \"text\", m || \"\"),\n        f = h(c.current, r || \"\", a || n || \"text\", E || \"\");\n      u.current?.setModel({\n        original: i,\n        modified: f\n      });\n    }, [n, r, a, e, t, m, E]),\n    U = oe(() => {\n      !b.current && w.current && (u.current = c.current.editor.createDiffEditor(w.current, {\n        automaticLayout: !0,\n        ...y\n      }), L(), c.current?.editor.setTheme(x), O(!0), b.current = !0);\n    }, [y, x, L]);\n  ne(() => {\n    M && d.current(u.current, c.current);\n  }, [M]), ne(() => {\n    !T && !M && U();\n  }, [T, M, U]);\n  function I() {\n    let i = u.current?.getModel();\n    g || i?.original?.dispose(), N || i?.modified?.dispose(), u.current?.dispose();\n  }\n  return ke.createElement(H, {\n    width: z,\n    height: V,\n    isEditorReady: M,\n    loading: P,\n    _ref: w,\n    className: F,\n    wrapperProps: j\n  });\n}\nvar ie = Oe;\nvar we = Te(ie);\nimport { useState as Ie } from \"react\";\nimport ce from \"@monaco-editor/loader\";\nfunction Pe() {\n  let [e, r] = Ie(ce.__getMonacoInstance());\n  return k(() => {\n    let n;\n    return e || (n = ce.init(), n.then(t => {\n      r(t);\n    })), () => n?.cancel();\n  }), e;\n}\nvar Le = Pe;\nimport { memo as ze } from \"react\";\nimport We, { useState as ue, useEffect as W, useRef as C, useCallback as _e } from \"react\";\nimport Ne from \"@monaco-editor/loader\";\nimport { useEffect as Ue, useRef as ve } from \"react\";\nfunction He(e) {\n  let r = ve();\n  return Ue(() => {\n    r.current = e;\n  }, [e]), r.current;\n}\nvar se = He;\nvar _ = new Map();\nfunction Ve({\n  defaultValue: e,\n  defaultLanguage: r,\n  defaultPath: n,\n  value: t,\n  language: a,\n  path: m,\n  theme: E = \"light\",\n  line: g,\n  loading: N = \"Loading...\",\n  options: x = {},\n  overrideServices: P = {},\n  saveViewState: y = !0,\n  keepCurrentModel: V = !1,\n  width: z = \"100%\",\n  height: F = \"100%\",\n  className: j,\n  wrapperProps: A = {},\n  beforeMount: q = D,\n  onMount: M = D,\n  onChange: O,\n  onValidate: T = D\n}) {\n  let [s, u] = ue(!1),\n    [c, w] = ue(!0),\n    d = C(null),\n    o = C(null),\n    b = C(null),\n    L = C(M),\n    U = C(q),\n    I = C(),\n    i = C(t),\n    f = se(m),\n    Q = C(!1),\n    B = C(!1);\n  k(() => {\n    let p = Ne.init();\n    return p.then(R => (d.current = R) && w(!1)).catch(R => R?.type !== \"cancelation\" && console.error(\"Monaco initialization: error:\", R)), () => o.current ? pe() : p.cancel();\n  }), l(() => {\n    let p = h(d.current, e || t || \"\", r || a || \"\", m || n || \"\");\n    p !== o.current?.getModel() && (y && _.set(f, o.current?.saveViewState()), o.current?.setModel(p), y && o.current?.restoreViewState(_.get(m)));\n  }, [m], s), l(() => {\n    o.current?.updateOptions(x);\n  }, [x], s), l(() => {\n    !o.current || t === void 0 || (o.current.getOption(d.current.editor.EditorOption.readOnly) ? o.current.setValue(t) : t !== o.current.getValue() && (B.current = !0, o.current.executeEdits(\"\", [{\n      range: o.current.getModel().getFullModelRange(),\n      text: t,\n      forceMoveMarkers: !0\n    }]), o.current.pushUndoStop(), B.current = !1));\n  }, [t], s), l(() => {\n    let p = o.current?.getModel();\n    p && a && d.current?.editor.setModelLanguage(p, a);\n  }, [a], s), l(() => {\n    g !== void 0 && o.current?.revealLine(g);\n  }, [g], s), l(() => {\n    d.current?.editor.setTheme(E);\n  }, [E], s);\n  let X = _e(() => {\n    if (!(!b.current || !d.current) && !Q.current) {\n      U.current(d.current);\n      let p = m || n,\n        R = h(d.current, t || e || \"\", r || a || \"\", p || \"\");\n      o.current = d.current?.editor.create(b.current, {\n        model: R,\n        automaticLayout: !0,\n        ...x\n      }, P), y && o.current.restoreViewState(_.get(p)), d.current.editor.setTheme(E), g !== void 0 && o.current.revealLine(g), u(!0), Q.current = !0;\n    }\n  }, [e, r, n, t, a, m, x, P, y, E, g]);\n  W(() => {\n    s && L.current(o.current, d.current);\n  }, [s]), W(() => {\n    !c && !s && X();\n  }, [c, s, X]), i.current = t, W(() => {\n    s && O && (I.current?.dispose(), I.current = o.current?.onDidChangeModelContent(p => {\n      B.current || O(o.current.getValue(), p);\n    }));\n  }, [s, O]), W(() => {\n    if (s) {\n      let p = d.current.editor.onDidChangeMarkers(R => {\n        let G = o.current.getModel()?.uri;\n        if (G && R.find(J => J.path === G.path)) {\n          let J = d.current.editor.getModelMarkers({\n            resource: G\n          });\n          T?.(J);\n        }\n      });\n      return () => {\n        p?.dispose();\n      };\n    }\n    return () => {};\n  }, [s, T]);\n  function pe() {\n    I.current?.dispose(), V ? y && _.set(m, o.current.saveViewState()) : o.current.getModel()?.dispose(), o.current.dispose();\n  }\n  return We.createElement(H, {\n    width: z,\n    height: F,\n    isEditorReady: s,\n    loading: N,\n    _ref: b,\n    className: j,\n    wrapperProps: A\n  });\n}\nvar fe = Ve;\nvar de = ze(fe);\nvar Ft = de;\nexport { we as DiffEditor, de as Editor, Ft as default, _t as loader, Le as useMonaco };", "map": {"version": 3, "names": ["_t", "memo", "Te", "ke", "useState", "re", "useRef", "S", "useCallback", "oe", "useEffect", "ne", "Se", "ye", "K", "le", "wrapper", "display", "position", "textAlign", "fullWidth", "width", "hide", "v", "me", "ae", "container", "height", "justifyContent", "alignItems", "Y", "Me", "children", "e", "createElement", "style", "Z", "$", "Ee", "r", "isEditorReady", "n", "loading", "t", "_ref", "a", "className", "m", "wrapperProps", "E", "ref", "ee", "H", "xe", "Ce", "k", "ge", "Re", "he", "current", "l", "D", "h", "De", "be", "editor", "getModel", "te", "createModel", "<PERSON><PERSON>", "parse", "Oe", "original", "modified", "language", "originalLanguage", "modifiedLanguage", "originalModelPath", "modifiedModelPath", "keepCurrentOriginalModel", "g", "keepCurrentModifiedModel", "N", "theme", "x", "P", "options", "y", "V", "z", "F", "j", "beforeMount", "A", "onMount", "q", "M", "O", "T", "s", "u", "c", "w", "d", "o", "b", "i", "init", "then", "f", "catch", "type", "console", "error", "I", "cancel", "getOriginalEditor", "setModel", "getModifiedEditor", "getOption", "EditorOption", "readOnly", "setValue", "getValue", "executeEdits", "range", "getFullModelRange", "text", "forceMoveMarkers", "pushUndoStop", "setModelLanguage", "setTheme", "updateOptions", "L", "U", "createDiffEditor", "automaticLayout", "dispose", "ie", "we", "Ie", "ce", "Pe", "__getMonacoInstance", "Le", "ze", "We", "ue", "W", "C", "_e", "Ne", "Ue", "ve", "He", "se", "_", "Map", "Ve", "defaultValue", "defaultLanguage", "defaultPath", "value", "path", "line", "overrideServices", "saveViewState", "keepCurrentModel", "onChange", "onValidate", "Q", "B", "p", "R", "pe", "set", "restoreViewState", "get", "revealLine", "X", "create", "model", "onDidChangeModelContent", "onDidChangeMarkers", "G", "uri", "find", "J", "getModelMarkers", "resource", "fe", "de", "Ft", "DiffE<PERSON>or", "Editor", "default", "loader", "useMonaco"], "sources": ["D:\\Documents_2\\New Coding\\CoderBuddy\\CoderBuddy\\frontend\\node_modules\\@monaco-editor\\react\\src\\index.ts", "D:\\Documents_2\\New Coding\\CoderBuddy\\CoderBuddy\\frontend\\node_modules\\@monaco-editor\\react\\src\\DiffEditor\\index.ts", "D:\\Documents_2\\New Coding\\CoderBuddy\\CoderBuddy\\frontend\\node_modules\\@monaco-editor\\react\\src\\DiffEditor\\DiffEditor.tsx", "D:\\Documents_2\\New Coding\\CoderBuddy\\CoderBuddy\\frontend\\node_modules\\@monaco-editor\\react\\src\\MonacoContainer\\index.ts", "D:\\Documents_2\\New Coding\\CoderBuddy\\CoderBuddy\\frontend\\node_modules\\@monaco-editor\\react\\src\\MonacoContainer\\MonacoContainer.tsx", "D:\\Documents_2\\New Coding\\CoderBuddy\\CoderBuddy\\frontend\\node_modules\\@monaco-editor\\react\\src\\MonacoContainer\\styles.ts", "D:\\Documents_2\\New Coding\\CoderBuddy\\CoderBuddy\\frontend\\node_modules\\@monaco-editor\\react\\src\\Loading\\Loading.tsx", "D:\\Documents_2\\New Coding\\CoderBuddy\\CoderBuddy\\frontend\\node_modules\\@monaco-editor\\react\\src\\Loading\\styles.ts", "D:\\Documents_2\\New Coding\\CoderBuddy\\CoderBuddy\\frontend\\node_modules\\@monaco-editor\\react\\src\\Loading\\index.ts", "D:\\Documents_2\\New Coding\\CoderBuddy\\CoderBuddy\\frontend\\node_modules\\@monaco-editor\\react\\src\\hooks\\useMount\\index.ts", "D:\\Documents_2\\New Coding\\CoderBuddy\\CoderBuddy\\frontend\\node_modules\\@monaco-editor\\react\\src\\hooks\\useUpdate\\index.ts", "D:\\Documents_2\\New Coding\\CoderBuddy\\CoderBuddy\\frontend\\node_modules\\@monaco-editor\\react\\src\\utils\\index.ts", "D:\\Documents_2\\New Coding\\CoderBuddy\\CoderBuddy\\frontend\\node_modules\\@monaco-editor\\react\\src\\hooks\\useMonaco\\index.ts", "D:\\Documents_2\\New Coding\\CoderBuddy\\CoderBuddy\\frontend\\node_modules\\@monaco-editor\\react\\src\\Editor\\index.ts", "D:\\Documents_2\\New Coding\\CoderBuddy\\CoderBuddy\\frontend\\node_modules\\@monaco-editor\\react\\src\\Editor\\Editor.tsx", "D:\\Documents_2\\New Coding\\CoderBuddy\\CoderBuddy\\frontend\\node_modules\\@monaco-editor\\react\\src\\hooks\\usePrevious\\index.ts"], "sourcesContent": ["import loader from '@monaco-editor/loader';\nexport { loader };\n\nimport DiffEditor from './DiffEditor';\nexport * from './DiffEditor/types';\nexport { DiffEditor };\n\nimport useMonaco from './hooks/useMonaco';\nexport { useMonaco };\n\nimport Editor from './Editor';\nexport * from './Editor/types';\nexport { Editor };\nexport default Editor;\n\n// Monaco\nimport type * as monaco from 'monaco-editor/esm/vs/editor/editor.api';\nexport type Monaco = typeof monaco;\n\n// Default themes\nexport type Theme = 'vs-dark' | 'light';\n", "import { memo } from 'react';\n\nimport DiffEditor from './DiffEditor';\n\nexport * from './types';\n\nexport default memo(DiffEditor);\n", "'use client';\n\nimport React, { useState, useRef, useCallback, useEffect } from 'react';\nimport loader from '@monaco-editor/loader';\n\nimport MonacoContainer from '../MonacoContainer';\nimport useMount from '../hooks/useMount';\nimport useUpdate from '../hooks/useUpdate';\nimport { noop, getOrCreateModel } from '../utils';\nimport { type DiffEditorProps, type MonacoDiffEditor } from './types';\nimport { type Monaco } from '..';\n\nfunction DiffEditor({\n  original,\n  modified,\n  language,\n  originalLanguage,\n  modifiedLanguage,\n  originalModelPath,\n  modifiedModelPath,\n  keepCurrentOriginalModel = false,\n  keepCurrentModifiedModel = false,\n  theme = 'light',\n  loading = 'Loading...',\n  options = {},\n  height = '100%',\n  width = '100%',\n  className,\n  wrapperProps = {},\n  beforeMount = noop,\n  onMount = noop,\n}: DiffEditorProps) {\n  const [isEditorReady, setIsEditorReady] = useState(false);\n  const [isMonacoMounting, setIsMonacoMounting] = useState(true);\n  const editorRef = useRef<MonacoDiffEditor | null>(null);\n  const monacoRef = useRef<Monaco | null>(null);\n  const containerRef = useRef<HTMLDivElement>(null);\n  const onMountRef = useRef(onMount);\n  const beforeMountRef = useRef(beforeMount);\n  const preventCreation = useRef(false);\n\n  useMount(() => {\n    const cancelable = loader.init();\n\n    cancelable\n      .then((monaco) => (monacoRef.current = monaco) && setIsMonacoMounting(false))\n      .catch(\n        (error) =>\n          error?.type !== 'cancelation' && console.error('Monaco initialization: error:', error),\n      );\n\n    return () => (editorRef.current ? disposeEditor() : cancelable.cancel());\n  });\n\n  useUpdate(\n    () => {\n      if (editorRef.current && monacoRef.current) {\n        const originalEditor = editorRef.current.getOriginalEditor();\n        const model = getOrCreateModel(\n          monacoRef.current,\n          original || '',\n          originalLanguage || language || 'text',\n          originalModelPath || '',\n        );\n\n        if (model !== originalEditor.getModel()) {\n          originalEditor.setModel(model);\n        }\n      }\n    },\n    [originalModelPath],\n    isEditorReady,\n  );\n\n  useUpdate(\n    () => {\n      if (editorRef.current && monacoRef.current) {\n        const modifiedEditor = editorRef.current.getModifiedEditor();\n        const model = getOrCreateModel(\n          monacoRef.current,\n          modified || '',\n          modifiedLanguage || language || 'text',\n          modifiedModelPath || '',\n        );\n\n        if (model !== modifiedEditor.getModel()) {\n          modifiedEditor.setModel(model);\n        }\n      }\n    },\n    [modifiedModelPath],\n    isEditorReady,\n  );\n\n  useUpdate(\n    () => {\n      const modifiedEditor = editorRef.current!.getModifiedEditor();\n      if (modifiedEditor.getOption(monacoRef.current!.editor.EditorOption.readOnly)) {\n        modifiedEditor.setValue(modified || '');\n      } else {\n        if (modified !== modifiedEditor.getValue()) {\n          modifiedEditor.executeEdits('', [\n            {\n              range: modifiedEditor.getModel()!.getFullModelRange(),\n              text: modified || '',\n              forceMoveMarkers: true,\n            },\n          ]);\n\n          modifiedEditor.pushUndoStop();\n        }\n      }\n    },\n    [modified],\n    isEditorReady,\n  );\n\n  useUpdate(\n    () => {\n      editorRef.current?.getModel()?.original.setValue(original || '');\n    },\n    [original],\n    isEditorReady,\n  );\n\n  useUpdate(\n    () => {\n      const { original, modified } = editorRef.current!.getModel()!;\n\n      monacoRef.current!.editor.setModelLanguage(original, originalLanguage || language || 'text');\n      monacoRef.current!.editor.setModelLanguage(modified, modifiedLanguage || language || 'text');\n    },\n    [language, originalLanguage, modifiedLanguage],\n    isEditorReady,\n  );\n\n  useUpdate(\n    () => {\n      monacoRef.current?.editor.setTheme(theme);\n    },\n    [theme],\n    isEditorReady,\n  );\n\n  useUpdate(\n    () => {\n      editorRef.current?.updateOptions(options);\n    },\n    [options],\n    isEditorReady,\n  );\n\n  const setModels = useCallback(() => {\n    if (!monacoRef.current) return;\n    beforeMountRef.current(monacoRef.current);\n    const originalModel = getOrCreateModel(\n      monacoRef.current,\n      original || '',\n      originalLanguage || language || 'text',\n      originalModelPath || '',\n    );\n\n    const modifiedModel = getOrCreateModel(\n      monacoRef.current,\n      modified || '',\n      modifiedLanguage || language || 'text',\n      modifiedModelPath || '',\n    );\n\n    editorRef.current?.setModel({\n      original: originalModel,\n      modified: modifiedModel,\n    });\n  }, [\n    language,\n    modified,\n    modifiedLanguage,\n    original,\n    originalLanguage,\n    originalModelPath,\n    modifiedModelPath,\n  ]);\n\n  const createEditor = useCallback(() => {\n    if (!preventCreation.current && containerRef.current) {\n      editorRef.current = monacoRef.current!.editor.createDiffEditor(containerRef.current, {\n        automaticLayout: true,\n        ...options,\n      });\n\n      setModels();\n\n      monacoRef.current?.editor.setTheme(theme);\n\n      setIsEditorReady(true);\n      preventCreation.current = true;\n    }\n  }, [options, theme, setModels]);\n\n  useEffect(() => {\n    if (isEditorReady) {\n      onMountRef.current(editorRef.current!, monacoRef.current!);\n    }\n  }, [isEditorReady]);\n\n  useEffect(() => {\n    !isMonacoMounting && !isEditorReady && createEditor();\n  }, [isMonacoMounting, isEditorReady, createEditor]);\n\n  function disposeEditor() {\n    const models = editorRef.current?.getModel();\n\n    if (!keepCurrentOriginalModel) {\n      models?.original?.dispose();\n    }\n\n    if (!keepCurrentModifiedModel) {\n      models?.modified?.dispose();\n    }\n\n    editorRef.current?.dispose();\n  }\n\n  return (\n    <MonacoContainer\n      width={width}\n      height={height}\n      isEditorReady={isEditorReady}\n      loading={loading}\n      _ref={containerRef}\n      className={className}\n      wrapperProps={wrapperProps}\n    />\n  );\n}\n\nexport default DiffEditor;\n", "import { memo } from 'react';\n\nimport MonacoContainer from './MonacoContainer';\n\nexport default memo(MonacoContainer);\n", "import React from 'react';\n\nimport styles from './styles';\nimport Loading from '../Loading';\nimport { type ContainerProps } from './types';\n\n// ** forwardref render functions do not support proptypes or defaultprops **\n// one of the reasons why we use a separate prop for passing ref instead of using forwardref\n\nfunction MonacoContainer({\n  width,\n  height,\n  isEditorReady,\n  loading,\n  _ref,\n  className,\n  wrapperProps,\n}: ContainerProps) {\n  return (\n    <section style={{ ...styles.wrapper, width, height }} {...wrapperProps}>\n      {!isEditorReady && <Loading>{loading}</Loading>}\n      <div\n        ref={_ref}\n        style={{ ...styles.fullWidth, ...(!isEditorReady && styles.hide) }}\n        className={className}\n      />\n    </section>\n  );\n}\n\nexport default MonacoContainer;\n", "import { type CSSProperties } from 'react';\n\nconst styles: Record<string, CSSProperties> = {\n  wrapper: {\n    display: 'flex',\n    position: 'relative',\n    textAlign: 'initial',\n  },\n  fullWidth: {\n    width: '100%',\n  },\n  hide: {\n    display: 'none',\n  },\n};\n\nexport default styles;\n", "import React, { type PropsWithChildren } from 'react';\n\nimport styles from './styles';\n\nfunction Loading({ children }: PropsWithChildren) {\n  return <div style={styles.container}>{children}</div>;\n}\n\nexport default Loading;\n", "import { type CSSProperties } from 'react';\n\nconst styles: Record<string, CSSProperties> = {\n  container: {\n    display: 'flex',\n    height: '100%',\n    width: '100%',\n    justifyContent: 'center',\n    alignItems: 'center',\n  },\n};\n\nexport default styles;\n", "import Loading from './Loading';\n\nexport default Loading;\n", "import { useEffect, type EffectCallback } from 'react';\n\nfunction useMount(effect: EffectCallback) {\n  useEffect(effect, []);\n}\n\nexport default useMount;\n", "import { useEffect, useRef, type DependencyList, type EffectCallback } from 'react';\n\nfunction useUpdate(effect: EffectCallback, deps: DependencyList, applyChanges = true) {\n  const isInitialMount = useRef(true);\n\n  useEffect(\n    isInitialMount.current || !applyChanges\n      ? () => {\n          isInitialMount.current = false;\n        }\n      : effect,\n    deps,\n  );\n}\n\nexport default useUpdate;\n", "import { type Monaco } from '..';\n\n/**\n * noop is a helper function that does nothing\n * @returns undefined\n */\nfunction noop() {\n  /** no-op */\n}\n\n/**\n * getOrCreateModel is a helper function that will return a model if it exists\n * or create a new model if it does not exist.\n * This is useful for when you want to create a model for a file that may or may not exist yet.\n * @param monaco The monaco instance\n * @param value The value of the model\n * @param language The language of the model\n * @param path The path of the model\n * @returns The model that was found or created\n */\nfunction getOrCreateModel(monaco: Monaco, value: string, language: string, path: string) {\n  return getModel(monaco, path) || createModel(monaco, value, language, path);\n}\n\n/**\n * getModel is a helper function that will return a model if it exists\n * or return undefined if it does not exist.\n * @param monaco The monaco instance\n * @param path The path of the model\n * @returns The model that was found or undefined\n */\nfunction getModel(monaco: Monaco, path: string) {\n  return monaco.editor.getModel(createModelUri(monaco, path));\n}\n\n/**\n * createModel is a helper function that will create a new model\n * @param monaco The monaco instance\n * @param value The value of the model\n * @param language The language of the model\n * @param path The path of the model\n * @returns The model that was created\n */\nfunction createModel(monaco: Monaco, value: string, language?: string, path?: string) {\n  return monaco.editor.createModel(\n    value,\n    language,\n    path ? createModelUri(monaco, path) : undefined,\n  );\n}\n\n/**\n * createModelUri is a helper function that will create a new model uri\n * @param monaco The monaco instance\n * @param path The path of the model\n * @returns The model uri that was created\n */\nfunction createModelUri(monaco: Monaco, path: string) {\n  return monaco.Uri.parse(path);\n}\n\nexport { noop, getOrCreateModel };\n", "import { useState } from 'react';\nimport loader from '@monaco-editor/loader';\n\nimport useMount from '../useMount';\n\nfunction useMonaco() {\n  const [monaco, setMonaco] = useState(loader.__getMonacoInstance());\n\n  useMount(() => {\n    let cancelable: ReturnType<typeof loader.init>;\n\n    if (!monaco) {\n      cancelable = loader.init();\n\n      cancelable.then((monaco) => {\n        setMonaco(monaco);\n      });\n    }\n\n    return () => cancelable?.cancel();\n  });\n\n  return monaco;\n}\n\nexport default useMonaco;\n", "import { memo } from 'react';\n\nimport Editor from './Editor';\n\nexport * from './types';\n\nexport default memo(Editor);\n", "'use client';\n\nimport React, { useState, useEffect, useRef, useCallback } from 'react';\nimport loader from '@monaco-editor/loader';\nimport useMount from '../hooks/useMount';\nimport useUpdate from '../hooks/useUpdate';\nimport usePrevious from '../hooks/usePrevious';\nimport { type IDisposable, type editor } from 'monaco-editor';\nimport { noop, getOrCreateModel } from '../utils';\nimport { type EditorProps } from './types';\nimport { type Monaco } from '..';\nimport MonacoContainer from '../MonacoContainer';\n\nconst viewStates = new Map();\n\nfunction Editor({\n  defaultValue,\n  defaultLanguage,\n  defaultPath,\n  value,\n  language,\n  path,\n  /* === */\n  theme = 'light',\n  line,\n  loading = 'Loading...',\n  options = {},\n  overrideServices = {},\n  saveViewState = true,\n  keepCurrentModel = false,\n  /* === */\n  width = '100%',\n  height = '100%',\n  className,\n  wrapperProps = {},\n  /* === */\n  beforeMount = noop,\n  onMount = noop,\n  onChange,\n  onValidate = noop,\n}: EditorProps) {\n  const [isEditorReady, setIsEditorReady] = useState(false);\n  const [isMonacoMounting, setIsMonacoMounting] = useState(true);\n  const monacoRef = useRef<Monaco | null>(null);\n  const editorRef = useRef<editor.IStandaloneCodeEditor | null>(null);\n  const containerRef = useRef<HTMLDivElement>(null);\n  const onMountRef = useRef(onMount);\n  const beforeMountRef = useRef(beforeMount);\n  const subscriptionRef = useRef<IDisposable>();\n  const valueRef = useRef(value);\n  const previousPath = usePrevious(path);\n  const preventCreation = useRef(false);\n  const preventTriggerChangeEvent = useRef<boolean>(false);\n\n  useMount(() => {\n    const cancelable = loader.init();\n\n    cancelable\n      .then((monaco) => (monacoRef.current = monaco) && setIsMonacoMounting(false))\n      .catch(\n        (error) =>\n          error?.type !== 'cancelation' && console.error('Monaco initialization: error:', error),\n      );\n\n    return () => (editorRef.current ? disposeEditor() : cancelable.cancel());\n  });\n\n  useUpdate(\n    () => {\n      const model = getOrCreateModel(\n        monacoRef.current!,\n        defaultValue || value || '',\n        defaultLanguage || language || '',\n        path || defaultPath || '',\n      );\n\n      if (model !== editorRef.current?.getModel()) {\n        if (saveViewState) viewStates.set(previousPath, editorRef.current?.saveViewState());\n        editorRef.current?.setModel(model);\n        if (saveViewState) editorRef.current?.restoreViewState(viewStates.get(path));\n      }\n    },\n    [path],\n    isEditorReady,\n  );\n\n  useUpdate(\n    () => {\n      editorRef.current?.updateOptions(options);\n    },\n    [options],\n    isEditorReady,\n  );\n\n  useUpdate(\n    () => {\n      if (!editorRef.current || value === undefined) return;\n      if (editorRef.current.getOption(monacoRef.current!.editor.EditorOption.readOnly)) {\n        editorRef.current.setValue(value);\n      } else if (value !== editorRef.current.getValue()) {\n        preventTriggerChangeEvent.current = true;\n        editorRef.current.executeEdits('', [\n          {\n            range: editorRef.current.getModel()!.getFullModelRange(),\n            text: value,\n            forceMoveMarkers: true,\n          },\n        ]);\n\n        editorRef.current.pushUndoStop();\n        preventTriggerChangeEvent.current = false;\n      }\n    },\n    [value],\n    isEditorReady,\n  );\n\n  useUpdate(\n    () => {\n      const model = editorRef.current?.getModel();\n      if (model && language) monacoRef.current?.editor.setModelLanguage(model, language);\n    },\n    [language],\n    isEditorReady,\n  );\n\n  useUpdate(\n    () => {\n      // reason for undefined check: https://github.com/suren-atoyan/monaco-react/pull/188\n      if (line !== undefined) {\n        editorRef.current?.revealLine(line);\n      }\n    },\n    [line],\n    isEditorReady,\n  );\n\n  useUpdate(\n    () => {\n      monacoRef.current?.editor.setTheme(theme);\n    },\n    [theme],\n    isEditorReady,\n  );\n\n  const createEditor = useCallback(() => {\n    if (!containerRef.current || !monacoRef.current) return;\n    if (!preventCreation.current) {\n      beforeMountRef.current(monacoRef.current);\n      const autoCreatedModelPath = path || defaultPath;\n\n      const defaultModel = getOrCreateModel(\n        monacoRef.current,\n        value || defaultValue || '',\n        defaultLanguage || language || '',\n        autoCreatedModelPath || '',\n      );\n\n      editorRef.current = monacoRef.current?.editor.create(\n        containerRef.current,\n        {\n          model: defaultModel,\n          automaticLayout: true,\n          ...options,\n        },\n        overrideServices,\n      );\n\n      saveViewState && editorRef.current.restoreViewState(viewStates.get(autoCreatedModelPath));\n\n      monacoRef.current.editor.setTheme(theme);\n\n      if (line !== undefined) {\n        editorRef.current.revealLine(line);\n      }\n\n      setIsEditorReady(true);\n      preventCreation.current = true;\n    }\n  }, [\n    defaultValue,\n    defaultLanguage,\n    defaultPath,\n    value,\n    language,\n    path,\n    options,\n    overrideServices,\n    saveViewState,\n    theme,\n    line,\n  ]);\n\n  useEffect(() => {\n    if (isEditorReady) {\n      onMountRef.current(editorRef.current!, monacoRef.current!);\n    }\n  }, [isEditorReady]);\n\n  useEffect(() => {\n    !isMonacoMounting && !isEditorReady && createEditor();\n  }, [isMonacoMounting, isEditorReady, createEditor]);\n\n  // subscription\n  // to avoid unnecessary updates (attach - dispose listener) in subscription\n  valueRef.current = value;\n\n  // onChange\n  useEffect(() => {\n    if (isEditorReady && onChange) {\n      subscriptionRef.current?.dispose();\n      subscriptionRef.current = editorRef.current?.onDidChangeModelContent((event) => {\n        if (!preventTriggerChangeEvent.current) {\n          onChange(editorRef.current!.getValue(), event);\n        }\n      });\n    }\n  }, [isEditorReady, onChange]);\n\n  // onValidate\n  useEffect(() => {\n    if (isEditorReady) {\n      const changeMarkersListener = monacoRef.current!.editor.onDidChangeMarkers((uris) => {\n        const editorUri = editorRef.current!.getModel()?.uri;\n\n        if (editorUri) {\n          const currentEditorHasMarkerChanges = uris.find((uri) => uri.path === editorUri.path);\n          if (currentEditorHasMarkerChanges) {\n            const markers = monacoRef.current!.editor.getModelMarkers({\n              resource: editorUri,\n            });\n            onValidate?.(markers);\n          }\n        }\n      });\n\n      return () => {\n        changeMarkersListener?.dispose();\n      };\n    }\n    return () => {\n      // eslint happy\n    };\n  }, [isEditorReady, onValidate]);\n\n  function disposeEditor() {\n    subscriptionRef.current?.dispose();\n\n    if (keepCurrentModel) {\n      saveViewState && viewStates.set(path, editorRef.current!.saveViewState());\n    } else {\n      editorRef.current!.getModel()?.dispose();\n    }\n\n    editorRef.current!.dispose();\n  }\n\n  return (\n    <MonacoContainer\n      width={width}\n      height={height}\n      isEditorReady={isEditorReady}\n      loading={loading}\n      _ref={containerRef}\n      className={className}\n      wrapperProps={wrapperProps}\n    />\n  );\n}\n\nexport default Editor;\n", "import { useEffect, useRef } from 'react';\n\nfunction usePrevious<T>(value: T) {\n  const ref = useRef<T>();\n\n  useEffect(() => {\n    ref.current = value;\n  }, [value]);\n\n  return ref.current;\n}\n\nexport default usePrevious;\n"], "mappings": "AAAA,OAAOA,EAAA,MAAY;ACAnB,SAASC,IAAA,IAAAC,EAAA,QAAY;ACErB,OAAOC,EAAA,IAASC,QAAA,IAAAC,EAAA,EAAUC,MAAA,IAAAC,CAAA,EAAQC,WAAA,IAAAC,EAAA,EAAaC,SAAA,IAAAC,EAAA,QAAiB;AAChE,OAAOC,EAAA,MAAY;ACHnB,SAASX,IAAA,IAAAY,EAAA,QAAY;ACArB,OAAOC,CAAA,MAAW;ACElB,IAAMC,EAAA,GAAwC;IAC5CC,OAAA,EAAS;MACPC,OAAA,EAAS;MACTC,QAAA,EAAU;MACVC,SAAA,EAAW;IACb;IACAC,SAAA,EAAW;MACTC,KAAA,EAAO;IACT;IACAC,IAAA,EAAM;MACJL,OAAA,EAAS;IACX;EACF;EAEOM,CAAA,GAAQR,EAAA;AChBf,OAAOS,EAAA,MAAuC;ACE9C,IAAMC,EAAA,GAAwC;IAC5CC,SAAA,EAAW;MACTT,OAAA,EAAS;MACTU,MAAA,EAAQ;MACRN,KAAA,EAAO;MACPO,cAAA,EAAgB;MAChBC,UAAA,EAAY;IACd;EACF;EAEOC,CAAA,GAAQL,EAAA;ADRf,SAASM,GAAQ;EAAEC,QAAA,EAAAC;AAAS,GAAsB;EAChD,OAAOT,EAAA,CAAAU,aAAA,CAAC;IAAIC,KAAA,EAAOL,CAAA,CAAOJ;EAAA,GAAYO,CAAS,CACjD;AAAA;AAEA,IAAOG,CAAA,GAAQL,EAAA;AENf,IAAOM,CAAA,GAAQD,CAAA;AJOf,SAASE,GAAgB;EACvBjB,KAAA,EAAAY,CAAA;EACAN,MAAA,EAAAY,CAAA;EACAC,aAAA,EAAAC,CAAA;EACAC,OAAA,EAAAC,CAAA;EACAC,IAAA,EAAAC,CAAA;EACAC,SAAA,EAAAC,CAAA;EACAC,YAAA,EAAAC;AACF,GAAmB;EACjB,OACEnC,CAAA,CAAAoB,aAAA,CAAC;IAAQC,KAAA,EAAO;MAAE,GAAGZ,CAAA,CAAOP,OAAA;MAASK,KAAA,EAAAY,CAAA;MAAON,MAAA,EAAAY;IAAO;IAAI,GAAGU;EAAA,GACvD,CAACR,CAAA,IAAiB3B,CAAA,CAAAoB,aAAA,CAACG,CAAA,QAASM,CAAQ,GACrC7B,CAAA,CAAAoB,aAAA,CAAC;IACCgB,GAAA,EAAKL,CAAA;IACLV,KAAA,EAAO;MAAE,GAAGZ,CAAA,CAAOH,SAAA;MAAW,IAAI,CAACqB,CAAA,IAAiBlB,CAAA,CAAOD,IAAM;IAAA;IACjEwB,SAAA,EAAWC;EAAA,CACb,CACF,CAEJ;AAAA;AAEA,IAAOI,EAAA,GAAQb,EAAA;AD1Bf,IAAOc,CAAA,GAAQvC,EAAA,CAAKsC,EAAe;AMJnC,SAASzC,SAAA,IAAA2C,EAAA,QAAsC;AAE/C,SAASC,GAASrB,CAAA,EAAwB;EACxCoB,EAAA,CAAUpB,CAAA,EAAQ,EAAE,CACtB;AAAA;AAEA,IAAOsB,CAAA,GAAQD,EAAA;ACNf,SAAS5C,SAAA,IAAA8C,EAAA,EAAWlD,MAAA,IAAAmD,EAAA,QAAwD;AAE5E,SAASC,GAAUzB,CAAA,EAAwBM,CAAA,EAAsBE,CAAA,GAAe,IAAM;EACpF,IAAME,CAAA,GAAiBc,EAAA,CAAO,EAAI;EAElCD,EAAA,CACEb,CAAA,CAAegB,OAAA,IAAW,CAAClB,CAAA,GACvB,MAAM;IACJE,CAAA,CAAegB,OAAA,GAAU,EAC3B;EAAA,IACA1B,CAAA,EACJM,CACF,CACF;AAAA;AAEA,IAAOqB,CAAA,GAAQF,EAAA;ACTf,SAASG,EAAA,EAAO,CAEhB;AAYA,SAASC,EAAiB7B,CAAA,EAAgBM,CAAA,EAAeE,CAAA,EAAkBE,CAAA,EAAc;EACvF,OAAOoB,EAAA,CAAS9B,CAAA,EAAQU,CAAI,KAAKqB,EAAA,CAAY/B,CAAA,EAAQM,CAAA,EAAOE,CAAA,EAAUE,CAAI,CAC5E;AAAA;AASA,SAASoB,GAAS9B,CAAA,EAAgBM,CAAA,EAAc;EAC9C,OAAON,CAAA,CAAOgC,MAAA,CAAOC,QAAA,CAASC,EAAA,CAAelC,CAAA,EAAQM,CAAI,CAAC,CAC5D;AAAA;AAUA,SAASyB,GAAY/B,CAAA,EAAgBM,CAAA,EAAeE,CAAA,EAAmBE,CAAA,EAAe;EACpF,OAAOV,CAAA,CAAOgC,MAAA,CAAOG,WAAA,CACnB7B,CAAA,EACAE,CAAA,EACAE,CAAA,GAAOwB,EAAA,CAAelC,CAAA,EAAQU,CAAI,IAAI,MACxC,CACF;AAAA;AAQA,SAASwB,GAAelC,CAAA,EAAgBM,CAAA,EAAc;EACpD,OAAON,CAAA,CAAOoC,GAAA,CAAIC,KAAA,CAAM/B,CAAI,CAC9B;AAAA;AT/CA,SAASgC,GAAW;EAClBC,QAAA,EAAAvC,CAAA;EACAwC,QAAA,EAAAlC,CAAA;EACAmC,QAAA,EAAAjC,CAAA;EACAkC,gBAAA,EAAAhC,CAAA;EACAiC,gBAAA,EAAA/B,CAAA;EACAgC,iBAAA,EAAA9B,CAAA;EACA+B,iBAAA,EAAA7B,CAAA;EACA8B,wBAAA,EAAAC,CAAA,GAA2B;EAC3BC,wBAAA,EAAAC,CAAA,GAA2B;EAC3BC,KAAA,EAAAC,CAAA,GAAQ;EACR1C,OAAA,EAAA2C,CAAA,GAAU;EACVC,OAAA,EAAAC,CAAA,GAAU,CAAC;EACX5D,MAAA,EAAA6D,CAAA,GAAS;EACTnE,KAAA,EAAAoE,CAAA,GAAQ;EACR3C,SAAA,EAAA4C,CAAA;EACA1C,YAAA,EAAA2C,CAAA,GAAe,CAAC;EAChBC,WAAA,EAAAC,CAAA,GAAchC,CAAA;EACdiC,OAAA,EAAAC,CAAA,GAAUlC;AACZ,GAAoB;EAClB,IAAM,CAACmC,CAAA,EAAeC,CAAgB,IAAI5F,EAAA,CAAS,EAAK;IAClD,CAAC6F,CAAA,EAAkBC,CAAmB,IAAI9F,EAAA,CAAS,EAAI;IACvD+F,CAAA,GAAY7F,CAAA,CAAgC,IAAI;IAChD8F,CAAA,GAAY9F,CAAA,CAAsB,IAAI;IACtC+F,CAAA,GAAe/F,CAAA,CAAuB,IAAI;IAC1CgG,CAAA,GAAahG,CAAA,CAAOwF,CAAO;IAC3BS,CAAA,GAAiBjG,CAAA,CAAOsF,CAAW;IACnCY,CAAA,GAAkBlG,CAAA,CAAO,EAAK;EAEpCgD,CAAA,CAAS,MAAM;IACb,IAAMmD,CAAA,GAAa9F,EAAA,CAAO+F,IAAA,CAAK;IAE/B,OAAAD,CAAA,CACGE,IAAA,CAAMC,CAAA,KAAYR,CAAA,CAAU1C,OAAA,GAAUkD,CAAA,KAAWV,CAAA,CAAoB,EAAK,CAAC,EAC3EW,KAAA,CACED,CAAA,IACCA,CAAA,EAAOE,IAAA,KAAS,iBAAiBC,OAAA,CAAQC,KAAA,CAAM,iCAAiCJ,CAAK,CACzF,GAEK,MAAOT,CAAA,CAAUzC,OAAA,GAAUuD,CAAA,CAAc,IAAIR,CAAA,CAAWS,MAAA,CAAO,CACxE;EAAA,CAAC,GAEDvD,CAAA,CACE,MAAM;IACJ,IAAIwC,CAAA,CAAUzC,OAAA,IAAW0C,CAAA,CAAU1C,OAAA,EAAS;MAC1C,IAAM+C,CAAA,GAAiBN,CAAA,CAAUzC,OAAA,CAAQyD,iBAAA,CAAkB;QACrDP,CAAA,GAAQ/C,CAAA,CACZuC,CAAA,CAAU1C,OAAA,EACV1B,CAAA,IAAY,IACZU,CAAA,IAAoBF,CAAA,IAAY,QAChCM,CAAA,IAAqB,EACvB;MAEI8D,CAAA,KAAUH,CAAA,CAAexC,QAAA,CAAS,KACpCwC,CAAA,CAAeW,QAAA,CAASR,CAAK;IAAA;EAGnC,GACA,CAAC9D,CAAiB,GAClBiD,CACF,GAEApC,CAAA,CACE,MAAM;IACJ,IAAIwC,CAAA,CAAUzC,OAAA,IAAW0C,CAAA,CAAU1C,OAAA,EAAS;MAC1C,IAAM+C,CAAA,GAAiBN,CAAA,CAAUzC,OAAA,CAAQ2D,iBAAA,CAAkB;QACrDT,CAAA,GAAQ/C,CAAA,CACZuC,CAAA,CAAU1C,OAAA,EACVpB,CAAA,IAAY,IACZM,CAAA,IAAoBJ,CAAA,IAAY,QAChCQ,CAAA,IAAqB,EACvB;MAEI4D,CAAA,KAAUH,CAAA,CAAexC,QAAA,CAAS,KACpCwC,CAAA,CAAeW,QAAA,CAASR,CAAK;IAAA;EAGnC,GACA,CAAC5D,CAAiB,GAClB+C,CACF,GAEApC,CAAA,CACE,MAAM;IACJ,IAAM8C,CAAA,GAAiBN,CAAA,CAAUzC,OAAA,CAAS2D,iBAAA,CAAkB;IACxDZ,CAAA,CAAea,SAAA,CAAUlB,CAAA,CAAU1C,OAAA,CAASM,MAAA,CAAOuD,YAAA,CAAaC,QAAQ,IAC1Ef,CAAA,CAAegB,QAAA,CAASnF,CAAA,IAAY,EAAE,IAElCA,CAAA,KAAamE,CAAA,CAAeiB,QAAA,CAAS,MACvCjB,CAAA,CAAekB,YAAA,CAAa,IAAI,CAC9B;MACEC,KAAA,EAAOnB,CAAA,CAAexC,QAAA,CAAS,EAAG4D,iBAAA,CAAkB;MACpDC,IAAA,EAAMxF,CAAA,IAAY;MAClByF,gBAAA,EAAkB;IACpB,CACF,CAAC,GAEDtB,CAAA,CAAeuB,YAAA,CAAa,EAGlC;EAAA,GACA,CAAC1F,CAAQ,GACTyD,CACF,GAEApC,CAAA,CACE,MAAM;IACJwC,CAAA,CAAUzC,OAAA,EAASO,QAAA,CAAS,GAAGM,QAAA,CAASkD,QAAA,CAASzF,CAAA,IAAY,EAAE,CACjE;EAAA,GACA,CAACA,CAAQ,GACT+D,CACF,GAEApC,CAAA,CACE,MAAM;IACJ,IAAM;MAAEY,QAAA,EAAAkC,CAAA;MAAUjC,QAAA,EAAAoC;IAAS,IAAIT,CAAA,CAAUzC,OAAA,CAASO,QAAA,CAAS;IAE3DmC,CAAA,CAAU1C,OAAA,CAASM,MAAA,CAAOiE,gBAAA,CAAiBxB,CAAA,EAAU/D,CAAA,IAAoBF,CAAA,IAAY,MAAM,GAC3F4D,CAAA,CAAU1C,OAAA,CAASM,MAAA,CAAOiE,gBAAA,CAAiBrB,CAAA,EAAUhE,CAAA,IAAoBJ,CAAA,IAAY,MAAM,CAC7F;EAAA,GACA,CAACA,CAAA,EAAUE,CAAA,EAAkBE,CAAgB,GAC7CmD,CACF,GAEApC,CAAA,CACE,MAAM;IACJyC,CAAA,CAAU1C,OAAA,EAASM,MAAA,CAAOkE,QAAA,CAAS/C,CAAK,CAC1C;EAAA,GACA,CAACA,CAAK,GACNY,CACF,GAEApC,CAAA,CACE,MAAM;IACJwC,CAAA,CAAUzC,OAAA,EAASyE,aAAA,CAAc7C,CAAO,CAC1C;EAAA,GACA,CAACA,CAAO,GACRS,CACF;EAEA,IAAMqC,CAAA,GAAY5H,EAAA,CAAY,MAAM;MAClC,IAAI,CAAC4F,CAAA,CAAU1C,OAAA,EAAS;MACxB6C,CAAA,CAAe7C,OAAA,CAAQ0C,CAAA,CAAU1C,OAAO;MACxC,IAAM+C,CAAA,GAAgB5C,CAAA,CACpBuC,CAAA,CAAU1C,OAAA,EACV1B,CAAA,IAAY,IACZU,CAAA,IAAoBF,CAAA,IAAY,QAChCM,CAAA,IAAqB,EACvB;QAEM8D,CAAA,GAAgB/C,CAAA,CACpBuC,CAAA,CAAU1C,OAAA,EACVpB,CAAA,IAAY,IACZM,CAAA,IAAoBJ,CAAA,IAAY,QAChCQ,CAAA,IAAqB,EACvB;MAEAmD,CAAA,CAAUzC,OAAA,EAAS0D,QAAA,CAAS;QAC1B7C,QAAA,EAAUkC,CAAA;QACVjC,QAAA,EAAUoC;MACZ,CAAC,CACH;IAAA,GAAG,CACDpE,CAAA,EACAF,CAAA,EACAM,CAAA,EACAZ,CAAA,EACAU,CAAA,EACAI,CAAA,EACAE,CACF,CAAC;IAEKqF,CAAA,GAAe7H,EAAA,CAAY,MAAM;MACjC,CAACgG,CAAA,CAAgB9C,OAAA,IAAW2C,CAAA,CAAa3C,OAAA,KAC3CyC,CAAA,CAAUzC,OAAA,GAAU0C,CAAA,CAAU1C,OAAA,CAASM,MAAA,CAAOsE,gBAAA,CAAiBjC,CAAA,CAAa3C,OAAA,EAAS;QACnF6E,eAAA,EAAiB;QACjB,GAAGjD;MACL,CAAC,GAED8C,CAAA,CAAU,GAEVhC,CAAA,CAAU1C,OAAA,EAASM,MAAA,CAAOkE,QAAA,CAAS/C,CAAK,GAExCa,CAAA,CAAiB,EAAI,GACrBQ,CAAA,CAAgB9C,OAAA,GAAU,GAE9B;IAAA,GAAG,CAAC4B,CAAA,EAASH,CAAA,EAAOiD,CAAS,CAAC;EAE9B1H,EAAA,CAAU,MAAM;IACVqF,CAAA,IACFO,CAAA,CAAW5C,OAAA,CAAQyC,CAAA,CAAUzC,OAAA,EAAU0C,CAAA,CAAU1C,OAAQ,CAE7D;EAAA,GAAG,CAACqC,CAAa,CAAC,GAElBrF,EAAA,CAAU,MAAM;IACd,CAACuF,CAAA,IAAoB,CAACF,CAAA,IAAiBsC,CAAA,CAAa,CACtD;EAAA,GAAG,CAACpC,CAAA,EAAkBF,CAAA,EAAesC,CAAY,CAAC;EAElD,SAASpB,EAAA,EAAgB;IACvB,IAAMR,CAAA,GAASN,CAAA,CAAUzC,OAAA,EAASO,QAAA,CAAS;IAEtCc,CAAA,IACH0B,CAAA,EAAQlC,QAAA,EAAUiE,OAAA,CAAQ,GAGvBvD,CAAA,IACHwB,CAAA,EAAQjC,QAAA,EAAUgE,OAAA,CAAQ,GAG5BrC,CAAA,CAAUzC,OAAA,EAAS8E,OAAA,CAAQ,CAC7B;EAAA;EAEA,OACEtI,EAAA,CAAA+B,aAAA,CAACkB,CAAA;IACC/B,KAAA,EAAOoE,CAAA;IACP9D,MAAA,EAAQ6D,CAAA;IACRhD,aAAA,EAAewD,CAAA;IACftD,OAAA,EAAS2C,CAAA;IACTzC,IAAA,EAAM0D,CAAA;IACNxD,SAAA,EAAW4C,CAAA;IACX1C,YAAA,EAAc2C;EAAA,CAChB,CAEJ;AAAA;AAEA,IAAO+C,EAAA,GAAQnE,EAAA;ADtOf,IAAOoE,EAAA,GAAQzI,EAAA,CAAKwI,EAAU;AWN9B,SAAStI,QAAA,IAAAwI,EAAA,QAAgB;AACzB,OAAOC,EAAA,MAAY;AAInB,SAASC,GAAA,EAAY;EACnB,IAAM,CAAC7G,CAAA,EAAQM,CAAS,IAAIqG,EAAA,CAASC,EAAA,CAAOE,mBAAA,CAAoB,CAAC;EAEjE,OAAAxF,CAAA,CAAS,MAAM;IACb,IAAId,CAAA;IAEJ,OAAKR,CAAA,KACHQ,CAAA,GAAaoG,EAAA,CAAOlC,IAAA,CAAK,GAEzBlE,CAAA,CAAWmE,IAAA,CAAMjE,CAAA,IAAW;MAC1BJ,CAAA,CAAUI,CAAM,CAClB;IAAA,CAAC,IAGI,MAAMF,CAAA,EAAY0E,MAAA,CAAO,CAClC;EAAA,CAAC,GAEMlF,CACT;AAAA;AAEA,IAAO+G,EAAA,GAAQF,EAAA;ACzBf,SAAS7I,IAAA,IAAAgJ,EAAA,QAAY;ACErB,OAAOC,EAAA,IAAS9I,QAAA,IAAA+I,EAAA,EAAUzI,SAAA,IAAA0I,CAAA,EAAW9I,MAAA,IAAA+I,CAAA,EAAQ7I,WAAA,IAAA8I,EAAA,QAAmB;AAChE,OAAOC,EAAA,MAAY;ACHnB,SAAS7I,SAAA,IAAA8I,EAAA,EAAWlJ,MAAA,IAAAmJ,EAAA,QAAc;AAElC,SAASC,GAAezH,CAAA,EAAU;EAChC,IAAMM,CAAA,GAAMkH,EAAA,CAAU;EAEtB,OAAAD,EAAA,CAAU,MAAM;IACdjH,CAAA,CAAIoB,OAAA,GAAU1B,CAChB;EAAA,GAAG,CAACA,CAAK,CAAC,GAEHM,CAAA,CAAIoB,OACb;AAAA;AAEA,IAAOgG,EAAA,GAAQD,EAAA;ADCf,IAAME,CAAA,GAAa,IAAIC,GAAA;AAEvB,SAASC,GAAO;EACdC,YAAA,EAAA9H,CAAA;EACA+H,eAAA,EAAAzH,CAAA;EACA0H,WAAA,EAAAxH,CAAA;EACAyH,KAAA,EAAAvH,CAAA;EACA+B,QAAA,EAAA7B,CAAA;EACAsH,IAAA,EAAApH,CAAA;EAEAoC,KAAA,EAAAlC,CAAA,GAAQ;EACRmH,IAAA,EAAApF,CAAA;EACAtC,OAAA,EAAAwC,CAAA,GAAU;EACVI,OAAA,EAAAF,CAAA,GAAU,CAAC;EACXiF,gBAAA,EAAAhF,CAAA,GAAmB,CAAC;EACpBiF,aAAA,EAAA/E,CAAA,GAAgB;EAChBgF,gBAAA,EAAA/E,CAAA,GAAmB;EAEnBnE,KAAA,EAAAoE,CAAA,GAAQ;EACR9D,MAAA,EAAA+D,CAAA,GAAS;EACT5C,SAAA,EAAA6C,CAAA;EACA3C,YAAA,EAAA6C,CAAA,GAAe,CAAC;EAEhBD,WAAA,EAAAG,CAAA,GAAclC,CAAA;EACdiC,OAAA,EAAAE,CAAA,GAAUnC,CAAA;EACV2G,QAAA,EAAAvE,CAAA;EACAwE,UAAA,EAAAvE,CAAA,GAAarC;AACf,GAAgB;EACd,IAAM,CAACsC,CAAA,EAAeC,CAAgB,IAAI+C,EAAA,CAAS,EAAK;IAClD,CAAC9C,CAAA,EAAkBC,CAAmB,IAAI6C,EAAA,CAAS,EAAI;IACvD5C,CAAA,GAAY8C,CAAA,CAAsB,IAAI;IACtC7C,CAAA,GAAY6C,CAAA,CAA4C,IAAI;IAC5D5C,CAAA,GAAe4C,CAAA,CAAuB,IAAI;IAC1ChB,CAAA,GAAagB,CAAA,CAAOrD,CAAO;IAC3BsC,CAAA,GAAiBe,CAAA,CAAOtD,CAAW;IACnCmB,CAAA,GAAkBmC,CAAA,CAAoB;IACtC3C,CAAA,GAAW2C,CAAA,CAAO1G,CAAK;IACvBkE,CAAA,GAAe8C,EAAA,CAAY5G,CAAI;IAC/B2H,CAAA,GAAkBrB,CAAA,CAAO,EAAK;IAC9BsB,CAAA,GAA4BtB,CAAA,CAAgB,EAAK;EAEvD9F,CAAA,CAAS,MAAM;IACb,IAAMqH,CAAA,GAAarB,EAAA,CAAO5C,IAAA,CAAK;IAE/B,OAAAiE,CAAA,CACGhE,IAAA,CAAMiE,CAAA,KAAYtE,CAAA,CAAU5C,OAAA,GAAUkH,CAAA,KAAWvE,CAAA,CAAoB,EAAK,CAAC,EAC3EQ,KAAA,CACE+D,CAAA,IACCA,CAAA,EAAO9D,IAAA,KAAS,iBAAiBC,OAAA,CAAQC,KAAA,CAAM,iCAAiC4D,CAAK,CACzF,GAEK,MAAOrE,CAAA,CAAU7C,OAAA,GAAUmH,EAAA,CAAc,IAAIF,CAAA,CAAWzD,MAAA,CAAO,CACxE;EAAA,CAAC,GAEDvD,CAAA,CACE,MAAM;IACJ,IAAMgH,CAAA,GAAQ9G,CAAA,CACZyC,CAAA,CAAU5C,OAAA,EACV1B,CAAA,IAAgBU,CAAA,IAAS,IACzBJ,CAAA,IAAmBM,CAAA,IAAY,IAC/BE,CAAA,IAAQN,CAAA,IAAe,EACzB;IAEImI,CAAA,KAAUpE,CAAA,CAAU7C,OAAA,EAASO,QAAA,CAAS,MACpCqB,CAAA,IAAeqE,CAAA,CAAWmB,GAAA,CAAIlE,CAAA,EAAcL,CAAA,CAAU7C,OAAA,EAAS2G,aAAA,CAAc,CAAC,GAClF9D,CAAA,CAAU7C,OAAA,EAAS0D,QAAA,CAASuD,CAAK,GAC7BrF,CAAA,IAAeiB,CAAA,CAAU7C,OAAA,EAASqH,gBAAA,CAAiBpB,CAAA,CAAWqB,GAAA,CAAIlI,CAAI,CAAC,EAE/E;EAAA,GACA,CAACA,CAAI,GACLoD,CACF,GAEAvC,CAAA,CACE,MAAM;IACJ4C,CAAA,CAAU7C,OAAA,EAASyE,aAAA,CAAchD,CAAO,CAC1C;EAAA,GACA,CAACA,CAAO,GACRe,CACF,GAEAvC,CAAA,CACE,MAAM;IACA,CAAC4C,CAAA,CAAU7C,OAAA,IAAWhB,CAAA,KAAU,WAChC6D,CAAA,CAAU7C,OAAA,CAAQ4D,SAAA,CAAUhB,CAAA,CAAU5C,OAAA,CAASM,MAAA,CAAOuD,YAAA,CAAaC,QAAQ,IAC7EjB,CAAA,CAAU7C,OAAA,CAAQ+D,QAAA,CAAS/E,CAAK,IACvBA,CAAA,KAAU6D,CAAA,CAAU7C,OAAA,CAAQgE,QAAA,CAAS,MAC9CgD,CAAA,CAA0BhH,OAAA,GAAU,IACpC6C,CAAA,CAAU7C,OAAA,CAAQiE,YAAA,CAAa,IAAI,CACjC;MACEC,KAAA,EAAOrB,CAAA,CAAU7C,OAAA,CAAQO,QAAA,CAAS,EAAG4D,iBAAA,CAAkB;MACvDC,IAAA,EAAMpF,CAAA;MACNqF,gBAAA,EAAkB;IACpB,CACF,CAAC,GAEDxB,CAAA,CAAU7C,OAAA,CAAQsE,YAAA,CAAa,GAC/B0C,CAAA,CAA0BhH,OAAA,GAAU,IAExC;EAAA,GACA,CAAChB,CAAK,GACNwD,CACF,GAEAvC,CAAA,CACE,MAAM;IACJ,IAAMgH,CAAA,GAAQpE,CAAA,CAAU7C,OAAA,EAASO,QAAA,CAAS;IACtC0G,CAAA,IAAS/H,CAAA,IAAU0D,CAAA,CAAU5C,OAAA,EAASM,MAAA,CAAOiE,gBAAA,CAAiB0C,CAAA,EAAO/H,CAAQ,CACnF;EAAA,GACA,CAACA,CAAQ,GACTsD,CACF,GAEAvC,CAAA,CACE,MAAM;IAEAoB,CAAA,KAAS,UACXwB,CAAA,CAAU7C,OAAA,EAASuH,UAAA,CAAWlG,CAAI,CAEtC;EAAA,GACA,CAACA,CAAI,GACLmB,CACF,GAEAvC,CAAA,CACE,MAAM;IACJ2C,CAAA,CAAU5C,OAAA,EAASM,MAAA,CAAOkE,QAAA,CAASlF,CAAK,CAC1C;EAAA,GACA,CAACA,CAAK,GACNkD,CACF;EAEA,IAAMgF,CAAA,GAAe7B,EAAA,CAAY,MAAM;IACrC,IAAI,GAAC7C,CAAA,CAAa9C,OAAA,IAAW,CAAC4C,CAAA,CAAU5C,OAAA,KACpC,CAAC+G,CAAA,CAAgB/G,OAAA,EAAS;MAC5B2E,CAAA,CAAe3E,OAAA,CAAQ4C,CAAA,CAAU5C,OAAO;MACxC,IAAMiH,CAAA,GAAuB7H,CAAA,IAAQN,CAAA;QAE/BoI,CAAA,GAAe/G,CAAA,CACnByC,CAAA,CAAU5C,OAAA,EACVhB,CAAA,IAASV,CAAA,IAAgB,IACzBM,CAAA,IAAmBM,CAAA,IAAY,IAC/B+H,CAAA,IAAwB,EAC1B;MAEApE,CAAA,CAAU7C,OAAA,GAAU4C,CAAA,CAAU5C,OAAA,EAASM,MAAA,CAAOmH,MAAA,CAC5C3E,CAAA,CAAa9C,OAAA,EACb;QACE0H,KAAA,EAAOR,CAAA;QACPrC,eAAA,EAAiB;QACjB,GAAGpD;MACL,GACAC,CACF,GAEAE,CAAA,IAAiBiB,CAAA,CAAU7C,OAAA,CAAQqH,gBAAA,CAAiBpB,CAAA,CAAWqB,GAAA,CAAIL,CAAoB,CAAC,GAExFrE,CAAA,CAAU5C,OAAA,CAAQM,MAAA,CAAOkE,QAAA,CAASlF,CAAK,GAEnC+B,CAAA,KAAS,UACXwB,CAAA,CAAU7C,OAAA,CAAQuH,UAAA,CAAWlG,CAAI,GAGnCoB,CAAA,CAAiB,EAAI,GACrBsE,CAAA,CAAgB/G,OAAA,GAAU;IAAA;EAE9B,GAAG,CACD1B,CAAA,EACAM,CAAA,EACAE,CAAA,EACAE,CAAA,EACAE,CAAA,EACAE,CAAA,EACAqC,CAAA,EACAC,CAAA,EACAE,CAAA,EACAtC,CAAA,EACA+B,CACF,CAAC;EAEDoE,CAAA,CAAU,MAAM;IACVjD,CAAA,IACFkC,CAAA,CAAW1E,OAAA,CAAQ6C,CAAA,CAAU7C,OAAA,EAAU4C,CAAA,CAAU5C,OAAQ,CAE7D;EAAA,GAAG,CAACwC,CAAa,CAAC,GAElBiD,CAAA,CAAU,MAAM;IACd,CAAC/C,CAAA,IAAoB,CAACF,CAAA,IAAiBgF,CAAA,CAAa,CACtD;EAAA,GAAG,CAAC9E,CAAA,EAAkBF,CAAA,EAAegF,CAAY,CAAC,GAIlDzE,CAAA,CAAS/C,OAAA,GAAUhB,CAAA,EAGnByG,CAAA,CAAU,MAAM;IACVjD,CAAA,IAAiBF,CAAA,KACnBiB,CAAA,CAAgBvD,OAAA,EAAS8E,OAAA,CAAQ,GACjCvB,CAAA,CAAgBvD,OAAA,GAAU6C,CAAA,CAAU7C,OAAA,EAAS2H,uBAAA,CAAyBV,CAAA,IAAU;MACzED,CAAA,CAA0BhH,OAAA,IAC7BsC,CAAA,CAASO,CAAA,CAAU7C,OAAA,CAASgE,QAAA,CAAS,GAAGiD,CAAK,CAEjD;IAAA,CAAC,EAEL;EAAA,GAAG,CAACzE,CAAA,EAAeF,CAAQ,CAAC,GAG5BmD,CAAA,CAAU,MAAM;IACd,IAAIjD,CAAA,EAAe;MACjB,IAAMyE,CAAA,GAAwBrE,CAAA,CAAU5C,OAAA,CAASM,MAAA,CAAOsH,kBAAA,CAAoBV,CAAA,IAAS;QACnF,IAAMW,CAAA,GAAYhF,CAAA,CAAU7C,OAAA,CAASO,QAAA,CAAS,GAAGuH,GAAA;QAEjD,IAAID,CAAA,IACoCX,CAAA,CAAKa,IAAA,CAAMC,CAAA,IAAQA,CAAA,CAAIxB,IAAA,KAASqB,CAAA,CAAUrB,IAAI,GACjD;UACjC,IAAMwB,CAAA,GAAUpF,CAAA,CAAU5C,OAAA,CAASM,MAAA,CAAO2H,eAAA,CAAgB;YACxDC,QAAA,EAAUL;UACZ,CAAC;UACDtF,CAAA,GAAayF,CAAO;QAAA;MAG1B,CAAC;MAED,OAAO,MAAM;QACXf,CAAA,EAAuBnC,OAAA,CAAQ,CACjC;MAAA;IAAA;IAEF,OAAO,MAAM,CAEb,CACF;EAAA,GAAG,CAACtC,CAAA,EAAeD,CAAU,CAAC;EAE9B,SAAS4E,GAAA,EAAgB;IACvB5D,CAAA,CAAgBvD,OAAA,EAAS8E,OAAA,CAAQ,GAE7BjD,CAAA,GACFD,CAAA,IAAiBqE,CAAA,CAAWmB,GAAA,CAAIhI,CAAA,EAAMyD,CAAA,CAAU7C,OAAA,CAAS2G,aAAA,CAAc,CAAC,IAExE9D,CAAA,CAAU7C,OAAA,CAASO,QAAA,CAAS,GAAGuE,OAAA,CAAQ,GAGzCjC,CAAA,CAAU7C,OAAA,CAAS8E,OAAA,CAAQ,CAC7B;EAAA;EAEA,OACES,EAAA,CAAAhH,aAAA,CAACkB,CAAA;IACC/B,KAAA,EAAOoE,CAAA;IACP9D,MAAA,EAAQ+D,CAAA;IACRlD,aAAA,EAAe2D,CAAA;IACfzD,OAAA,EAASwC,CAAA;IACTtC,IAAA,EAAM6D,CAAA;IACN3D,SAAA,EAAW6C,CAAA;IACX3C,YAAA,EAAc6C;EAAA,CAChB,CAEJ;AAAA;AAEA,IAAOiG,EAAA,GAAQhC,EAAA;ADxQf,IAAOiC,EAAA,GAAQ9C,EAAA,CAAK6C,EAAM;AbO1B,IAAOE,EAAA,GAAQD,EAAA;AAAA,SAAApD,EAAA,IAAAsD,UAAA,EAAAF,EAAA,IAAAG,MAAA,EAAAF,EAAA,IAAAG,OAAA,EAAAnM,EAAA,IAAAoM,MAAA,EAAApD,EAAA,IAAAqD,SAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}