{"ast": null, "code": "import createEmotion from \"@emotion/css/create-instance\";\nexport default (styleOverride, useDarkTheme = false, nonce = \"\") => {\n  const {\n    variables: overrideVariables = {},\n    ...styles\n  } = styleOverride;\n  const themeVariables = {\n    light: {\n      ...{\n        diffViewerBackground: \"#fff\",\n        diffViewerColor: \"#212529\",\n        addedBackground: \"#e6ffed\",\n        addedColor: \"#24292e\",\n        removedBackground: \"#ffeef0\",\n        removedColor: \"#24292e\",\n        changedBackground: \"#fffbdd\",\n        wordAddedBackground: \"#acf2bd\",\n        wordRemovedBackground: \"#fdb8c0\",\n        addedGutterBackground: \"#cdffd8\",\n        removedGutterBackground: \"#ffdce0\",\n        gutterBackground: \"#f7f7f7\",\n        gutterBackgroundDark: \"#f3f1f1\",\n        highlightBackground: \"#fffbdd\",\n        highlightGutterBackground: \"#fff5b1\",\n        codeFoldGutterBackground: \"#dbedff\",\n        codeFoldBackground: \"#f1f8ff\",\n        emptyLineBackground: \"#fafbfc\",\n        gutterColor: \"#212529\",\n        addedGutterColor: \"#212529\",\n        removedGutterColor: \"#212529\",\n        codeFoldContentColor: \"#212529\",\n        diffViewerTitleBackground: \"#fafbfc\",\n        diffViewerTitleColor: \"#212529\",\n        diffViewerTitleBorderColor: \"#eee\"\n      },\n      ...(overrideVariables.light || {})\n    },\n    dark: {\n      ...{\n        diffViewerBackground: \"#2e303c\",\n        diffViewerColor: \"#FFF\",\n        addedBackground: \"#044B53\",\n        addedColor: \"white\",\n        removedBackground: \"#632F34\",\n        removedColor: \"white\",\n        changedBackground: \"#3e302c\",\n        wordAddedBackground: \"#055d67\",\n        wordRemovedBackground: \"#7d383f\",\n        addedGutterBackground: \"#034148\",\n        removedGutterBackground: \"#632b30\",\n        gutterBackground: \"#2c2f3a\",\n        gutterBackgroundDark: \"#262933\",\n        highlightBackground: \"#2a3967\",\n        highlightGutterBackground: \"#2d4077\",\n        codeFoldGutterBackground: \"#262831\",\n        codeFoldBackground: \"#262831\",\n        emptyLineBackground: \"#363946\",\n        gutterColor: \"#666c87\",\n        addedGutterColor: \"#8c8c8c\",\n        removedGutterColor: \"#8c8c8c\",\n        codeFoldContentColor: \"#656a8b\",\n        diffViewerTitleBackground: \"#2f323e\",\n        diffViewerTitleColor: \"#757a9b\",\n        diffViewerTitleBorderColor: \"#353846\"\n      },\n      ...(overrideVariables.dark || {})\n    }\n  };\n  const variables = useDarkTheme ? themeVariables.dark : themeVariables.light;\n  const {\n    css,\n    cx\n  } = createEmotion({\n    key: \"react-diff\",\n    nonce\n  });\n  const content = css({\n    width: \"auto\",\n    label: \"content\"\n  });\n  const splitView = css({\n    label: \"split-view\"\n  });\n  const summary = css({\n    background: variables.diffViewerTitleBackground,\n    color: variables.diffViewerTitleColor,\n    padding: \"0.5em 1em\",\n    display: \"flex\",\n    alignItems: \"center\",\n    gap: \"0.5em\",\n    fontFamily: \"monospace\",\n    fill: variables.diffViewerTitleColor\n  });\n  const diffContainer = css({\n    width: \"100%\",\n    minWidth: \"1000px\",\n    overflowX: \"auto\",\n    tableLayout: \"fixed\",\n    background: variables.diffViewerBackground,\n    pre: {\n      margin: 0,\n      whiteSpace: \"pre-wrap\",\n      lineHeight: \"1.6em\",\n      width: \"fit-content\"\n    },\n    label: \"diff-container\",\n    borderCollapse: \"collapse\"\n  });\n  const lineContent = css({\n    overflow: \"hidden\",\n    width: \"100%\"\n  });\n  const contentText = css({\n    color: variables.diffViewerColor,\n    whiteSpace: \"pre-wrap\",\n    fontFamily: \"monospace\",\n    lineBreak: \"anywhere\",\n    textDecoration: \"none\",\n    label: \"content-text\"\n  });\n  const unselectable = css({\n    userSelect: \"none\",\n    label: \"unselectable\"\n  });\n  const allExpandButton = css({\n    background: \"transparent\",\n    border: \"none\",\n    cursor: \"pointer\",\n    display: \"flex\",\n    alignItems: \"center\",\n    justifyContent: \"center\",\n    margin: 0,\n    label: \"all-expand-button\",\n    \":hover\": {\n      fill: variables.addedGutterColor\n    },\n    \":focus\": {\n      outline: `1px ${variables.addedGutterColor} solid`\n    }\n  });\n  const titleBlock = css({\n    background: variables.diffViewerTitleBackground,\n    padding: \"0.5em\",\n    lineHeight: \"1.4em\",\n    height: \"2.4em\",\n    overflow: \"hidden\",\n    width: \"50%\",\n    borderBottom: `1px solid ${variables.diffViewerTitleBorderColor}`,\n    label: \"title-block\",\n    \":last-child\": {\n      borderLeft: `1px solid ${variables.diffViewerTitleBorderColor}`\n    },\n    [`.${contentText}`]: {\n      color: variables.diffViewerTitleColor\n    }\n  });\n  const lineNumber = css({\n    color: variables.gutterColor,\n    label: \"line-number\"\n  });\n  const diffRemoved = css({\n    background: variables.removedBackground,\n    color: variables.removedColor,\n    pre: {\n      color: variables.removedColor\n    },\n    [`.${lineNumber}`]: {\n      color: variables.removedGutterColor\n    },\n    label: \"diff-removed\"\n  });\n  const diffAdded = css({\n    background: variables.addedBackground,\n    color: variables.addedColor,\n    pre: {\n      color: variables.addedColor\n    },\n    [`.${lineNumber}`]: {\n      color: variables.addedGutterColor\n    },\n    label: \"diff-added\"\n  });\n  const diffChanged = css({\n    background: variables.changedBackground,\n    [`.${lineNumber}`]: {\n      color: variables.gutterColor\n    },\n    label: \"diff-changed\"\n  });\n  const wordDiff = css({\n    padding: 2,\n    display: \"inline-flex\",\n    borderRadius: 4,\n    wordBreak: \"break-all\",\n    label: \"word-diff\"\n  });\n  const wordAdded = css({\n    background: variables.wordAddedBackground,\n    textDecoration: \"none\",\n    label: \"word-added\"\n  });\n  const wordRemoved = css({\n    background: variables.wordRemovedBackground,\n    textDecoration: \"none\",\n    label: \"word-removed\"\n  });\n  const codeFoldGutter = css({\n    backgroundColor: variables.codeFoldGutterBackground,\n    label: \"code-fold-gutter\",\n    minWidth: \"50px\",\n    width: \"50px\"\n  });\n  const codeFoldContentContainer = css({\n    padding: \"\"\n  });\n  const codeFoldExpandButton = css({\n    background: variables.codeFoldBackground,\n    cursor: \"pointer\",\n    display: \"inline\",\n    margin: 0,\n    border: \"none\",\n    label: \"code-fold-expand-button\"\n  });\n  const codeFoldContent = css({\n    color: variables.codeFoldContentColor,\n    fontFamily: \"monospace\",\n    label: \"code-fold-content\"\n  });\n  const block = css({\n    display: \"block\",\n    width: \"10px\",\n    height: \"10px\",\n    backgroundColor: \"#ddd\",\n    borderWidth: \"1px\",\n    borderStyle: \"solid\",\n    borderColor: variables.diffViewerTitleBorderColor\n  });\n  const blockAddition = css({\n    backgroundColor: variables.wordAddedBackground\n  });\n  const blockDeletion = css({\n    backgroundColor: variables.wordRemovedBackground\n  });\n  const codeFold = css({\n    backgroundColor: variables.codeFoldBackground,\n    height: 40,\n    fontSize: 14,\n    alignItems: \"center\",\n    userSelect: \"none\",\n    fontWeight: 700,\n    label: \"code-fold\",\n    a: {\n      textDecoration: \"underline !important\",\n      cursor: \"pointer\",\n      pre: {\n        display: \"inline\"\n      }\n    }\n  });\n  const emptyLine = css({\n    backgroundColor: variables.emptyLineBackground,\n    label: \"empty-line\"\n  });\n  const marker = css({\n    width: 28,\n    paddingLeft: 10,\n    paddingRight: 10,\n    userSelect: \"none\",\n    label: \"marker\",\n    [`&.${diffAdded}`]: {\n      pre: {\n        color: variables.addedColor\n      }\n    },\n    [`&.${diffRemoved}`]: {\n      pre: {\n        color: variables.removedColor\n      }\n    }\n  });\n  const highlightedLine = css({\n    background: variables.highlightBackground,\n    label: \"highlighted-line\",\n    [`.${wordAdded}, .${wordRemoved}`]: {\n      backgroundColor: \"initial\"\n    }\n  });\n  const highlightedGutter = css({\n    label: \"highlighted-gutter\"\n  });\n  const gutter = css({\n    userSelect: \"none\",\n    minWidth: 50,\n    width: \"50px\",\n    padding: \"0 10px\",\n    whiteSpace: \"nowrap\",\n    label: \"gutter\",\n    textAlign: \"right\",\n    background: variables.gutterBackground,\n    \"&:hover\": {\n      cursor: \"pointer\",\n      background: variables.gutterBackgroundDark,\n      pre: {\n        opacity: 1\n      }\n    },\n    pre: {\n      opacity: 0.5\n    },\n    [`&.${diffAdded}`]: {\n      background: variables.addedGutterBackground\n    },\n    [`&.${diffRemoved}`]: {\n      background: variables.removedGutterBackground\n    },\n    [`&.${highlightedGutter}`]: {\n      background: variables.highlightGutterBackground,\n      \"&:hover\": {\n        background: variables.highlightGutterBackground\n      }\n    }\n  });\n  const emptyGutter = css({\n    \"&:hover\": {\n      background: variables.gutterBackground,\n      cursor: \"initial\"\n    },\n    label: \"empty-gutter\"\n  });\n  const line = css({\n    verticalAlign: \"baseline\",\n    label: \"line\",\n    textDecoration: \"none\"\n  });\n  const column = css({});\n  const defaultStyles = {\n    diffContainer,\n    diffRemoved,\n    diffAdded,\n    diffChanged,\n    splitView,\n    marker,\n    highlightedGutter,\n    highlightedLine,\n    gutter,\n    line,\n    lineContent,\n    wordDiff,\n    wordAdded,\n    summary,\n    block,\n    blockAddition,\n    blockDeletion,\n    wordRemoved,\n    noSelect: unselectable,\n    codeFoldGutter,\n    codeFoldExpandButton,\n    codeFoldContentContainer,\n    codeFold,\n    emptyGutter,\n    emptyLine,\n    lineNumber,\n    contentText,\n    content,\n    column,\n    codeFoldContent,\n    titleBlock,\n    allExpandButton\n  };\n  const computerOverrideStyles = Object.keys(styles).reduce((acc, key) => ({\n    ...acc,\n    ...{\n      [key]: css(styles[key])\n    }\n  }), {});\n  return Object.keys(defaultStyles).reduce((acc, key) => ({\n    ...acc,\n    ...{\n      [key]: computerOverrideStyles[key] ? cx(defaultStyles[key], computerOverrideStyles[key]) : defaultStyles[key]\n    }\n  }), {});\n};", "map": {"version": 3, "names": ["createEmotion", "styleOverride", "useDarkTheme", "nonce", "variables", "overrideVariables", "styles", "themeVariables", "light", "diffViewerBackground", "diffViewerColor", "addedBackground", "addedColor", "removedBackground", "removedColor", "changedBackground", "wordAddedBackground", "wordRemovedBackground", "addedGutterBackground", "removedGutterBackground", "gutterBackground", "gutterBackgroundDark", "highlightBackground", "highlightGutterBackground", "codeFoldGutterBackground", "codeFoldBackground", "emptyLineBackground", "gutterColor", "addedGutterColor", "removedGutterColor", "codeFoldContentColor", "diffViewerTitleBackground", "diffViewerTitleColor", "diffViewerTitleBorderColor", "dark", "css", "cx", "key", "content", "width", "label", "splitView", "summary", "background", "color", "padding", "display", "alignItems", "gap", "fontFamily", "fill", "<PERSON>ff<PERSON><PERSON><PERSON>", "min<PERSON><PERSON><PERSON>", "overflowX", "tableLayout", "pre", "margin", "whiteSpace", "lineHeight", "borderCollapse", "lineContent", "overflow", "contentText", "lineBreak", "textDecoration", "unselectable", "userSelect", "allExpandButton", "border", "cursor", "justifyContent", "outline", "titleBlock", "height", "borderBottom", "borderLeft", "lineNumber", "diffRemoved", "diffAdded", "diffChanged", "wordDiff", "borderRadius", "wordBreak", "wordAdded", "wordRemoved", "codeFoldGutter", "backgroundColor", "codeFoldContentContainer", "codeFoldExpandButton", "codeFoldContent", "block", "borderWidth", "borderStyle", "borderColor", "blockAddition", "blockDeletion", "codeFold", "fontSize", "fontWeight", "a", "emptyLine", "marker", "paddingLeft", "paddingRight", "highlightedLine", "<PERSON><PERSON><PERSON>", "gutter", "textAlign", "opacity", "emptyGutter", "line", "verticalAlign", "column", "defaultStyles", "noSelect", "computerOverrideStyles", "Object", "keys", "reduce", "acc"], "sources": ["D:/Documents_2/New Coding/CoderBuddy/CoderBuddy/frontend/node_modules/react-diff-viewer-continued/lib/esm/src/styles.js"], "sourcesContent": ["import createEmotion from \"@emotion/css/create-instance\";\nexport default (styleOverride, useDarkTheme = false, nonce = \"\") => {\n    const { variables: overrideVariables = {}, ...styles } = styleOverride;\n    const themeVariables = {\n        light: {\n            ...{\n                diffViewerBackground: \"#fff\",\n                diffViewerColor: \"#212529\",\n                addedBackground: \"#e6ffed\",\n                addedColor: \"#24292e\",\n                removedBackground: \"#ffeef0\",\n                removedColor: \"#24292e\",\n                changedBackground: \"#fffbdd\",\n                wordAddedBackground: \"#acf2bd\",\n                wordRemovedBackground: \"#fdb8c0\",\n                addedGutterBackground: \"#cdffd8\",\n                removedGutterBackground: \"#ffdce0\",\n                gutterBackground: \"#f7f7f7\",\n                gutterBackgroundDark: \"#f3f1f1\",\n                highlightBackground: \"#fffbdd\",\n                highlightGutterBackground: \"#fff5b1\",\n                codeFoldGutterBackground: \"#dbedff\",\n                codeFoldBackground: \"#f1f8ff\",\n                emptyLineBackground: \"#fafbfc\",\n                gutterColor: \"#212529\",\n                addedGutterColor: \"#212529\",\n                removedGutterColor: \"#212529\",\n                codeFoldContentColor: \"#212529\",\n                diffViewerTitleBackground: \"#fafbfc\",\n                diffViewerTitleColor: \"#212529\",\n                diffViewerTitleBorderColor: \"#eee\",\n            },\n            ...(overrideVariables.light || {}),\n        },\n        dark: {\n            ...{\n                diffViewerBackground: \"#2e303c\",\n                diffViewerColor: \"#FFF\",\n                addedBackground: \"#044B53\",\n                addedColor: \"white\",\n                removedBackground: \"#632F34\",\n                removedColor: \"white\",\n                changedBackground: \"#3e302c\",\n                wordAddedBackground: \"#055d67\",\n                wordRemovedBackground: \"#7d383f\",\n                addedGutterBackground: \"#034148\",\n                removedGutterBackground: \"#632b30\",\n                gutterBackground: \"#2c2f3a\",\n                gutterBackgroundDark: \"#262933\",\n                highlightBackground: \"#2a3967\",\n                highlightGutterBackground: \"#2d4077\",\n                codeFoldGutterBackground: \"#262831\",\n                codeFoldBackground: \"#262831\",\n                emptyLineBackground: \"#363946\",\n                gutterColor: \"#666c87\",\n                addedGutterColor: \"#8c8c8c\",\n                removedGutterColor: \"#8c8c8c\",\n                codeFoldContentColor: \"#656a8b\",\n                diffViewerTitleBackground: \"#2f323e\",\n                diffViewerTitleColor: \"#757a9b\",\n                diffViewerTitleBorderColor: \"#353846\",\n            },\n            ...(overrideVariables.dark || {}),\n        },\n    };\n    const variables = useDarkTheme ? themeVariables.dark : themeVariables.light;\n    const { css, cx } = createEmotion({ key: \"react-diff\", nonce });\n    const content = css({\n        width: \"auto\",\n        label: \"content\",\n    });\n    const splitView = css({\n        label: \"split-view\",\n    });\n    const summary = css({\n        background: variables.diffViewerTitleBackground,\n        color: variables.diffViewerTitleColor,\n        padding: \"0.5em 1em\",\n        display: \"flex\",\n        alignItems: \"center\",\n        gap: \"0.5em\",\n        fontFamily: \"monospace\",\n        fill: variables.diffViewerTitleColor,\n    });\n    const diffContainer = css({\n        width: \"100%\",\n        minWidth: \"1000px\",\n        overflowX: \"auto\",\n        tableLayout: \"fixed\",\n        background: variables.diffViewerBackground,\n        pre: {\n            margin: 0,\n            whiteSpace: \"pre-wrap\",\n            lineHeight: \"1.6em\",\n            width: \"fit-content\",\n        },\n        label: \"diff-container\",\n        borderCollapse: \"collapse\",\n    });\n    const lineContent = css({\n        overflow: \"hidden\",\n        width: \"100%\",\n    });\n    const contentText = css({\n        color: variables.diffViewerColor,\n        whiteSpace: \"pre-wrap\",\n        fontFamily: \"monospace\",\n        lineBreak: \"anywhere\",\n        textDecoration: \"none\",\n        label: \"content-text\",\n    });\n    const unselectable = css({\n        userSelect: \"none\",\n        label: \"unselectable\",\n    });\n    const allExpandButton = css({\n        background: \"transparent\",\n        border: \"none\",\n        cursor: \"pointer\",\n        display: \"flex\",\n        alignItems: \"center\",\n        justifyContent: \"center\",\n        margin: 0,\n        label: \"all-expand-button\",\n        \":hover\": {\n            fill: variables.addedGutterColor,\n        },\n        \":focus\": {\n            outline: `1px ${variables.addedGutterColor} solid`,\n        },\n    });\n    const titleBlock = css({\n        background: variables.diffViewerTitleBackground,\n        padding: \"0.5em\",\n        lineHeight: \"1.4em\",\n        height: \"2.4em\",\n        overflow: \"hidden\",\n        width: \"50%\",\n        borderBottom: `1px solid ${variables.diffViewerTitleBorderColor}`,\n        label: \"title-block\",\n        \":last-child\": {\n            borderLeft: `1px solid ${variables.diffViewerTitleBorderColor}`,\n        },\n        [`.${contentText}`]: {\n            color: variables.diffViewerTitleColor,\n        },\n    });\n    const lineNumber = css({\n        color: variables.gutterColor,\n        label: \"line-number\",\n    });\n    const diffRemoved = css({\n        background: variables.removedBackground,\n        color: variables.removedColor,\n        pre: {\n            color: variables.removedColor,\n        },\n        [`.${lineNumber}`]: {\n            color: variables.removedGutterColor,\n        },\n        label: \"diff-removed\",\n    });\n    const diffAdded = css({\n        background: variables.addedBackground,\n        color: variables.addedColor,\n        pre: {\n            color: variables.addedColor,\n        },\n        [`.${lineNumber}`]: {\n            color: variables.addedGutterColor,\n        },\n        label: \"diff-added\",\n    });\n    const diffChanged = css({\n        background: variables.changedBackground,\n        [`.${lineNumber}`]: {\n            color: variables.gutterColor,\n        },\n        label: \"diff-changed\",\n    });\n    const wordDiff = css({\n        padding: 2,\n        display: \"inline-flex\",\n        borderRadius: 4,\n        wordBreak: \"break-all\",\n        label: \"word-diff\",\n    });\n    const wordAdded = css({\n        background: variables.wordAddedBackground,\n        textDecoration: \"none\",\n        label: \"word-added\",\n    });\n    const wordRemoved = css({\n        background: variables.wordRemovedBackground,\n        textDecoration: \"none\",\n        label: \"word-removed\",\n    });\n    const codeFoldGutter = css({\n        backgroundColor: variables.codeFoldGutterBackground,\n        label: \"code-fold-gutter\",\n        minWidth: \"50px\",\n        width: \"50px\",\n    });\n    const codeFoldContentContainer = css({\n        padding: \"\",\n    });\n    const codeFoldExpandButton = css({\n        background: variables.codeFoldBackground,\n        cursor: \"pointer\",\n        display: \"inline\",\n        margin: 0,\n        border: \"none\",\n        label: \"code-fold-expand-button\",\n    });\n    const codeFoldContent = css({\n        color: variables.codeFoldContentColor,\n        fontFamily: \"monospace\",\n        label: \"code-fold-content\",\n    });\n    const block = css({\n        display: \"block\",\n        width: \"10px\",\n        height: \"10px\",\n        backgroundColor: \"#ddd\",\n        borderWidth: \"1px\",\n        borderStyle: \"solid\",\n        borderColor: variables.diffViewerTitleBorderColor,\n    });\n    const blockAddition = css({\n        backgroundColor: variables.wordAddedBackground,\n    });\n    const blockDeletion = css({\n        backgroundColor: variables.wordRemovedBackground,\n    });\n    const codeFold = css({\n        backgroundColor: variables.codeFoldBackground,\n        height: 40,\n        fontSize: 14,\n        alignItems: \"center\",\n        userSelect: \"none\",\n        fontWeight: 700,\n        label: \"code-fold\",\n        a: {\n            textDecoration: \"underline !important\",\n            cursor: \"pointer\",\n            pre: {\n                display: \"inline\",\n            },\n        },\n    });\n    const emptyLine = css({\n        backgroundColor: variables.emptyLineBackground,\n        label: \"empty-line\",\n    });\n    const marker = css({\n        width: 28,\n        paddingLeft: 10,\n        paddingRight: 10,\n        userSelect: \"none\",\n        label: \"marker\",\n        [`&.${diffAdded}`]: {\n            pre: {\n                color: variables.addedColor,\n            },\n        },\n        [`&.${diffRemoved}`]: {\n            pre: {\n                color: variables.removedColor,\n            },\n        },\n    });\n    const highlightedLine = css({\n        background: variables.highlightBackground,\n        label: \"highlighted-line\",\n        [`.${wordAdded}, .${wordRemoved}`]: {\n            backgroundColor: \"initial\",\n        },\n    });\n    const highlightedGutter = css({\n        label: \"highlighted-gutter\",\n    });\n    const gutter = css({\n        userSelect: \"none\",\n        minWidth: 50,\n        width: \"50px\",\n        padding: \"0 10px\",\n        whiteSpace: \"nowrap\",\n        label: \"gutter\",\n        textAlign: \"right\",\n        background: variables.gutterBackground,\n        \"&:hover\": {\n            cursor: \"pointer\",\n            background: variables.gutterBackgroundDark,\n            pre: {\n                opacity: 1,\n            },\n        },\n        pre: {\n            opacity: 0.5,\n        },\n        [`&.${diffAdded}`]: {\n            background: variables.addedGutterBackground,\n        },\n        [`&.${diffRemoved}`]: {\n            background: variables.removedGutterBackground,\n        },\n        [`&.${highlightedGutter}`]: {\n            background: variables.highlightGutterBackground,\n            \"&:hover\": {\n                background: variables.highlightGutterBackground,\n            },\n        },\n    });\n    const emptyGutter = css({\n        \"&:hover\": {\n            background: variables.gutterBackground,\n            cursor: \"initial\",\n        },\n        label: \"empty-gutter\",\n    });\n    const line = css({\n        verticalAlign: \"baseline\",\n        label: \"line\",\n        textDecoration: \"none\",\n    });\n    const column = css({});\n    const defaultStyles = {\n        diffContainer,\n        diffRemoved,\n        diffAdded,\n        diffChanged,\n        splitView,\n        marker,\n        highlightedGutter,\n        highlightedLine,\n        gutter,\n        line,\n        lineContent,\n        wordDiff,\n        wordAdded,\n        summary,\n        block,\n        blockAddition,\n        blockDeletion,\n        wordRemoved,\n        noSelect: unselectable,\n        codeFoldGutter,\n        codeFoldExpandButton,\n        codeFoldContentContainer,\n        codeFold,\n        emptyGutter,\n        emptyLine,\n        lineNumber,\n        contentText,\n        content,\n        column,\n        codeFoldContent,\n        titleBlock,\n        allExpandButton,\n    };\n    const computerOverrideStyles = Object.keys(styles).reduce((acc, key) => ({\n        ...acc,\n        ...{\n            [key]: css(styles[key]),\n        },\n    }), {});\n    return Object.keys(defaultStyles).reduce((acc, key) => ({\n        ...acc,\n        ...{\n            [key]: computerOverrideStyles[key]\n                ? cx(defaultStyles[key], computerOverrideStyles[key])\n                : defaultStyles[key],\n        },\n    }), {});\n};\n"], "mappings": "AAAA,OAAOA,aAAa,MAAM,8BAA8B;AACxD,eAAe,CAACC,aAAa,EAAEC,YAAY,GAAG,KAAK,EAAEC,KAAK,GAAG,EAAE,KAAK;EAChE,MAAM;IAAEC,SAAS,EAAEC,iBAAiB,GAAG,CAAC,CAAC;IAAE,GAAGC;EAAO,CAAC,GAAGL,aAAa;EACtE,MAAMM,cAAc,GAAG;IACnBC,KAAK,EAAE;MACH,GAAG;QACCC,oBAAoB,EAAE,MAAM;QAC5BC,eAAe,EAAE,SAAS;QAC1BC,eAAe,EAAE,SAAS;QAC1BC,UAAU,EAAE,SAAS;QACrBC,iBAAiB,EAAE,SAAS;QAC5BC,YAAY,EAAE,SAAS;QACvBC,iBAAiB,EAAE,SAAS;QAC5BC,mBAAmB,EAAE,SAAS;QAC9BC,qBAAqB,EAAE,SAAS;QAChCC,qBAAqB,EAAE,SAAS;QAChCC,uBAAuB,EAAE,SAAS;QAClCC,gBAAgB,EAAE,SAAS;QAC3BC,oBAAoB,EAAE,SAAS;QAC/BC,mBAAmB,EAAE,SAAS;QAC9BC,yBAAyB,EAAE,SAAS;QACpCC,wBAAwB,EAAE,SAAS;QACnCC,kBAAkB,EAAE,SAAS;QAC7BC,mBAAmB,EAAE,SAAS;QAC9BC,WAAW,EAAE,SAAS;QACtBC,gBAAgB,EAAE,SAAS;QAC3BC,kBAAkB,EAAE,SAAS;QAC7BC,oBAAoB,EAAE,SAAS;QAC/BC,yBAAyB,EAAE,SAAS;QACpCC,oBAAoB,EAAE,SAAS;QAC/BC,0BAA0B,EAAE;MAChC,CAAC;MACD,IAAI5B,iBAAiB,CAACG,KAAK,IAAI,CAAC,CAAC;IACrC,CAAC;IACD0B,IAAI,EAAE;MACF,GAAG;QACCzB,oBAAoB,EAAE,SAAS;QAC/BC,eAAe,EAAE,MAAM;QACvBC,eAAe,EAAE,SAAS;QAC1BC,UAAU,EAAE,OAAO;QACnBC,iBAAiB,EAAE,SAAS;QAC5BC,YAAY,EAAE,OAAO;QACrBC,iBAAiB,EAAE,SAAS;QAC5BC,mBAAmB,EAAE,SAAS;QAC9BC,qBAAqB,EAAE,SAAS;QAChCC,qBAAqB,EAAE,SAAS;QAChCC,uBAAuB,EAAE,SAAS;QAClCC,gBAAgB,EAAE,SAAS;QAC3BC,oBAAoB,EAAE,SAAS;QAC/BC,mBAAmB,EAAE,SAAS;QAC9BC,yBAAyB,EAAE,SAAS;QACpCC,wBAAwB,EAAE,SAAS;QACnCC,kBAAkB,EAAE,SAAS;QAC7BC,mBAAmB,EAAE,SAAS;QAC9BC,WAAW,EAAE,SAAS;QACtBC,gBAAgB,EAAE,SAAS;QAC3BC,kBAAkB,EAAE,SAAS;QAC7BC,oBAAoB,EAAE,SAAS;QAC/BC,yBAAyB,EAAE,SAAS;QACpCC,oBAAoB,EAAE,SAAS;QAC/BC,0BAA0B,EAAE;MAChC,CAAC;MACD,IAAI5B,iBAAiB,CAAC6B,IAAI,IAAI,CAAC,CAAC;IACpC;EACJ,CAAC;EACD,MAAM9B,SAAS,GAAGF,YAAY,GAAGK,cAAc,CAAC2B,IAAI,GAAG3B,cAAc,CAACC,KAAK;EAC3E,MAAM;IAAE2B,GAAG;IAAEC;EAAG,CAAC,GAAGpC,aAAa,CAAC;IAAEqC,GAAG,EAAE,YAAY;IAAElC;EAAM,CAAC,CAAC;EAC/D,MAAMmC,OAAO,GAAGH,GAAG,CAAC;IAChBI,KAAK,EAAE,MAAM;IACbC,KAAK,EAAE;EACX,CAAC,CAAC;EACF,MAAMC,SAAS,GAAGN,GAAG,CAAC;IAClBK,KAAK,EAAE;EACX,CAAC,CAAC;EACF,MAAME,OAAO,GAAGP,GAAG,CAAC;IAChBQ,UAAU,EAAEvC,SAAS,CAAC2B,yBAAyB;IAC/Ca,KAAK,EAAExC,SAAS,CAAC4B,oBAAoB;IACrCa,OAAO,EAAE,WAAW;IACpBC,OAAO,EAAE,MAAM;IACfC,UAAU,EAAE,QAAQ;IACpBC,GAAG,EAAE,OAAO;IACZC,UAAU,EAAE,WAAW;IACvBC,IAAI,EAAE9C,SAAS,CAAC4B;EACpB,CAAC,CAAC;EACF,MAAMmB,aAAa,GAAGhB,GAAG,CAAC;IACtBI,KAAK,EAAE,MAAM;IACba,QAAQ,EAAE,QAAQ;IAClBC,SAAS,EAAE,MAAM;IACjBC,WAAW,EAAE,OAAO;IACpBX,UAAU,EAAEvC,SAAS,CAACK,oBAAoB;IAC1C8C,GAAG,EAAE;MACDC,MAAM,EAAE,CAAC;MACTC,UAAU,EAAE,UAAU;MACtBC,UAAU,EAAE,OAAO;MACnBnB,KAAK,EAAE;IACX,CAAC;IACDC,KAAK,EAAE,gBAAgB;IACvBmB,cAAc,EAAE;EACpB,CAAC,CAAC;EACF,MAAMC,WAAW,GAAGzB,GAAG,CAAC;IACpB0B,QAAQ,EAAE,QAAQ;IAClBtB,KAAK,EAAE;EACX,CAAC,CAAC;EACF,MAAMuB,WAAW,GAAG3B,GAAG,CAAC;IACpBS,KAAK,EAAExC,SAAS,CAACM,eAAe;IAChC+C,UAAU,EAAE,UAAU;IACtBR,UAAU,EAAE,WAAW;IACvBc,SAAS,EAAE,UAAU;IACrBC,cAAc,EAAE,MAAM;IACtBxB,KAAK,EAAE;EACX,CAAC,CAAC;EACF,MAAMyB,YAAY,GAAG9B,GAAG,CAAC;IACrB+B,UAAU,EAAE,MAAM;IAClB1B,KAAK,EAAE;EACX,CAAC,CAAC;EACF,MAAM2B,eAAe,GAAGhC,GAAG,CAAC;IACxBQ,UAAU,EAAE,aAAa;IACzByB,MAAM,EAAE,MAAM;IACdC,MAAM,EAAE,SAAS;IACjBvB,OAAO,EAAE,MAAM;IACfC,UAAU,EAAE,QAAQ;IACpBuB,cAAc,EAAE,QAAQ;IACxBd,MAAM,EAAE,CAAC;IACThB,KAAK,EAAE,mBAAmB;IAC1B,QAAQ,EAAE;MACNU,IAAI,EAAE9C,SAAS,CAACwB;IACpB,CAAC;IACD,QAAQ,EAAE;MACN2C,OAAO,EAAE,OAAOnE,SAAS,CAACwB,gBAAgB;IAC9C;EACJ,CAAC,CAAC;EACF,MAAM4C,UAAU,GAAGrC,GAAG,CAAC;IACnBQ,UAAU,EAAEvC,SAAS,CAAC2B,yBAAyB;IAC/Cc,OAAO,EAAE,OAAO;IAChBa,UAAU,EAAE,OAAO;IACnBe,MAAM,EAAE,OAAO;IACfZ,QAAQ,EAAE,QAAQ;IAClBtB,KAAK,EAAE,KAAK;IACZmC,YAAY,EAAE,aAAatE,SAAS,CAAC6B,0BAA0B,EAAE;IACjEO,KAAK,EAAE,aAAa;IACpB,aAAa,EAAE;MACXmC,UAAU,EAAE,aAAavE,SAAS,CAAC6B,0BAA0B;IACjE,CAAC;IACD,CAAC,IAAI6B,WAAW,EAAE,GAAG;MACjBlB,KAAK,EAAExC,SAAS,CAAC4B;IACrB;EACJ,CAAC,CAAC;EACF,MAAM4C,UAAU,GAAGzC,GAAG,CAAC;IACnBS,KAAK,EAAExC,SAAS,CAACuB,WAAW;IAC5Ba,KAAK,EAAE;EACX,CAAC,CAAC;EACF,MAAMqC,WAAW,GAAG1C,GAAG,CAAC;IACpBQ,UAAU,EAAEvC,SAAS,CAACS,iBAAiB;IACvC+B,KAAK,EAAExC,SAAS,CAACU,YAAY;IAC7ByC,GAAG,EAAE;MACDX,KAAK,EAAExC,SAAS,CAACU;IACrB,CAAC;IACD,CAAC,IAAI8D,UAAU,EAAE,GAAG;MAChBhC,KAAK,EAAExC,SAAS,CAACyB;IACrB,CAAC;IACDW,KAAK,EAAE;EACX,CAAC,CAAC;EACF,MAAMsC,SAAS,GAAG3C,GAAG,CAAC;IAClBQ,UAAU,EAAEvC,SAAS,CAACO,eAAe;IACrCiC,KAAK,EAAExC,SAAS,CAACQ,UAAU;IAC3B2C,GAAG,EAAE;MACDX,KAAK,EAAExC,SAAS,CAACQ;IACrB,CAAC;IACD,CAAC,IAAIgE,UAAU,EAAE,GAAG;MAChBhC,KAAK,EAAExC,SAAS,CAACwB;IACrB,CAAC;IACDY,KAAK,EAAE;EACX,CAAC,CAAC;EACF,MAAMuC,WAAW,GAAG5C,GAAG,CAAC;IACpBQ,UAAU,EAAEvC,SAAS,CAACW,iBAAiB;IACvC,CAAC,IAAI6D,UAAU,EAAE,GAAG;MAChBhC,KAAK,EAAExC,SAAS,CAACuB;IACrB,CAAC;IACDa,KAAK,EAAE;EACX,CAAC,CAAC;EACF,MAAMwC,QAAQ,GAAG7C,GAAG,CAAC;IACjBU,OAAO,EAAE,CAAC;IACVC,OAAO,EAAE,aAAa;IACtBmC,YAAY,EAAE,CAAC;IACfC,SAAS,EAAE,WAAW;IACtB1C,KAAK,EAAE;EACX,CAAC,CAAC;EACF,MAAM2C,SAAS,GAAGhD,GAAG,CAAC;IAClBQ,UAAU,EAAEvC,SAAS,CAACY,mBAAmB;IACzCgD,cAAc,EAAE,MAAM;IACtBxB,KAAK,EAAE;EACX,CAAC,CAAC;EACF,MAAM4C,WAAW,GAAGjD,GAAG,CAAC;IACpBQ,UAAU,EAAEvC,SAAS,CAACa,qBAAqB;IAC3C+C,cAAc,EAAE,MAAM;IACtBxB,KAAK,EAAE;EACX,CAAC,CAAC;EACF,MAAM6C,cAAc,GAAGlD,GAAG,CAAC;IACvBmD,eAAe,EAAElF,SAAS,CAACoB,wBAAwB;IACnDgB,KAAK,EAAE,kBAAkB;IACzBY,QAAQ,EAAE,MAAM;IAChBb,KAAK,EAAE;EACX,CAAC,CAAC;EACF,MAAMgD,wBAAwB,GAAGpD,GAAG,CAAC;IACjCU,OAAO,EAAE;EACb,CAAC,CAAC;EACF,MAAM2C,oBAAoB,GAAGrD,GAAG,CAAC;IAC7BQ,UAAU,EAAEvC,SAAS,CAACqB,kBAAkB;IACxC4C,MAAM,EAAE,SAAS;IACjBvB,OAAO,EAAE,QAAQ;IACjBU,MAAM,EAAE,CAAC;IACTY,MAAM,EAAE,MAAM;IACd5B,KAAK,EAAE;EACX,CAAC,CAAC;EACF,MAAMiD,eAAe,GAAGtD,GAAG,CAAC;IACxBS,KAAK,EAAExC,SAAS,CAAC0B,oBAAoB;IACrCmB,UAAU,EAAE,WAAW;IACvBT,KAAK,EAAE;EACX,CAAC,CAAC;EACF,MAAMkD,KAAK,GAAGvD,GAAG,CAAC;IACdW,OAAO,EAAE,OAAO;IAChBP,KAAK,EAAE,MAAM;IACbkC,MAAM,EAAE,MAAM;IACda,eAAe,EAAE,MAAM;IACvBK,WAAW,EAAE,KAAK;IAClBC,WAAW,EAAE,OAAO;IACpBC,WAAW,EAAEzF,SAAS,CAAC6B;EAC3B,CAAC,CAAC;EACF,MAAM6D,aAAa,GAAG3D,GAAG,CAAC;IACtBmD,eAAe,EAAElF,SAAS,CAACY;EAC/B,CAAC,CAAC;EACF,MAAM+E,aAAa,GAAG5D,GAAG,CAAC;IACtBmD,eAAe,EAAElF,SAAS,CAACa;EAC/B,CAAC,CAAC;EACF,MAAM+E,QAAQ,GAAG7D,GAAG,CAAC;IACjBmD,eAAe,EAAElF,SAAS,CAACqB,kBAAkB;IAC7CgD,MAAM,EAAE,EAAE;IACVwB,QAAQ,EAAE,EAAE;IACZlD,UAAU,EAAE,QAAQ;IACpBmB,UAAU,EAAE,MAAM;IAClBgC,UAAU,EAAE,GAAG;IACf1D,KAAK,EAAE,WAAW;IAClB2D,CAAC,EAAE;MACCnC,cAAc,EAAE,sBAAsB;MACtCK,MAAM,EAAE,SAAS;MACjBd,GAAG,EAAE;QACDT,OAAO,EAAE;MACb;IACJ;EACJ,CAAC,CAAC;EACF,MAAMsD,SAAS,GAAGjE,GAAG,CAAC;IAClBmD,eAAe,EAAElF,SAAS,CAACsB,mBAAmB;IAC9Cc,KAAK,EAAE;EACX,CAAC,CAAC;EACF,MAAM6D,MAAM,GAAGlE,GAAG,CAAC;IACfI,KAAK,EAAE,EAAE;IACT+D,WAAW,EAAE,EAAE;IACfC,YAAY,EAAE,EAAE;IAChBrC,UAAU,EAAE,MAAM;IAClB1B,KAAK,EAAE,QAAQ;IACf,CAAC,KAAKsC,SAAS,EAAE,GAAG;MAChBvB,GAAG,EAAE;QACDX,KAAK,EAAExC,SAAS,CAACQ;MACrB;IACJ,CAAC;IACD,CAAC,KAAKiE,WAAW,EAAE,GAAG;MAClBtB,GAAG,EAAE;QACDX,KAAK,EAAExC,SAAS,CAACU;MACrB;IACJ;EACJ,CAAC,CAAC;EACF,MAAM0F,eAAe,GAAGrE,GAAG,CAAC;IACxBQ,UAAU,EAAEvC,SAAS,CAACkB,mBAAmB;IACzCkB,KAAK,EAAE,kBAAkB;IACzB,CAAC,IAAI2C,SAAS,MAAMC,WAAW,EAAE,GAAG;MAChCE,eAAe,EAAE;IACrB;EACJ,CAAC,CAAC;EACF,MAAMmB,iBAAiB,GAAGtE,GAAG,CAAC;IAC1BK,KAAK,EAAE;EACX,CAAC,CAAC;EACF,MAAMkE,MAAM,GAAGvE,GAAG,CAAC;IACf+B,UAAU,EAAE,MAAM;IAClBd,QAAQ,EAAE,EAAE;IACZb,KAAK,EAAE,MAAM;IACbM,OAAO,EAAE,QAAQ;IACjBY,UAAU,EAAE,QAAQ;IACpBjB,KAAK,EAAE,QAAQ;IACfmE,SAAS,EAAE,OAAO;IAClBhE,UAAU,EAAEvC,SAAS,CAACgB,gBAAgB;IACtC,SAAS,EAAE;MACPiD,MAAM,EAAE,SAAS;MACjB1B,UAAU,EAAEvC,SAAS,CAACiB,oBAAoB;MAC1CkC,GAAG,EAAE;QACDqD,OAAO,EAAE;MACb;IACJ,CAAC;IACDrD,GAAG,EAAE;MACDqD,OAAO,EAAE;IACb,CAAC;IACD,CAAC,KAAK9B,SAAS,EAAE,GAAG;MAChBnC,UAAU,EAAEvC,SAAS,CAACc;IAC1B,CAAC;IACD,CAAC,KAAK2D,WAAW,EAAE,GAAG;MAClBlC,UAAU,EAAEvC,SAAS,CAACe;IAC1B,CAAC;IACD,CAAC,KAAKsF,iBAAiB,EAAE,GAAG;MACxB9D,UAAU,EAAEvC,SAAS,CAACmB,yBAAyB;MAC/C,SAAS,EAAE;QACPoB,UAAU,EAAEvC,SAAS,CAACmB;MAC1B;IACJ;EACJ,CAAC,CAAC;EACF,MAAMsF,WAAW,GAAG1E,GAAG,CAAC;IACpB,SAAS,EAAE;MACPQ,UAAU,EAAEvC,SAAS,CAACgB,gBAAgB;MACtCiD,MAAM,EAAE;IACZ,CAAC;IACD7B,KAAK,EAAE;EACX,CAAC,CAAC;EACF,MAAMsE,IAAI,GAAG3E,GAAG,CAAC;IACb4E,aAAa,EAAE,UAAU;IACzBvE,KAAK,EAAE,MAAM;IACbwB,cAAc,EAAE;EACpB,CAAC,CAAC;EACF,MAAMgD,MAAM,GAAG7E,GAAG,CAAC,CAAC,CAAC,CAAC;EACtB,MAAM8E,aAAa,GAAG;IAClB9D,aAAa;IACb0B,WAAW;IACXC,SAAS;IACTC,WAAW;IACXtC,SAAS;IACT4D,MAAM;IACNI,iBAAiB;IACjBD,eAAe;IACfE,MAAM;IACNI,IAAI;IACJlD,WAAW;IACXoB,QAAQ;IACRG,SAAS;IACTzC,OAAO;IACPgD,KAAK;IACLI,aAAa;IACbC,aAAa;IACbX,WAAW;IACX8B,QAAQ,EAAEjD,YAAY;IACtBoB,cAAc;IACdG,oBAAoB;IACpBD,wBAAwB;IACxBS,QAAQ;IACRa,WAAW;IACXT,SAAS;IACTxB,UAAU;IACVd,WAAW;IACXxB,OAAO;IACP0E,MAAM;IACNvB,eAAe;IACfjB,UAAU;IACVL;EACJ,CAAC;EACD,MAAMgD,sBAAsB,GAAGC,MAAM,CAACC,IAAI,CAAC/G,MAAM,CAAC,CAACgH,MAAM,CAAC,CAACC,GAAG,EAAElF,GAAG,MAAM;IACrE,GAAGkF,GAAG;IACN,GAAG;MACC,CAAClF,GAAG,GAAGF,GAAG,CAAC7B,MAAM,CAAC+B,GAAG,CAAC;IAC1B;EACJ,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;EACP,OAAO+E,MAAM,CAACC,IAAI,CAACJ,aAAa,CAAC,CAACK,MAAM,CAAC,CAACC,GAAG,EAAElF,GAAG,MAAM;IACpD,GAAGkF,GAAG;IACN,GAAG;MACC,CAAClF,GAAG,GAAG8E,sBAAsB,CAAC9E,GAAG,CAAC,GAC5BD,EAAE,CAAC6E,aAAa,CAAC5E,GAAG,CAAC,EAAE8E,sBAAsB,CAAC9E,GAAG,CAAC,CAAC,GACnD4E,aAAa,CAAC5E,GAAG;IAC3B;EACJ,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;AACX,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}