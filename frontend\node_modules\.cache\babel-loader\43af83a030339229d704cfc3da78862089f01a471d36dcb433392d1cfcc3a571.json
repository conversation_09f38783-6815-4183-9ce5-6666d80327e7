{"ast": null, "code": "import { jsx as _jsx, jsxs as _jsxs } from \"react/jsx-runtime\";\nexport function Fold() {\n  return _jsxs(\"svg\", {\n    xmlns: \"http://www.w3.org/2000/svg\",\n    viewBox: \"0 0 16 16\",\n    width: \"16\",\n    height: \"16\",\n    children: [_jsx(\"title\", {\n      children: \"fold\"\n    }), _jsx(\"path\", {\n      d: \"M10.896 2H8.75V.75a.75.75 0 0 0-1.5 0V2H5.104a.25.25 0 0 0-.177.427l2.896 2.896a.25.25 0 0 0 .354 0l2.896-2.896A.25.25 0 0 0 10.896 2ZM8.75 15.25a.75.75 0 0 1-1.5 0V14H5.104a.25.25 0 0 1-.177-.427l2.896-2.896a.25.25 0 0 1 .354 0l2.896 2.896a.25.25 0 0 1-.177.427H8.75v1.25Zm-6.5-6.5a.75.75 0 0 0 0-1.5h-.5a.75.75 0 0 0 0 1.5h.5ZM6 8a.75.75 0 0 1-.75.75h-.5a.75.75 0 0 1 0-1.5h.5A.75.75 0 0 1 6 8Zm2.25.75a.75.75 0 0 0 0-1.5h-.5a.75.75 0 0 0 0 1.5h.5ZM12 8a.75.75 0 0 1-.75.75h-.5a.75.75 0 0 1 0-1.5h.5A.75.75 0 0 1 12 8Zm2.25.75a.75.75 0 0 0 0-1.5h-.5a.75.75 0 0 0 0 1.5h.5Z\"\n    })]\n  });\n}", "map": {"version": 3, "names": ["jsx", "_jsx", "jsxs", "_jsxs", "Fold", "xmlns", "viewBox", "width", "height", "children", "d"], "sources": ["D:/Documents_2/New Coding/CoderBuddy/CoderBuddy/frontend/node_modules/react-diff-viewer-continued/lib/esm/src/fold.js"], "sourcesContent": ["import { jsx as _jsx, jsxs as _jsxs } from \"react/jsx-runtime\";\nexport function Fold() {\n    return (_jsxs(\"svg\", { xmlns: \"http://www.w3.org/2000/svg\", viewBox: \"0 0 16 16\", width: \"16\", height: \"16\", children: [_jsx(\"title\", { children: \"fold\" }), _jsx(\"path\", { d: \"M10.896 2H8.75V.75a.75.75 0 0 0-1.5 0V2H5.104a.25.25 0 0 0-.177.427l2.896 2.896a.25.25 0 0 0 .354 0l2.896-2.896A.25.25 0 0 0 10.896 2ZM8.75 15.25a.75.75 0 0 1-1.5 0V14H5.104a.25.25 0 0 1-.177-.427l2.896-2.896a.25.25 0 0 1 .354 0l2.896 2.896a.25.25 0 0 1-.177.427H8.75v1.25Zm-6.5-6.5a.75.75 0 0 0 0-1.5h-.5a.75.75 0 0 0 0 1.5h.5ZM6 8a.75.75 0 0 1-.75.75h-.5a.75.75 0 0 1 0-1.5h.5A.75.75 0 0 1 6 8Zm2.25.75a.75.75 0 0 0 0-1.5h-.5a.75.75 0 0 0 0 1.5h.5ZM12 8a.75.75 0 0 1-.75.75h-.5a.75.75 0 0 1 0-1.5h.5A.75.75 0 0 1 12 8Zm2.25.75a.75.75 0 0 0 0-1.5h-.5a.75.75 0 0 0 0 1.5h.5Z\" })] }));\n}\n"], "mappings": "AAAA,SAASA,GAAG,IAAIC,IAAI,EAAEC,IAAI,IAAIC,KAAK,QAAQ,mBAAmB;AAC9D,OAAO,SAASC,IAAIA,CAAA,EAAG;EACnB,OAAQD,KAAK,CAAC,KAAK,EAAE;IAAEE,KAAK,EAAE,4BAA4B;IAAEC,OAAO,EAAE,WAAW;IAAEC,KAAK,EAAE,IAAI;IAAEC,MAAM,EAAE,IAAI;IAAEC,QAAQ,EAAE,CAACR,IAAI,CAAC,OAAO,EAAE;MAAEQ,QAAQ,EAAE;IAAO,CAAC,CAAC,EAAER,IAAI,CAAC,MAAM,EAAE;MAAES,CAAC,EAAE;IAAikB,CAAC,CAAC;EAAE,CAAC,CAAC;AAC1vB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}