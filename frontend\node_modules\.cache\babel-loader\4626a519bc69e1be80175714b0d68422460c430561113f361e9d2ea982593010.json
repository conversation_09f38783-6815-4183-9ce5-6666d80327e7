{"ast": null, "code": "var _jsxFileName = \"D:\\\\Documents_2\\\\New Coding\\\\CoderBuddy\\\\CoderBuddy\\\\frontend\\\\src\\\\components\\\\EditorTabs.js\",\n  _s = $RefreshSig$();\nimport React, { useState } from 'react';\nimport { readFile, writeFile } from '../services/api';\nimport Editor, { DiffEditor } from '@monaco-editor/react';\nimport { useHotkeys } from 'react-hotkeys-hook';\nimport NiceModal from '@ebay/nice-modal-react';\nimport SaveDiffModal from './SaveDiffModal';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nexport default function EditorTabs({\n  selectedFile,\n  projectId,\n  proposedEdit,\n  onEditApplied\n}) {\n  _s();\n  const [tabs, setTabs] = useState([]);\n  const [activePath, setActivePath] = useState(null);\n  React.useEffect(() => {\n    if (selectedFile) {\n      openFile(selectedFile);\n    }\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n  }, [selectedFile, projectId]);\n  React.useEffect(() => {\n    if (proposedEdit) {\n      proposeFileEdit(proposedEdit.rel_path, proposedEdit.content);\n      onEditApplied(); // Signal that we've handled it\n    }\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n  }, [proposedEdit]);\n  React.useEffect(() => {\n    setTabs([]);\n    setActivePath(null);\n  }, [projectId]);\n  async function openFile(path) {\n    // if already open, just activate\n    const existing = tabs.find(t => t.path === path);\n    if (existing) {\n      setActivePath(path);\n      return;\n    }\n    try {\n      const {\n        content\n      } = await readFile(path, projectId);\n      setTabs(prev => [...prev, {\n        path,\n        content,\n        savedContent: content,\n        diffMode: false\n      }]);\n      setActivePath(path);\n    } catch (e) {\n      alert(`Failed to open file: ${e.message}`);\n    }\n  }\n  async function proposeFileEdit(path, newContent) {\n    const existingTab = tabs.find(t => t.path === path);\n    let originalContent;\n    let isNewFile = false;\n    if (existingTab) {\n      originalContent = existingTab.savedContent;\n    } else {\n      try {\n        const fileData = await readFile(path, projectId);\n        originalContent = fileData.content;\n      } catch (e) {\n        // It's a new file\n        originalContent = '';\n        isNewFile = true;\n      }\n    }\n    const newTab = {\n      path,\n      content: newContent,\n      savedContent: originalContent,\n      diffMode: true\n    };\n\n    // If file isn't open, add it. Otherwise, replace it.\n    const otherTabs = tabs.filter(t => t.path !== path);\n    setTabs([...otherTabs, newTab]);\n    setActivePath(path);\n  }\n  function handleChange(value = '') {\n    setTabs(prev => prev.map(t => t.path === activePath ? {\n      ...t,\n      content: value\n    } : t));\n  }\n  const handleSave = async () => {\n    if (!activePath) return;\n    const tab = tabs.find(t => t.path === activePath);\n    if (!tab) return;\n\n    // If not in diff mode yet and file is dirty -> open diff view\n    if (!tab.diffMode) {\n      if (tab.content === tab.savedContent) return; // nothing to save\n      setTabs(prev => prev.map(t => t.path === tab.path ? {\n        ...t,\n        diffMode: true\n      } : t));\n      return;\n    }\n\n    // In diff mode -> actually persist\n    try {\n      await writeFile(tab.path, tab.content, true, projectId);\n      setTabs(prev => prev.map(t => t.path === tab.path ? {\n        ...t,\n        savedContent: t.content,\n        diffMode: false\n      } : t));\n    } catch (e) {\n      console.error(e);\n      alert('Save failed: ' + e.message);\n    }\n  };\n\n  // Cancel diff -> revert content\n  const handleCancel = () => {\n    if (!activePath) return;\n    setTabs(prev => prev.map(t => t.path === activePath ? {\n      ...t,\n      content: t.savedContent,\n      diffMode: false\n    } : t));\n  };\n\n  // Ctrl+S hotkey\n  useHotkeys('ctrl+s, command+s', e => {\n    e.preventDefault();\n    handleSave();\n  }, [tabs, activePath]);\n\n  // ESC to cancel when diffMode\n  useHotkeys('esc', () => {\n    const tab = tabs.find(t => t.path === activePath);\n    if (tab !== null && tab !== void 0 && tab.diffMode) handleCancel();\n  }, [tabs, activePath]);\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    style: {\n      flex: 1,\n      display: 'flex',\n      flexDirection: 'column'\n    },\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        display: 'flex',\n        borderBottom: '1px solid #ccc',\n        overflowX: 'auto',\n        alignItems: 'center'\n      },\n      children: [tabs.map(tab => /*#__PURE__*/_jsxDEV(\"div\", {\n        onClick: () => setActivePath(tab.path),\n        style: {\n          padding: '4px 8px',\n          cursor: 'pointer',\n          backgroundColor: tab.path === activePath ? '#eee' : 'transparent'\n        },\n        children: tab.path.split('/').pop()\n      }, tab.path, false, {\n        fileName: _jsxFileName,\n        lineNumber: 150,\n        columnNumber: 11\n      }, this)), activePath && /*#__PURE__*/_jsxDEV(\"button\", {\n        style: {\n          marginLeft: 'auto',\n          marginRight: 8\n        },\n        onClick: handleSave,\n        children: \"Save\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 163,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 148,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        flex: 1,\n        position: 'relative'\n      },\n      children: tabs.length > 0 && activePath && (() => {\n        const current = tabs.find(t => t.path === activePath);\n        if (!current) return null;\n        if (current.diffMode) {\n          return /*#__PURE__*/_jsxDEV(_Fragment, {\n            children: [/*#__PURE__*/_jsxDEV(DiffEditor, {\n              height: \"100%\",\n              original: current.savedContent,\n              modified: current.content,\n              language: \"markdown\",\n              options: {\n                renderSideBySide: true,\n                fontSize: 14\n              },\n              onChange: v => handleChange(v !== null && v !== void 0 ? v : '')\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 175,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                position: 'absolute',\n                top: 8,\n                right: 8\n              },\n              children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                style: {\n                  marginRight: 8\n                },\n                onClick: handleSave,\n                children: \"Save\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 184,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                onClick: handleCancel,\n                children: \"Cancel\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 185,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 183,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true);\n        }\n        return /*#__PURE__*/_jsxDEV(Editor, {\n          height: \"100%\",\n          defaultLanguage: \"markdown\",\n          value: current.content,\n          onChange: v => handleChange(v !== null && v !== void 0 ? v : ''),\n          options: {\n            fontSize: 14\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 191,\n          columnNumber: 13\n        }, this);\n      })()\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 168,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 147,\n    columnNumber: 5\n  }, this);\n}\n_s(EditorTabs, \"61f5QxsPF7B2VUX3JazR+XYx2xI=\", false, function () {\n  return [useHotkeys, useHotkeys];\n});\n_c = EditorTabs;\nvar _c;\n$RefreshReg$(_c, \"EditorTabs\");", "map": {"version": 3, "names": ["React", "useState", "readFile", "writeFile", "Editor", "DiffE<PERSON>or", "useHotkeys", "NiceModal", "SaveDiffModal", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "EditorTabs", "selectedFile", "projectId", "proposedEdit", "onEditApplied", "_s", "tabs", "setTabs", "activePath", "setActivePath", "useEffect", "openFile", "proposeFileEdit", "rel_path", "content", "path", "existing", "find", "t", "prev", "saved<PERSON><PERSON>nt", "diffMode", "e", "alert", "message", "newContent", "existingTab", "originalContent", "isNewFile", "fileData", "newTab", "otherTabs", "filter", "handleChange", "value", "map", "handleSave", "tab", "console", "error", "handleCancel", "preventDefault", "style", "flex", "display", "flexDirection", "children", "borderBottom", "overflowX", "alignItems", "onClick", "padding", "cursor", "backgroundColor", "split", "pop", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "marginLeft", "marginRight", "position", "length", "current", "height", "original", "modified", "language", "options", "renderSideBySide", "fontSize", "onChange", "v", "top", "right", "defaultLanguage", "_c", "$RefreshReg$"], "sources": ["D:/Documents_2/New Coding/CoderBuddy/CoderBuddy/frontend/src/components/EditorTabs.js"], "sourcesContent": ["import React, { useState } from 'react';\r\nimport { readFile, writeFile } from '../services/api';\r\nimport Editor, { DiffEditor } from '@monaco-editor/react';\r\nimport { useHotkeys } from 'react-hotkeys-hook';\r\nimport NiceModal from '@ebay/nice-modal-react';\r\nimport SaveDiffModal from './SaveDiffModal';\r\n\r\nexport default function EditorTabs({ selectedFile, projectId, proposedEdit, onEditApplied }) {\r\n  const [tabs, setTabs] = useState([]);\r\n  const [activePath, setActivePath] = useState(null);\r\n\r\n  React.useEffect(() => {\r\n    if (selectedFile) {\r\n      openFile(selectedFile);\r\n    }\r\n    // eslint-disable-next-line react-hooks/exhaustive-deps\r\n  }, [selectedFile, projectId]);\r\n\r\n  React.useEffect(() => {\r\n    if (proposedEdit) {\r\n      proposeFileEdit(proposedEdit.rel_path, proposedEdit.content);\r\n      onEditApplied(); // Signal that we've handled it\r\n    }\r\n    // eslint-disable-next-line react-hooks/exhaustive-deps\r\n  }, [proposedEdit]);\r\n\r\n  React.useEffect(() => {\r\n    setTabs([]);\r\n    setActivePath(null);\r\n  }, [projectId]);\r\n\r\n  async function openFile(path) {\r\n    // if already open, just activate\r\n    const existing = tabs.find((t) => t.path === path);\r\n    if (existing) {\r\n      setActivePath(path);\r\n      return;\r\n    }\r\n    try {\r\n      const { content } = await readFile(path, projectId);\r\n      setTabs((prev) => [\r\n        ...prev,\r\n        { path, content, savedContent: content, diffMode: false },\r\n      ]);\r\n      setActivePath(path);\r\n    } catch (e) {\r\n      alert(`Failed to open file: ${e.message}`);\r\n    }\r\n  }\r\n\r\n  async function proposeFileEdit(path, newContent) {\r\n    const existingTab = tabs.find((t) => t.path === path);\r\n    let originalContent;\r\n    let isNewFile = false;\r\n\r\n    if (existingTab) {\r\n      originalContent = existingTab.savedContent;\r\n    } else {\r\n      try {\r\n        const fileData = await readFile(path, projectId);\r\n        originalContent = fileData.content;\r\n      } catch (e) {\r\n        // It's a new file\r\n        originalContent = '';\r\n        isNewFile = true;\r\n      }\r\n    }\r\n\r\n    const newTab = {\r\n      path,\r\n      content: newContent,\r\n      savedContent: originalContent,\r\n      diffMode: true,\r\n    };\r\n\r\n    // If file isn't open, add it. Otherwise, replace it.\r\n    const otherTabs = tabs.filter((t) => t.path !== path);\r\n    setTabs([...otherTabs, newTab]);\r\n    setActivePath(path);\r\n  }\r\n\r\n  function handleChange(value = '') {\r\n    setTabs((prev) =>\r\n      prev.map((t) =>\r\n        t.path === activePath ? { ...t, content: value } : t\r\n      )\r\n    );\r\n  }\r\n\r\n  const handleSave = async () => {\r\n    if (!activePath) return;\r\n    const tab = tabs.find((t) => t.path === activePath);\r\n    if (!tab) return;\r\n\r\n    // If not in diff mode yet and file is dirty -> open diff view\r\n    if (!tab.diffMode) {\r\n      if (tab.content === tab.savedContent) return; // nothing to save\r\n      setTabs((prev) =>\r\n        prev.map((t) =>\r\n          t.path === tab.path ? { ...t, diffMode: true } : t\r\n        )\r\n      );\r\n      return;\r\n    }\r\n\r\n    // In diff mode -> actually persist\r\n    try {\r\n      await writeFile(tab.path, tab.content, true, projectId);\r\n      setTabs((prev) =>\r\n        prev.map((t) =>\r\n          t.path === tab.path\r\n            ? { ...t, savedContent: t.content, diffMode: false }\r\n            : t\r\n        )\r\n      );\r\n    } catch (e) {\r\n      console.error(e);\r\n      alert('Save failed: ' + e.message);\r\n    }\r\n  };\r\n\r\n  // Cancel diff -> revert content\r\n  const handleCancel = () => {\r\n    if (!activePath) return;\r\n    setTabs((prev) =>\r\n      prev.map((t) =>\r\n        t.path === activePath\r\n          ? { ...t, content: t.savedContent, diffMode: false }\r\n          : t\r\n      )\r\n    );\r\n  };\r\n\r\n  // Ctrl+S hotkey\r\n  useHotkeys('ctrl+s, command+s', (e) => {\r\n    e.preventDefault();\r\n    handleSave();\r\n  }, [tabs, activePath]);\r\n\r\n  // ESC to cancel when diffMode\r\n  useHotkeys('esc', () => {\r\n    const tab = tabs.find((t) => t.path === activePath);\r\n    if (tab?.diffMode) handleCancel();\r\n  }, [tabs, activePath]);\r\n\r\n  return (\r\n    <div style={{ flex: 1, display: 'flex', flexDirection: 'column' }}>\r\n      <div style={{ display: 'flex', borderBottom: '1px solid #ccc', overflowX: 'auto', alignItems: 'center' }}>\r\n        {tabs.map((tab) => (\r\n          <div\r\n            key={tab.path}\r\n            onClick={() => setActivePath(tab.path)}\r\n            style={{\r\n              padding: '4px 8px',\r\n              cursor: 'pointer',\r\n              backgroundColor: tab.path === activePath ? '#eee' : 'transparent',\r\n            }}\r\n          >\r\n            {tab.path.split('/').pop()}\r\n          </div>\r\n        ))}\r\n        {activePath && (\r\n          <button style={{ marginLeft: 'auto', marginRight: 8 }} onClick={handleSave}>\r\n            Save\r\n          </button>\r\n        )}\r\n      </div>\r\n      <div style={{ flex: 1, position: 'relative' }}>\r\n        {tabs.length > 0 && activePath && (() => {\r\n          const current = tabs.find((t) => t.path === activePath);\r\n          if (!current) return null;\r\n          if (current.diffMode) {\r\n            return (\r\n              <>\r\n                <DiffEditor\r\n                  height=\"100%\"\r\n                  original={current.savedContent}\r\n                  modified={current.content}\r\n                  language=\"markdown\"\r\n                  options={{ renderSideBySide: true, fontSize: 14 }}\r\n                  onChange={(v) => handleChange(v ?? '')}\r\n                />\r\n                <div style={{ position: 'absolute', top: 8, right: 8 }}>\r\n                  <button style={{ marginRight: 8 }} onClick={handleSave}>Save</button>\r\n                  <button onClick={handleCancel}>Cancel</button>\r\n                </div>\r\n              </>\r\n            );\r\n          }\r\n          return (\r\n            <Editor\r\n              height=\"100%\"\r\n              defaultLanguage=\"markdown\"\r\n              value={current.content}\r\n              onChange={(v) => handleChange(v ?? '')}\r\n              options={{ fontSize: 14 }}\r\n            />\r\n          );\r\n        })()}\r\n      </div>\r\n    </div>\r\n  );\r\n} "], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,SAASC,QAAQ,EAAEC,SAAS,QAAQ,iBAAiB;AACrD,OAAOC,MAAM,IAAIC,UAAU,QAAQ,sBAAsB;AACzD,SAASC,UAAU,QAAQ,oBAAoB;AAC/C,OAAOC,SAAS,MAAM,wBAAwB;AAC9C,OAAOC,aAAa,MAAM,iBAAiB;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAE5C,eAAe,SAASC,UAAUA,CAAC;EAAEC,YAAY;EAAEC,SAAS;EAAEC,YAAY;EAAEC;AAAc,CAAC,EAAE;EAAAC,EAAA;EAC3F,MAAM,CAACC,IAAI,EAAEC,OAAO,CAAC,GAAGnB,QAAQ,CAAC,EAAE,CAAC;EACpC,MAAM,CAACoB,UAAU,EAAEC,aAAa,CAAC,GAAGrB,QAAQ,CAAC,IAAI,CAAC;EAElDD,KAAK,CAACuB,SAAS,CAAC,MAAM;IACpB,IAAIT,YAAY,EAAE;MAChBU,QAAQ,CAACV,YAAY,CAAC;IACxB;IACA;EACF,CAAC,EAAE,CAACA,YAAY,EAAEC,SAAS,CAAC,CAAC;EAE7Bf,KAAK,CAACuB,SAAS,CAAC,MAAM;IACpB,IAAIP,YAAY,EAAE;MAChBS,eAAe,CAACT,YAAY,CAACU,QAAQ,EAAEV,YAAY,CAACW,OAAO,CAAC;MAC5DV,aAAa,CAAC,CAAC,CAAC,CAAC;IACnB;IACA;EACF,CAAC,EAAE,CAACD,YAAY,CAAC,CAAC;EAElBhB,KAAK,CAACuB,SAAS,CAAC,MAAM;IACpBH,OAAO,CAAC,EAAE,CAAC;IACXE,aAAa,CAAC,IAAI,CAAC;EACrB,CAAC,EAAE,CAACP,SAAS,CAAC,CAAC;EAEf,eAAeS,QAAQA,CAACI,IAAI,EAAE;IAC5B;IACA,MAAMC,QAAQ,GAAGV,IAAI,CAACW,IAAI,CAAEC,CAAC,IAAKA,CAAC,CAACH,IAAI,KAAKA,IAAI,CAAC;IAClD,IAAIC,QAAQ,EAAE;MACZP,aAAa,CAACM,IAAI,CAAC;MACnB;IACF;IACA,IAAI;MACF,MAAM;QAAED;MAAQ,CAAC,GAAG,MAAMzB,QAAQ,CAAC0B,IAAI,EAAEb,SAAS,CAAC;MACnDK,OAAO,CAAEY,IAAI,IAAK,CAChB,GAAGA,IAAI,EACP;QAAEJ,IAAI;QAAED,OAAO;QAAEM,YAAY,EAAEN,OAAO;QAAEO,QAAQ,EAAE;MAAM,CAAC,CAC1D,CAAC;MACFZ,aAAa,CAACM,IAAI,CAAC;IACrB,CAAC,CAAC,OAAOO,CAAC,EAAE;MACVC,KAAK,CAAC,wBAAwBD,CAAC,CAACE,OAAO,EAAE,CAAC;IAC5C;EACF;EAEA,eAAeZ,eAAeA,CAACG,IAAI,EAAEU,UAAU,EAAE;IAC/C,MAAMC,WAAW,GAAGpB,IAAI,CAACW,IAAI,CAAEC,CAAC,IAAKA,CAAC,CAACH,IAAI,KAAKA,IAAI,CAAC;IACrD,IAAIY,eAAe;IACnB,IAAIC,SAAS,GAAG,KAAK;IAErB,IAAIF,WAAW,EAAE;MACfC,eAAe,GAAGD,WAAW,CAACN,YAAY;IAC5C,CAAC,MAAM;MACL,IAAI;QACF,MAAMS,QAAQ,GAAG,MAAMxC,QAAQ,CAAC0B,IAAI,EAAEb,SAAS,CAAC;QAChDyB,eAAe,GAAGE,QAAQ,CAACf,OAAO;MACpC,CAAC,CAAC,OAAOQ,CAAC,EAAE;QACV;QACAK,eAAe,GAAG,EAAE;QACpBC,SAAS,GAAG,IAAI;MAClB;IACF;IAEA,MAAME,MAAM,GAAG;MACbf,IAAI;MACJD,OAAO,EAAEW,UAAU;MACnBL,YAAY,EAAEO,eAAe;MAC7BN,QAAQ,EAAE;IACZ,CAAC;;IAED;IACA,MAAMU,SAAS,GAAGzB,IAAI,CAAC0B,MAAM,CAAEd,CAAC,IAAKA,CAAC,CAACH,IAAI,KAAKA,IAAI,CAAC;IACrDR,OAAO,CAAC,CAAC,GAAGwB,SAAS,EAAED,MAAM,CAAC,CAAC;IAC/BrB,aAAa,CAACM,IAAI,CAAC;EACrB;EAEA,SAASkB,YAAYA,CAACC,KAAK,GAAG,EAAE,EAAE;IAChC3B,OAAO,CAAEY,IAAI,IACXA,IAAI,CAACgB,GAAG,CAAEjB,CAAC,IACTA,CAAC,CAACH,IAAI,KAAKP,UAAU,GAAG;MAAE,GAAGU,CAAC;MAAEJ,OAAO,EAAEoB;IAAM,CAAC,GAAGhB,CACrD,CACF,CAAC;EACH;EAEA,MAAMkB,UAAU,GAAG,MAAAA,CAAA,KAAY;IAC7B,IAAI,CAAC5B,UAAU,EAAE;IACjB,MAAM6B,GAAG,GAAG/B,IAAI,CAACW,IAAI,CAAEC,CAAC,IAAKA,CAAC,CAACH,IAAI,KAAKP,UAAU,CAAC;IACnD,IAAI,CAAC6B,GAAG,EAAE;;IAEV;IACA,IAAI,CAACA,GAAG,CAAChB,QAAQ,EAAE;MACjB,IAAIgB,GAAG,CAACvB,OAAO,KAAKuB,GAAG,CAACjB,YAAY,EAAE,OAAO,CAAC;MAC9Cb,OAAO,CAAEY,IAAI,IACXA,IAAI,CAACgB,GAAG,CAAEjB,CAAC,IACTA,CAAC,CAACH,IAAI,KAAKsB,GAAG,CAACtB,IAAI,GAAG;QAAE,GAAGG,CAAC;QAAEG,QAAQ,EAAE;MAAK,CAAC,GAAGH,CACnD,CACF,CAAC;MACD;IACF;;IAEA;IACA,IAAI;MACF,MAAM5B,SAAS,CAAC+C,GAAG,CAACtB,IAAI,EAAEsB,GAAG,CAACvB,OAAO,EAAE,IAAI,EAAEZ,SAAS,CAAC;MACvDK,OAAO,CAAEY,IAAI,IACXA,IAAI,CAACgB,GAAG,CAAEjB,CAAC,IACTA,CAAC,CAACH,IAAI,KAAKsB,GAAG,CAACtB,IAAI,GACf;QAAE,GAAGG,CAAC;QAAEE,YAAY,EAAEF,CAAC,CAACJ,OAAO;QAAEO,QAAQ,EAAE;MAAM,CAAC,GAClDH,CACN,CACF,CAAC;IACH,CAAC,CAAC,OAAOI,CAAC,EAAE;MACVgB,OAAO,CAACC,KAAK,CAACjB,CAAC,CAAC;MAChBC,KAAK,CAAC,eAAe,GAAGD,CAAC,CAACE,OAAO,CAAC;IACpC;EACF,CAAC;;EAED;EACA,MAAMgB,YAAY,GAAGA,CAAA,KAAM;IACzB,IAAI,CAAChC,UAAU,EAAE;IACjBD,OAAO,CAAEY,IAAI,IACXA,IAAI,CAACgB,GAAG,CAAEjB,CAAC,IACTA,CAAC,CAACH,IAAI,KAAKP,UAAU,GACjB;MAAE,GAAGU,CAAC;MAAEJ,OAAO,EAAEI,CAAC,CAACE,YAAY;MAAEC,QAAQ,EAAE;IAAM,CAAC,GAClDH,CACN,CACF,CAAC;EACH,CAAC;;EAED;EACAzB,UAAU,CAAC,mBAAmB,EAAG6B,CAAC,IAAK;IACrCA,CAAC,CAACmB,cAAc,CAAC,CAAC;IAClBL,UAAU,CAAC,CAAC;EACd,CAAC,EAAE,CAAC9B,IAAI,EAAEE,UAAU,CAAC,CAAC;;EAEtB;EACAf,UAAU,CAAC,KAAK,EAAE,MAAM;IACtB,MAAM4C,GAAG,GAAG/B,IAAI,CAACW,IAAI,CAAEC,CAAC,IAAKA,CAAC,CAACH,IAAI,KAAKP,UAAU,CAAC;IACnD,IAAI6B,GAAG,aAAHA,GAAG,eAAHA,GAAG,CAAEhB,QAAQ,EAAEmB,YAAY,CAAC,CAAC;EACnC,CAAC,EAAE,CAAClC,IAAI,EAAEE,UAAU,CAAC,CAAC;EAEtB,oBACEX,OAAA;IAAK6C,KAAK,EAAE;MAAEC,IAAI,EAAE,CAAC;MAAEC,OAAO,EAAE,MAAM;MAAEC,aAAa,EAAE;IAAS,CAAE;IAAAC,QAAA,gBAChEjD,OAAA;MAAK6C,KAAK,EAAE;QAAEE,OAAO,EAAE,MAAM;QAAEG,YAAY,EAAE,gBAAgB;QAAEC,SAAS,EAAE,MAAM;QAAEC,UAAU,EAAE;MAAS,CAAE;MAAAH,QAAA,GACtGxC,IAAI,CAAC6B,GAAG,CAAEE,GAAG,iBACZxC,OAAA;QAEEqD,OAAO,EAAEA,CAAA,KAAMzC,aAAa,CAAC4B,GAAG,CAACtB,IAAI,CAAE;QACvC2B,KAAK,EAAE;UACLS,OAAO,EAAE,SAAS;UAClBC,MAAM,EAAE,SAAS;UACjBC,eAAe,EAAEhB,GAAG,CAACtB,IAAI,KAAKP,UAAU,GAAG,MAAM,GAAG;QACtD,CAAE;QAAAsC,QAAA,EAEDT,GAAG,CAACtB,IAAI,CAACuC,KAAK,CAAC,GAAG,CAAC,CAACC,GAAG,CAAC;MAAC,GARrBlB,GAAG,CAACtB,IAAI;QAAAyC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OASV,CACN,CAAC,EACDnD,UAAU,iBACTX,OAAA;QAAQ6C,KAAK,EAAE;UAAEkB,UAAU,EAAE,MAAM;UAAEC,WAAW,EAAE;QAAE,CAAE;QAACX,OAAO,EAAEd,UAAW;QAAAU,QAAA,EAAC;MAE5E;QAAAU,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CACT;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC,eACN9D,OAAA;MAAK6C,KAAK,EAAE;QAAEC,IAAI,EAAE,CAAC;QAAEmB,QAAQ,EAAE;MAAW,CAAE;MAAAhB,QAAA,EAC3CxC,IAAI,CAACyD,MAAM,GAAG,CAAC,IAAIvD,UAAU,IAAI,CAAC,MAAM;QACvC,MAAMwD,OAAO,GAAG1D,IAAI,CAACW,IAAI,CAAEC,CAAC,IAAKA,CAAC,CAACH,IAAI,KAAKP,UAAU,CAAC;QACvD,IAAI,CAACwD,OAAO,EAAE,OAAO,IAAI;QACzB,IAAIA,OAAO,CAAC3C,QAAQ,EAAE;UACpB,oBACExB,OAAA,CAAAE,SAAA;YAAA+C,QAAA,gBACEjD,OAAA,CAACL,UAAU;cACTyE,MAAM,EAAC,MAAM;cACbC,QAAQ,EAAEF,OAAO,CAAC5C,YAAa;cAC/B+C,QAAQ,EAAEH,OAAO,CAAClD,OAAQ;cAC1BsD,QAAQ,EAAC,UAAU;cACnBC,OAAO,EAAE;gBAAEC,gBAAgB,EAAE,IAAI;gBAAEC,QAAQ,EAAE;cAAG,CAAE;cAClDC,QAAQ,EAAGC,CAAC,IAAKxC,YAAY,CAACwC,CAAC,aAADA,CAAC,cAADA,CAAC,GAAI,EAAE;YAAE;cAAAjB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACxC,CAAC,eACF9D,OAAA;cAAK6C,KAAK,EAAE;gBAAEoB,QAAQ,EAAE,UAAU;gBAAEY,GAAG,EAAE,CAAC;gBAAEC,KAAK,EAAE;cAAE,CAAE;cAAA7B,QAAA,gBACrDjD,OAAA;gBAAQ6C,KAAK,EAAE;kBAAEmB,WAAW,EAAE;gBAAE,CAAE;gBAACX,OAAO,EAAEd,UAAW;gBAAAU,QAAA,EAAC;cAAI;gBAAAU,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eACrE9D,OAAA;gBAAQqD,OAAO,EAAEV,YAAa;gBAAAM,QAAA,EAAC;cAAM;gBAAAU,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC3C,CAAC;UAAA,eACN,CAAC;QAEP;QACA,oBACE9D,OAAA,CAACN,MAAM;UACL0E,MAAM,EAAC,MAAM;UACbW,eAAe,EAAC,UAAU;UAC1B1C,KAAK,EAAE8B,OAAO,CAAClD,OAAQ;UACvB0D,QAAQ,EAAGC,CAAC,IAAKxC,YAAY,CAACwC,CAAC,aAADA,CAAC,cAADA,CAAC,GAAI,EAAE,CAAE;UACvCJ,OAAO,EAAE;YAAEE,QAAQ,EAAE;UAAG;QAAE;UAAAf,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC3B,CAAC;MAEN,CAAC,EAAE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACD,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV;AAACtD,EAAA,CAnMuBL,UAAU;EAAA,QA+HhCP,UAAU,EAMVA,UAAU;AAAA;AAAAoF,EAAA,GArIY7E,UAAU;AAAA,IAAA6E,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}