{"ast": null, "code": "import { loader } from '@monaco-editor/react';\nloader.config({\n  paths: {\n    vs: 'https://unpkg.com/monaco-editor@0.45.0/min/vs'\n  }\n});", "map": {"version": 3, "names": ["loader", "config", "paths", "vs"], "sources": ["D:/Documents_2/New Coding/CoderBuddy/CoderBuddy/frontend/src/monacoLoaderConfig.js"], "sourcesContent": ["import { loader } from '@monaco-editor/react';\r\n\r\nloader.config({\r\n  paths: {\r\n    vs: 'https://unpkg.com/monaco-editor@0.45.0/min/vs',\r\n  },\r\n}); "], "mappings": "AAAA,SAASA,MAAM,QAAQ,sBAAsB;AAE7CA,MAAM,CAACC,MAAM,CAAC;EACZC,KAAK,EAAE;IACLC,EAAE,EAAE;EACN;AACF,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}