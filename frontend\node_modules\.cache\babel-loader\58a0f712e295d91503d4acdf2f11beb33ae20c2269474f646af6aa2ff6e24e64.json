{"ast": null, "code": "var config = {\n  paths: {\n    vs: 'https://cdn.jsdelivr.net/npm/monaco-editor@0.52.2/min/vs'\n  }\n};\nexport default config;", "map": {"version": 3, "names": ["config", "paths", "vs"], "sources": ["D:/Documents_2/New Coding/CoderBuddy/CoderBuddy/frontend/node_modules/@monaco-editor/loader/lib/es/config/index.js"], "sourcesContent": ["var config = {\n  paths: {\n    vs: 'https://cdn.jsdelivr.net/npm/monaco-editor@0.52.2/min/vs'\n  }\n};\n\nexport default config;\n"], "mappings": "AAAA,IAAIA,MAAM,GAAG;EACXC,KAAK,EAAE;IACLC,EAAE,EAAE;EACN;AACF,CAAC;AAED,eAAeF,MAAM", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}