{"ast": null, "code": "import * as diff from \"diff\";\nconst jsDiff = diff;\nexport var DiffType;\n(function (DiffType) {\n  DiffType[DiffType[\"DEFAULT\"] = 0] = \"DEFAULT\";\n  DiffType[DiffType[\"ADDED\"] = 1] = \"ADDED\";\n  DiffType[DiffType[\"REMOVED\"] = 2] = \"REMOVED\";\n  DiffType[DiffType[\"CHANGED\"] = 3] = \"CHANGED\";\n})(DiffType || (DiffType = {}));\n// See https://github.com/kpdecker/jsdiff/tree/v4.0.1#api for more info on the below JsDiff methods\nexport var DiffMethod;\n(function (DiffMethod) {\n  DiffMethod[\"CHARS\"] = \"diffChars\";\n  DiffMethod[\"WORDS\"] = \"diffWords\";\n  DiffMethod[\"WORDS_WITH_SPACE\"] = \"diffWordsWithSpace\";\n  DiffMethod[\"LINES\"] = \"diffLines\";\n  DiffMethod[\"TRIMMED_LINES\"] = \"diffTrimmedLines\";\n  DiffMethod[\"SENTENCES\"] = \"diffSentences\";\n  DiffMethod[\"CSS\"] = \"diffCss\";\n  DiffMethod[\"JSON\"] = \"diffJson\";\n})(DiffMethod || (DiffMethod = {}));\n/**\n * Splits diff text by new line and computes final list of diff lines based on\n * conditions.\n *\n * @param value Diff text from the js diff module.\n */\nconst constructLines = value => {\n  if (value === \"\") return [];\n  const lines = value.replace(/\\n$/, \"\").split(\"\\n\");\n  return lines;\n};\n/**\n * Computes word diff information in the line.\n * [TODO]: Consider adding options argument for JsDiff text block comparison\n *\n * @param oldValue Old word in the line.\n * @param newValue New word in the line.\n * @param compareMethod JsDiff text diff method from https://github.com/kpdecker/jsdiff/tree/v4.0.1#api\n */\nconst computeDiff = (oldValue, newValue, compareMethod = DiffMethod.CHARS) => {\n  const compareFunc = typeof compareMethod === \"string\" ? jsDiff[compareMethod] : compareMethod;\n  const diffArray = compareFunc(oldValue, newValue);\n  const computedDiff = {\n    left: [],\n    right: []\n  };\n  diffArray.forEach(({\n    added,\n    removed,\n    value\n  }) => {\n    const diffInformation = {};\n    if (added) {\n      diffInformation.type = DiffType.ADDED;\n      diffInformation.value = value;\n      computedDiff.right.push(diffInformation);\n    }\n    if (removed) {\n      diffInformation.type = DiffType.REMOVED;\n      diffInformation.value = value;\n      computedDiff.left.push(diffInformation);\n    }\n    if (!removed && !added) {\n      diffInformation.type = DiffType.DEFAULT;\n      diffInformation.value = value;\n      computedDiff.right.push(diffInformation);\n      computedDiff.left.push(diffInformation);\n    }\n    return diffInformation;\n  });\n  return computedDiff;\n};\n/**\n * [TODO]: Think about moving common left and right value assignment to a\n * common place. Better readability?\n *\n * Computes line wise information based in the js diff information passed. Each\n * line contains information about left and right section. Left side denotes\n * deletion and right side denotes addition.\n *\n * @param oldString Old string to compare.\n * @param newString New string to compare with old string.\n * @param disableWordDiff Flag to enable/disable word diff.\n * @param lineCompareMethod JsDiff text diff method from https://github.com/kpdecker/jsdiff/tree/v4.0.1#api\n * @param linesOffset line number to start counting from\n * @param showLines lines that are always shown, regardless of diff\n */\nconst computeLineInformation = (oldString, newString, disableWordDiff = false, lineCompareMethod = DiffMethod.CHARS, linesOffset = 0, showLines = []) => {\n  let diffArray = [];\n  // Use diffLines for strings, and diffJson for objects...\n  if (typeof oldString === \"string\" && typeof newString === \"string\") {\n    diffArray = diff.diffLines(oldString, newString, {\n      newlineIsToken: false,\n      ignoreWhitespace: false,\n      ignoreCase: false\n    });\n  } else {\n    diffArray = diff.diffJson(oldString, newString);\n  }\n  let rightLineNumber = linesOffset;\n  let leftLineNumber = linesOffset;\n  let lineInformation = [];\n  let counter = 0;\n  const diffLines = [];\n  const ignoreDiffIndexes = [];\n  const getLineInformation = (value, diffIndex, added, removed, evaluateOnlyFirstLine) => {\n    const lines = constructLines(value);\n    return lines.map((line, lineIndex) => {\n      const left = {};\n      const right = {};\n      if (ignoreDiffIndexes.includes(`${diffIndex}-${lineIndex}`) || evaluateOnlyFirstLine && lineIndex !== 0) {\n        return undefined;\n      }\n      if (added || removed) {\n        let countAsChange = true;\n        if (removed) {\n          leftLineNumber += 1;\n          left.lineNumber = leftLineNumber;\n          left.type = DiffType.REMOVED;\n          left.value = line || \" \";\n          // When the current line is of type REMOVED, check the next item in\n          // the diff array whether it is of type ADDED. If true, the current\n          // diff will be marked as both REMOVED and ADDED. Meaning, the\n          // current line is a modification.\n          const nextDiff = diffArray[diffIndex + 1];\n          if (nextDiff?.added) {\n            const nextDiffLines = constructLines(nextDiff.value)[lineIndex];\n            if (nextDiffLines) {\n              const nextDiffLineInfo = getLineInformation(nextDiffLines, diffIndex, true, false, true);\n              const {\n                value: rightValue,\n                lineNumber,\n                type\n              } = nextDiffLineInfo[0].right;\n              // When identified as modification, push the next diff to ignore\n              // list as the next value will be added in this line computation as\n              // right and left values.\n              ignoreDiffIndexes.push(`${diffIndex + 1}-${lineIndex}`);\n              right.lineNumber = lineNumber;\n              if (left.value === rightValue) {\n                // The new value is exactly the same as the old\n                countAsChange = false;\n                right.type = 0;\n                left.type = 0;\n                right.value = rightValue;\n              } else {\n                right.type = type;\n                // Do char level diff and assign the corresponding values to the\n                // left and right diff information object.\n                if (disableWordDiff) {\n                  right.value = rightValue;\n                } else {\n                  const computedDiff = computeDiff(line, rightValue, lineCompareMethod);\n                  right.value = computedDiff.right;\n                  left.value = computedDiff.left;\n                }\n              }\n            }\n          }\n        } else {\n          rightLineNumber += 1;\n          right.lineNumber = rightLineNumber;\n          right.type = DiffType.ADDED;\n          right.value = line;\n        }\n        if (countAsChange && !evaluateOnlyFirstLine) {\n          if (!diffLines.includes(counter)) {\n            diffLines.push(counter);\n          }\n        }\n      } else {\n        leftLineNumber += 1;\n        rightLineNumber += 1;\n        left.lineNumber = leftLineNumber;\n        left.type = DiffType.DEFAULT;\n        left.value = line;\n        right.lineNumber = rightLineNumber;\n        right.type = DiffType.DEFAULT;\n        right.value = line;\n      }\n      if (showLines?.includes(`L-${left.lineNumber}`) || showLines?.includes(`R-${right.lineNumber}`) && !diffLines.includes(counter)) {\n        diffLines.push(counter);\n      }\n      if (!evaluateOnlyFirstLine) {\n        counter += 1;\n      }\n      return {\n        right,\n        left\n      };\n    }).filter(Boolean);\n  };\n  diffArray.forEach(({\n    added,\n    removed,\n    value\n  }, index) => {\n    lineInformation = [...lineInformation, ...getLineInformation(value, index, added, removed)];\n  });\n  return {\n    lineInformation,\n    diffLines\n  };\n};\nexport { computeLineInformation };", "map": {"version": 3, "names": ["diff", "jsDiff", "DiffType", "DiffMethod", "constructLines", "value", "lines", "replace", "split", "computeDiff", "oldValue", "newValue", "compareMethod", "CHARS", "compareFunc", "diffArray", "computedDiff", "left", "right", "for<PERSON>ach", "added", "removed", "diffInformation", "type", "ADDED", "push", "REMOVED", "DEFAULT", "computeLineInformation", "oldString", "newString", "disable<PERSON><PERSON><PERSON><PERSON>", "lineCompareMethod", "linesOffset", "showLines", "diffLines", "newlineIsToken", "ignoreWhitespace", "ignoreCase", "diff<PERSON><PERSON>", "rightLineNumber", "leftLineNumber", "lineInformation", "counter", "ignoreDiffIndexes", "getLineInformation", "diffIndex", "evaluateOnlyFirstLine", "map", "line", "lineIndex", "includes", "undefined", "count<PERSON><PERSON><PERSON><PERSON>", "lineNumber", "nextDiff", "nextDiffLines", "nextDiffLineInfo", "rightValue", "filter", "Boolean", "index"], "sources": ["D:/Documents_2/New Coding/CoderBuddy/CoderBuddy/frontend/node_modules/react-diff-viewer-continued/lib/esm/src/compute-lines.js"], "sourcesContent": ["import * as diff from \"diff\";\nconst jsDiff = diff;\nexport var DiffType;\n(function (DiffType) {\n    DiffType[DiffType[\"DEFAULT\"] = 0] = \"DEFAULT\";\n    DiffType[DiffType[\"ADDED\"] = 1] = \"ADDED\";\n    DiffType[DiffType[\"REMOVED\"] = 2] = \"REMOVED\";\n    DiffType[DiffType[\"CHANGED\"] = 3] = \"CHANGED\";\n})(DiffType || (DiffType = {}));\n// See https://github.com/kpdecker/jsdiff/tree/v4.0.1#api for more info on the below JsDiff methods\nexport var DiffMethod;\n(function (DiffMethod) {\n    DiffMethod[\"CHARS\"] = \"diffChars\";\n    DiffMethod[\"WORDS\"] = \"diffWords\";\n    DiffMethod[\"WORDS_WITH_SPACE\"] = \"diffWordsWithSpace\";\n    DiffMethod[\"LINES\"] = \"diffLines\";\n    DiffMethod[\"TRIMMED_LINES\"] = \"diffTrimmedLines\";\n    DiffMethod[\"SENTENCES\"] = \"diffSentences\";\n    DiffMethod[\"CSS\"] = \"diffCss\";\n    DiffMethod[\"JSON\"] = \"diffJson\";\n})(DiffMethod || (DiffMethod = {}));\n/**\n * Splits diff text by new line and computes final list of diff lines based on\n * conditions.\n *\n * @param value Diff text from the js diff module.\n */\nconst constructLines = (value) => {\n    if (value === \"\")\n        return [];\n    const lines = value.replace(/\\n$/, \"\").split(\"\\n\");\n    return lines;\n};\n/**\n * Computes word diff information in the line.\n * [TODO]: Consider adding options argument for JsDiff text block comparison\n *\n * @param oldValue Old word in the line.\n * @param newValue New word in the line.\n * @param compareMethod JsDiff text diff method from https://github.com/kpdecker/jsdiff/tree/v4.0.1#api\n */\nconst computeDiff = (oldValue, newValue, compareMethod = DiffMethod.CHARS) => {\n    const compareFunc = typeof compareMethod === \"string\" ? jsDiff[compareMethod] : compareMethod;\n    const diffArray = compareFunc(oldValue, newValue);\n    const computedDiff = {\n        left: [],\n        right: [],\n    };\n    diffArray.forEach(({ added, removed, value }) => {\n        const diffInformation = {};\n        if (added) {\n            diffInformation.type = DiffType.ADDED;\n            diffInformation.value = value;\n            computedDiff.right.push(diffInformation);\n        }\n        if (removed) {\n            diffInformation.type = DiffType.REMOVED;\n            diffInformation.value = value;\n            computedDiff.left.push(diffInformation);\n        }\n        if (!removed && !added) {\n            diffInformation.type = DiffType.DEFAULT;\n            diffInformation.value = value;\n            computedDiff.right.push(diffInformation);\n            computedDiff.left.push(diffInformation);\n        }\n        return diffInformation;\n    });\n    return computedDiff;\n};\n/**\n * [TODO]: Think about moving common left and right value assignment to a\n * common place. Better readability?\n *\n * Computes line wise information based in the js diff information passed. Each\n * line contains information about left and right section. Left side denotes\n * deletion and right side denotes addition.\n *\n * @param oldString Old string to compare.\n * @param newString New string to compare with old string.\n * @param disableWordDiff Flag to enable/disable word diff.\n * @param lineCompareMethod JsDiff text diff method from https://github.com/kpdecker/jsdiff/tree/v4.0.1#api\n * @param linesOffset line number to start counting from\n * @param showLines lines that are always shown, regardless of diff\n */\nconst computeLineInformation = (oldString, newString, disableWordDiff = false, lineCompareMethod = DiffMethod.CHARS, linesOffset = 0, showLines = []) => {\n    let diffArray = [];\n    // Use diffLines for strings, and diffJson for objects...\n    if (typeof oldString === \"string\" && typeof newString === \"string\") {\n        diffArray = diff.diffLines(oldString, newString, {\n            newlineIsToken: false,\n            ignoreWhitespace: false,\n            ignoreCase: false,\n        });\n    }\n    else {\n        diffArray = diff.diffJson(oldString, newString);\n    }\n    let rightLineNumber = linesOffset;\n    let leftLineNumber = linesOffset;\n    let lineInformation = [];\n    let counter = 0;\n    const diffLines = [];\n    const ignoreDiffIndexes = [];\n    const getLineInformation = (value, diffIndex, added, removed, evaluateOnlyFirstLine) => {\n        const lines = constructLines(value);\n        return lines\n            .map((line, lineIndex) => {\n            const left = {};\n            const right = {};\n            if (ignoreDiffIndexes.includes(`${diffIndex}-${lineIndex}`) ||\n                (evaluateOnlyFirstLine && lineIndex !== 0)) {\n                return undefined;\n            }\n            if (added || removed) {\n                let countAsChange = true;\n                if (removed) {\n                    leftLineNumber += 1;\n                    left.lineNumber = leftLineNumber;\n                    left.type = DiffType.REMOVED;\n                    left.value = line || \" \";\n                    // When the current line is of type REMOVED, check the next item in\n                    // the diff array whether it is of type ADDED. If true, the current\n                    // diff will be marked as both REMOVED and ADDED. Meaning, the\n                    // current line is a modification.\n                    const nextDiff = diffArray[diffIndex + 1];\n                    if (nextDiff?.added) {\n                        const nextDiffLines = constructLines(nextDiff.value)[lineIndex];\n                        if (nextDiffLines) {\n                            const nextDiffLineInfo = getLineInformation(nextDiffLines, diffIndex, true, false, true);\n                            const { value: rightValue, lineNumber, type, } = nextDiffLineInfo[0].right;\n                            // When identified as modification, push the next diff to ignore\n                            // list as the next value will be added in this line computation as\n                            // right and left values.\n                            ignoreDiffIndexes.push(`${diffIndex + 1}-${lineIndex}`);\n                            right.lineNumber = lineNumber;\n                            if (left.value === rightValue) {\n                                // The new value is exactly the same as the old\n                                countAsChange = false;\n                                right.type = 0;\n                                left.type = 0;\n                                right.value = rightValue;\n                            }\n                            else {\n                                right.type = type;\n                                // Do char level diff and assign the corresponding values to the\n                                // left and right diff information object.\n                                if (disableWordDiff) {\n                                    right.value = rightValue;\n                                }\n                                else {\n                                    const computedDiff = computeDiff(line, rightValue, lineCompareMethod);\n                                    right.value = computedDiff.right;\n                                    left.value = computedDiff.left;\n                                }\n                            }\n                        }\n                    }\n                }\n                else {\n                    rightLineNumber += 1;\n                    right.lineNumber = rightLineNumber;\n                    right.type = DiffType.ADDED;\n                    right.value = line;\n                }\n                if (countAsChange && !evaluateOnlyFirstLine) {\n                    if (!diffLines.includes(counter)) {\n                        diffLines.push(counter);\n                    }\n                }\n            }\n            else {\n                leftLineNumber += 1;\n                rightLineNumber += 1;\n                left.lineNumber = leftLineNumber;\n                left.type = DiffType.DEFAULT;\n                left.value = line;\n                right.lineNumber = rightLineNumber;\n                right.type = DiffType.DEFAULT;\n                right.value = line;\n            }\n            if (showLines?.includes(`L-${left.lineNumber}`) ||\n                (showLines?.includes(`R-${right.lineNumber}`) &&\n                    !diffLines.includes(counter))) {\n                diffLines.push(counter);\n            }\n            if (!evaluateOnlyFirstLine) {\n                counter += 1;\n            }\n            return { right, left };\n        })\n            .filter(Boolean);\n    };\n    diffArray.forEach(({ added, removed, value }, index) => {\n        lineInformation = [\n            ...lineInformation,\n            ...getLineInformation(value, index, added, removed),\n        ];\n    });\n    return {\n        lineInformation,\n        diffLines,\n    };\n};\nexport { computeLineInformation };\n"], "mappings": "AAAA,OAAO,KAAKA,IAAI,MAAM,MAAM;AAC5B,MAAMC,MAAM,GAAGD,IAAI;AACnB,OAAO,IAAIE,QAAQ;AACnB,CAAC,UAAUA,QAAQ,EAAE;EACjBA,QAAQ,CAACA,QAAQ,CAAC,SAAS,CAAC,GAAG,CAAC,CAAC,GAAG,SAAS;EAC7CA,QAAQ,CAACA,QAAQ,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC,GAAG,OAAO;EACzCA,QAAQ,CAACA,QAAQ,CAAC,SAAS,CAAC,GAAG,CAAC,CAAC,GAAG,SAAS;EAC7CA,QAAQ,CAACA,QAAQ,CAAC,SAAS,CAAC,GAAG,CAAC,CAAC,GAAG,SAAS;AACjD,CAAC,EAAEA,QAAQ,KAAKA,QAAQ,GAAG,CAAC,CAAC,CAAC,CAAC;AAC/B;AACA,OAAO,IAAIC,UAAU;AACrB,CAAC,UAAUA,UAAU,EAAE;EACnBA,UAAU,CAAC,OAAO,CAAC,GAAG,WAAW;EACjCA,UAAU,CAAC,OAAO,CAAC,GAAG,WAAW;EACjCA,UAAU,CAAC,kBAAkB,CAAC,GAAG,oBAAoB;EACrDA,UAAU,CAAC,OAAO,CAAC,GAAG,WAAW;EACjCA,UAAU,CAAC,eAAe,CAAC,GAAG,kBAAkB;EAChDA,UAAU,CAAC,WAAW,CAAC,GAAG,eAAe;EACzCA,UAAU,CAAC,KAAK,CAAC,GAAG,SAAS;EAC7BA,UAAU,CAAC,MAAM,CAAC,GAAG,UAAU;AACnC,CAAC,EAAEA,UAAU,KAAKA,UAAU,GAAG,CAAC,CAAC,CAAC,CAAC;AACnC;AACA;AACA;AACA;AACA;AACA;AACA,MAAMC,cAAc,GAAIC,KAAK,IAAK;EAC9B,IAAIA,KAAK,KAAK,EAAE,EACZ,OAAO,EAAE;EACb,MAAMC,KAAK,GAAGD,KAAK,CAACE,OAAO,CAAC,KAAK,EAAE,EAAE,CAAC,CAACC,KAAK,CAAC,IAAI,CAAC;EAClD,OAAOF,KAAK;AAChB,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMG,WAAW,GAAGA,CAACC,QAAQ,EAAEC,QAAQ,EAAEC,aAAa,GAAGT,UAAU,CAACU,KAAK,KAAK;EAC1E,MAAMC,WAAW,GAAG,OAAOF,aAAa,KAAK,QAAQ,GAAGX,MAAM,CAACW,aAAa,CAAC,GAAGA,aAAa;EAC7F,MAAMG,SAAS,GAAGD,WAAW,CAACJ,QAAQ,EAAEC,QAAQ,CAAC;EACjD,MAAMK,YAAY,GAAG;IACjBC,IAAI,EAAE,EAAE;IACRC,KAAK,EAAE;EACX,CAAC;EACDH,SAAS,CAACI,OAAO,CAAC,CAAC;IAAEC,KAAK;IAAEC,OAAO;IAAEhB;EAAM,CAAC,KAAK;IAC7C,MAAMiB,eAAe,GAAG,CAAC,CAAC;IAC1B,IAAIF,KAAK,EAAE;MACPE,eAAe,CAACC,IAAI,GAAGrB,QAAQ,CAACsB,KAAK;MACrCF,eAAe,CAACjB,KAAK,GAAGA,KAAK;MAC7BW,YAAY,CAACE,KAAK,CAACO,IAAI,CAACH,eAAe,CAAC;IAC5C;IACA,IAAID,OAAO,EAAE;MACTC,eAAe,CAACC,IAAI,GAAGrB,QAAQ,CAACwB,OAAO;MACvCJ,eAAe,CAACjB,KAAK,GAAGA,KAAK;MAC7BW,YAAY,CAACC,IAAI,CAACQ,IAAI,CAACH,eAAe,CAAC;IAC3C;IACA,IAAI,CAACD,OAAO,IAAI,CAACD,KAAK,EAAE;MACpBE,eAAe,CAACC,IAAI,GAAGrB,QAAQ,CAACyB,OAAO;MACvCL,eAAe,CAACjB,KAAK,GAAGA,KAAK;MAC7BW,YAAY,CAACE,KAAK,CAACO,IAAI,CAACH,eAAe,CAAC;MACxCN,YAAY,CAACC,IAAI,CAACQ,IAAI,CAACH,eAAe,CAAC;IAC3C;IACA,OAAOA,eAAe;EAC1B,CAAC,CAAC;EACF,OAAON,YAAY;AACvB,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMY,sBAAsB,GAAGA,CAACC,SAAS,EAAEC,SAAS,EAAEC,eAAe,GAAG,KAAK,EAAEC,iBAAiB,GAAG7B,UAAU,CAACU,KAAK,EAAEoB,WAAW,GAAG,CAAC,EAAEC,SAAS,GAAG,EAAE,KAAK;EACrJ,IAAInB,SAAS,GAAG,EAAE;EAClB;EACA,IAAI,OAAOc,SAAS,KAAK,QAAQ,IAAI,OAAOC,SAAS,KAAK,QAAQ,EAAE;IAChEf,SAAS,GAAGf,IAAI,CAACmC,SAAS,CAACN,SAAS,EAAEC,SAAS,EAAE;MAC7CM,cAAc,EAAE,KAAK;MACrBC,gBAAgB,EAAE,KAAK;MACvBC,UAAU,EAAE;IAChB,CAAC,CAAC;EACN,CAAC,MACI;IACDvB,SAAS,GAAGf,IAAI,CAACuC,QAAQ,CAACV,SAAS,EAAEC,SAAS,CAAC;EACnD;EACA,IAAIU,eAAe,GAAGP,WAAW;EACjC,IAAIQ,cAAc,GAAGR,WAAW;EAChC,IAAIS,eAAe,GAAG,EAAE;EACxB,IAAIC,OAAO,GAAG,CAAC;EACf,MAAMR,SAAS,GAAG,EAAE;EACpB,MAAMS,iBAAiB,GAAG,EAAE;EAC5B,MAAMC,kBAAkB,GAAGA,CAACxC,KAAK,EAAEyC,SAAS,EAAE1B,KAAK,EAAEC,OAAO,EAAE0B,qBAAqB,KAAK;IACpF,MAAMzC,KAAK,GAAGF,cAAc,CAACC,KAAK,CAAC;IACnC,OAAOC,KAAK,CACP0C,GAAG,CAAC,CAACC,IAAI,EAAEC,SAAS,KAAK;MAC1B,MAAMjC,IAAI,GAAG,CAAC,CAAC;MACf,MAAMC,KAAK,GAAG,CAAC,CAAC;MAChB,IAAI0B,iBAAiB,CAACO,QAAQ,CAAC,GAAGL,SAAS,IAAII,SAAS,EAAE,CAAC,IACtDH,qBAAqB,IAAIG,SAAS,KAAK,CAAE,EAAE;QAC5C,OAAOE,SAAS;MACpB;MACA,IAAIhC,KAAK,IAAIC,OAAO,EAAE;QAClB,IAAIgC,aAAa,GAAG,IAAI;QACxB,IAAIhC,OAAO,EAAE;UACToB,cAAc,IAAI,CAAC;UACnBxB,IAAI,CAACqC,UAAU,GAAGb,cAAc;UAChCxB,IAAI,CAACM,IAAI,GAAGrB,QAAQ,CAACwB,OAAO;UAC5BT,IAAI,CAACZ,KAAK,GAAG4C,IAAI,IAAI,GAAG;UACxB;UACA;UACA;UACA;UACA,MAAMM,QAAQ,GAAGxC,SAAS,CAAC+B,SAAS,GAAG,CAAC,CAAC;UACzC,IAAIS,QAAQ,EAAEnC,KAAK,EAAE;YACjB,MAAMoC,aAAa,GAAGpD,cAAc,CAACmD,QAAQ,CAAClD,KAAK,CAAC,CAAC6C,SAAS,CAAC;YAC/D,IAAIM,aAAa,EAAE;cACf,MAAMC,gBAAgB,GAAGZ,kBAAkB,CAACW,aAAa,EAAEV,SAAS,EAAE,IAAI,EAAE,KAAK,EAAE,IAAI,CAAC;cACxF,MAAM;gBAAEzC,KAAK,EAAEqD,UAAU;gBAAEJ,UAAU;gBAAE/B;cAAM,CAAC,GAAGkC,gBAAgB,CAAC,CAAC,CAAC,CAACvC,KAAK;cAC1E;cACA;cACA;cACA0B,iBAAiB,CAACnB,IAAI,CAAC,GAAGqB,SAAS,GAAG,CAAC,IAAII,SAAS,EAAE,CAAC;cACvDhC,KAAK,CAACoC,UAAU,GAAGA,UAAU;cAC7B,IAAIrC,IAAI,CAACZ,KAAK,KAAKqD,UAAU,EAAE;gBAC3B;gBACAL,aAAa,GAAG,KAAK;gBACrBnC,KAAK,CAACK,IAAI,GAAG,CAAC;gBACdN,IAAI,CAACM,IAAI,GAAG,CAAC;gBACbL,KAAK,CAACb,KAAK,GAAGqD,UAAU;cAC5B,CAAC,MACI;gBACDxC,KAAK,CAACK,IAAI,GAAGA,IAAI;gBACjB;gBACA;gBACA,IAAIQ,eAAe,EAAE;kBACjBb,KAAK,CAACb,KAAK,GAAGqD,UAAU;gBAC5B,CAAC,MACI;kBACD,MAAM1C,YAAY,GAAGP,WAAW,CAACwC,IAAI,EAAES,UAAU,EAAE1B,iBAAiB,CAAC;kBACrEd,KAAK,CAACb,KAAK,GAAGW,YAAY,CAACE,KAAK;kBAChCD,IAAI,CAACZ,KAAK,GAAGW,YAAY,CAACC,IAAI;gBAClC;cACJ;YACJ;UACJ;QACJ,CAAC,MACI;UACDuB,eAAe,IAAI,CAAC;UACpBtB,KAAK,CAACoC,UAAU,GAAGd,eAAe;UAClCtB,KAAK,CAACK,IAAI,GAAGrB,QAAQ,CAACsB,KAAK;UAC3BN,KAAK,CAACb,KAAK,GAAG4C,IAAI;QACtB;QACA,IAAII,aAAa,IAAI,CAACN,qBAAqB,EAAE;UACzC,IAAI,CAACZ,SAAS,CAACgB,QAAQ,CAACR,OAAO,CAAC,EAAE;YAC9BR,SAAS,CAACV,IAAI,CAACkB,OAAO,CAAC;UAC3B;QACJ;MACJ,CAAC,MACI;QACDF,cAAc,IAAI,CAAC;QACnBD,eAAe,IAAI,CAAC;QACpBvB,IAAI,CAACqC,UAAU,GAAGb,cAAc;QAChCxB,IAAI,CAACM,IAAI,GAAGrB,QAAQ,CAACyB,OAAO;QAC5BV,IAAI,CAACZ,KAAK,GAAG4C,IAAI;QACjB/B,KAAK,CAACoC,UAAU,GAAGd,eAAe;QAClCtB,KAAK,CAACK,IAAI,GAAGrB,QAAQ,CAACyB,OAAO;QAC7BT,KAAK,CAACb,KAAK,GAAG4C,IAAI;MACtB;MACA,IAAIf,SAAS,EAAEiB,QAAQ,CAAC,KAAKlC,IAAI,CAACqC,UAAU,EAAE,CAAC,IAC1CpB,SAAS,EAAEiB,QAAQ,CAAC,KAAKjC,KAAK,CAACoC,UAAU,EAAE,CAAC,IACzC,CAACnB,SAAS,CAACgB,QAAQ,CAACR,OAAO,CAAE,EAAE;QACnCR,SAAS,CAACV,IAAI,CAACkB,OAAO,CAAC;MAC3B;MACA,IAAI,CAACI,qBAAqB,EAAE;QACxBJ,OAAO,IAAI,CAAC;MAChB;MACA,OAAO;QAAEzB,KAAK;QAAED;MAAK,CAAC;IAC1B,CAAC,CAAC,CACG0C,MAAM,CAACC,OAAO,CAAC;EACxB,CAAC;EACD7C,SAAS,CAACI,OAAO,CAAC,CAAC;IAAEC,KAAK;IAAEC,OAAO;IAAEhB;EAAM,CAAC,EAAEwD,KAAK,KAAK;IACpDnB,eAAe,GAAG,CACd,GAAGA,eAAe,EAClB,GAAGG,kBAAkB,CAACxC,KAAK,EAAEwD,KAAK,EAAEzC,KAAK,EAAEC,OAAO,CAAC,CACtD;EACL,CAAC,CAAC;EACF,OAAO;IACHqB,eAAe;IACfP;EACJ,CAAC;AACL,CAAC;AACD,SAASP,sBAAsB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}