{"ast": null, "code": "import { jsx as _jsx, jsxs as _jsxs } from \"react/jsx-runtime\";\nexport function Expand() {\n  return _jsxs(\"svg\", {\n    xmlns: \"http://www.w3.org/2000/svg\",\n    viewBox: \"0 0 16 16\",\n    width: \"16\",\n    height: \"16\",\n    children: [_jsx(\"title\", {\n      children: \"expand\"\n    }), _jsx(\"path\", {\n      d: \"m8.177.677 2.896 2.896a.25.25 0 0 1-.177.427H8.75v1.25a.75.75 0 0 1-1.5 0V4H5.104a.25.25 0 0 1-.177-.427L7.823.677a.25.25 0 0 1 .354 0ZM7.25 10.75a.75.75 0 0 1 1.5 0V12h2.146a.25.25 0 0 1 .177.427l-2.896 2.896a.25.25 0 0 1-.354 0l-2.896-2.896A.25.25 0 0 1 5.104 12H7.25v-1.25Zm-5-2a.75.75 0 0 0 0-1.5h-.5a.75.75 0 0 0 0 1.5h.5ZM6 8a.75.75 0 0 1-.75.75h-.5a.75.75 0 0 1 0-1.5h.5A.75.75 0 0 1 6 8Zm2.25.75a.75.75 0 0 0 0-1.5h-.5a.75.75 0 0 0 0 1.5h.5ZM12 8a.75.75 0 0 1-.75.75h-.5a.75.75 0 0 1 0-1.5h.5A.75.75 0 0 1 12 8Zm2.25.75a.75.75 0 0 0 0-1.5h-.5a.75.75 0 0 0 0 1.5h.5Z\"\n    })]\n  });\n}", "map": {"version": 3, "names": ["jsx", "_jsx", "jsxs", "_jsxs", "Expand", "xmlns", "viewBox", "width", "height", "children", "d"], "sources": ["D:/Documents_2/New Coding/CoderBuddy/CoderBuddy/frontend/node_modules/react-diff-viewer-continued/lib/esm/src/expand.js"], "sourcesContent": ["import { jsx as _jsx, jsxs as _jsxs } from \"react/jsx-runtime\";\nexport function Expand() {\n    return (_jsxs(\"svg\", { xmlns: \"http://www.w3.org/2000/svg\", viewBox: \"0 0 16 16\", width: \"16\", height: \"16\", children: [_jsx(\"title\", { children: \"expand\" }), _jsx(\"path\", { d: \"m8.177.677 2.896 2.896a.25.25 0 0 1-.177.427H8.75v1.25a.75.75 0 0 1-1.5 0V4H5.104a.25.25 0 0 1-.177-.427L7.823.677a.25.25 0 0 1 .354 0ZM7.25 10.75a.75.75 0 0 1 1.5 0V12h2.146a.25.25 0 0 1 .177.427l-2.896 2.896a.25.25 0 0 1-.354 0l-2.896-2.896A.25.25 0 0 1 5.104 12H7.25v-1.25Zm-5-2a.75.75 0 0 0 0-1.5h-.5a.75.75 0 0 0 0 1.5h.5ZM6 8a.75.75 0 0 1-.75.75h-.5a.75.75 0 0 1 0-1.5h.5A.75.75 0 0 1 6 8Zm2.25.75a.75.75 0 0 0 0-1.5h-.5a.75.75 0 0 0 0 1.5h.5ZM12 8a.75.75 0 0 1-.75.75h-.5a.75.75 0 0 1 0-1.5h.5A.75.75 0 0 1 12 8Zm2.25.75a.75.75 0 0 0 0-1.5h-.5a.75.75 0 0 0 0 1.5h.5Z\" })] }));\n}\n"], "mappings": "AAAA,SAASA,GAAG,IAAIC,IAAI,EAAEC,IAAI,IAAIC,KAAK,QAAQ,mBAAmB;AAC9D,OAAO,SAASC,MAAMA,CAAA,EAAG;EACrB,OAAQD,KAAK,CAAC,KAAK,EAAE;IAAEE,KAAK,EAAE,4BAA4B;IAAEC,OAAO,EAAE,WAAW;IAAEC,KAAK,EAAE,IAAI;IAAEC,MAAM,EAAE,IAAI;IAAEC,QAAQ,EAAE,CAACR,IAAI,CAAC,OAAO,EAAE;MAAEQ,QAAQ,EAAE;IAAS,CAAC,CAAC,EAAER,IAAI,CAAC,MAAM,EAAE;MAAES,CAAC,EAAE;IAAgkB,CAAC,CAAC;EAAE,CAAC,CAAC;AAC3vB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}