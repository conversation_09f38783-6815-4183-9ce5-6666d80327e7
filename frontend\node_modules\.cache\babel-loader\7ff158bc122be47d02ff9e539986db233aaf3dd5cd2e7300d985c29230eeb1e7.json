{"ast": null, "code": "var _jsxFileName = \"D:\\\\Documents_2\\\\New Coding\\\\CoderBuddy\\\\CoderBuddy\\\\frontend\\\\src\\\\components\\\\ChatPanel.js\",\n  _s = $RefreshSig$();\nimport React, { useState } from 'react';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nexport default function ChatPanel({\n  model\n}) {\n  _s();\n  const [messages, setMessages] = useState([]); // {role, content}\n  const [input, setInput] = useState('');\n  const [loading, setLoading] = useState(false);\n  const [agentic, setAgentic] = useState(false);\n  const BASE = process.env.REACT_APP_BACKEND_URL || 'http://localhost:8000';\n  async function sendMessage() {\n    if (!input.trim()) return;\n    const BASE = process.env.REACT_APP_BACKEND_URL || 'http://localhost:8000';\n    setLoading(true);\n    if (agentic) {\n      // Agentic tool-calling chat\n      const newMsgs = [...messages, {\n        role: 'user',\n        content: input\n      }];\n      setMessages(newMsgs);\n      setInput('');\n      try {\n        const res = await fetch(`${BASE}/api/chat/agent`, {\n          method: 'POST',\n          headers: {\n            'Content-Type': 'application/json'\n          },\n          body: JSON.stringify({\n            question: input,\n            model\n          })\n        });\n        const data = await res.json();\n        if (!res.ok) throw new Error(data.detail);\n        setMessages([...newMsgs, {\n          role: 'assistant',\n          content: data.answer\n        }]);\n      } catch (e) {\n        alert(e.message);\n      } finally {\n        setLoading(false);\n      }\n      return;\n    }\n    // Normal chat\n    const newMsgs = [...messages, {\n      role: 'user',\n      content: input\n    }];\n    setMessages(newMsgs);\n    setInput('');\n    try {\n      const res = await fetch(`${BASE}/api/chat/completion`, {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json'\n        },\n        body: JSON.stringify({\n          messages: newMsgs,\n          model\n        })\n      });\n      const data = await res.json();\n      if (!res.ok) throw new Error(data.detail);\n      setMessages([...newMsgs, {\n        role: 'assistant',\n        content: data.content\n      }]);\n    } catch (e) {\n      alert(e.message);\n    } finally {\n      setLoading(false);\n    }\n  }\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    style: {\n      display: 'flex',\n      flexDirection: 'column',\n      height: '100%'\n    },\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        flex: 1,\n        overflowY: 'auto',\n        padding: 8\n      },\n      children: [messages.map((m, i) => /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          marginBottom: 8\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"b\", {\n          children: [m.role === 'user' ? 'You' : 'AI', \": \"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 61,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n          children: m.content\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 62,\n          columnNumber: 13\n        }, this)]\n      }, i, true, {\n        fileName: _jsxFileName,\n        lineNumber: 60,\n        columnNumber: 11\n      }, this)), loading && /*#__PURE__*/_jsxDEV(\"div\", {\n        children: \"Thinking...\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 65,\n        columnNumber: 21\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 58,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        display: 'flex',\n        padding: 8,\n        borderTop: '1px solid #ccc',\n        alignItems: 'center'\n      },\n      children: [/*#__PURE__*/_jsxDEV(\"label\", {\n        style: {\n          marginRight: 8\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"input\", {\n          type: \"checkbox\",\n          checked: agentic,\n          onChange: e => setAgentic(e.target.checked)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 69,\n          columnNumber: 11\n        }, this), \"AI-Tools\"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 68,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n        style: {\n          flex: 1,\n          marginRight: 8\n        },\n        value: input,\n        onChange: e => setInput(e.target.value),\n        onKeyDown: e => {\n          if (e.key === 'Enter') sendMessage();\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 72,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n        onClick: sendMessage,\n        children: \"Send\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 80,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 67,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 57,\n    columnNumber: 5\n  }, this);\n}\n_s(ChatPanel, \"ZjaYfLTinkRYmzgFXZs2EGFWgbk=\");\n_c = ChatPanel;\nvar _c;\n$RefreshReg$(_c, \"ChatPanel\");", "map": {"version": 3, "names": ["React", "useState", "jsxDEV", "_jsxDEV", "ChatPanel", "model", "_s", "messages", "setMessages", "input", "setInput", "loading", "setLoading", "agentic", "setAgentic", "BASE", "process", "env", "REACT_APP_BACKEND_URL", "sendMessage", "trim", "newMsgs", "role", "content", "res", "fetch", "method", "headers", "body", "JSON", "stringify", "question", "data", "json", "ok", "Error", "detail", "answer", "e", "alert", "message", "style", "display", "flexDirection", "height", "children", "flex", "overflowY", "padding", "map", "m", "i", "marginBottom", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "borderTop", "alignItems", "marginRight", "type", "checked", "onChange", "target", "value", "onKeyDown", "key", "onClick", "_c", "$RefreshReg$"], "sources": ["D:/Documents_2/New Coding/CoderBuddy/CoderBuddy/frontend/src/components/ChatPanel.js"], "sourcesContent": ["import React, { useState } from 'react';\r\n\r\nexport default function ChatPanel({ model }) {\r\n  const [messages, setMessages] = useState([]); // {role, content}\r\n  const [input, setInput] = useState('');\r\n  const [loading, setLoading] = useState(false);\r\n  const [agentic, setAgentic] = useState(false);\r\n\r\n  const BASE = process.env.REACT_APP_BACKEND_URL || 'http://localhost:8000';\r\n\r\n  async function sendMessage() {\r\n    if (!input.trim()) return;\r\n    const BASE = process.env.REACT_APP_BACKEND_URL || 'http://localhost:8000';\r\n    setLoading(true);\r\n    if (agentic) {\r\n      // Agentic tool-calling chat\r\n      const newMsgs = [...messages, { role: 'user', content: input }];\r\n      setMessages(newMsgs);\r\n      setInput('');\r\n      try {\r\n        const res = await fetch(`${BASE}/api/chat/agent`, {\r\n          method: 'POST',\r\n          headers: { 'Content-Type': 'application/json' },\r\n          body: JSON.stringify({ question: input, model }),\r\n        });\r\n        const data = await res.json();\r\n        if (!res.ok) throw new Error(data.detail);\r\n        setMessages([...newMsgs, { role: 'assistant', content: data.answer }]);\r\n      } catch (e) {\r\n        alert(e.message);\r\n      } finally {\r\n        setLoading(false);\r\n      }\r\n      return;\r\n    }\r\n    // Normal chat\r\n    const newMsgs = [...messages, { role: 'user', content: input }];\r\n    setMessages(newMsgs);\r\n    setInput('');\r\n    try {\r\n      const res = await fetch(`${BASE}/api/chat/completion`, {\r\n        method: 'POST',\r\n        headers: { 'Content-Type': 'application/json' },\r\n        body: JSON.stringify({ messages: newMsgs, model }),\r\n      });\r\n      const data = await res.json();\r\n      if (!res.ok) throw new Error(data.detail);\r\n      setMessages([...newMsgs, { role: 'assistant', content: data.content }]);\r\n    } catch (e) {\r\n      alert(e.message);\r\n    } finally {\r\n      setLoading(false);\r\n    }\r\n  }\r\n\r\n  return (\r\n    <div style={{ display: 'flex', flexDirection: 'column', height: '100%' }}>\r\n      <div style={{ flex: 1, overflowY: 'auto', padding: 8 }}>\r\n        {messages.map((m, i) => (\r\n          <div key={i} style={{ marginBottom: 8 }}>\r\n            <b>{m.role === 'user' ? 'You' : 'AI'}: </b>\r\n            <span>{m.content}</span>\r\n          </div>\r\n        ))}\r\n        {loading && <div>Thinking...</div>}\r\n      </div>\r\n      <div style={{ display: 'flex', padding: 8, borderTop: '1px solid #ccc', alignItems: 'center' }}>\r\n        <label style={{ marginRight: 8 }}>\r\n          <input type=\"checkbox\" checked={agentic} onChange={e => setAgentic(e.target.checked)} />\r\n          AI-Tools\r\n        </label>\r\n        <input\r\n          style={{ flex: 1, marginRight: 8 }}\r\n          value={input}\r\n          onChange={(e) => setInput(e.target.value)}\r\n          onKeyDown={(e) => {\r\n            if (e.key === 'Enter') sendMessage();\r\n          }}\r\n        />\r\n        <button onClick={sendMessage}>Send</button>\r\n      </div>\r\n    </div>\r\n  );\r\n} "], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAExC,eAAe,SAASC,SAASA,CAAC;EAAEC;AAAM,CAAC,EAAE;EAAAC,EAAA;EAC3C,MAAM,CAACC,QAAQ,EAAEC,WAAW,CAAC,GAAGP,QAAQ,CAAC,EAAE,CAAC,CAAC,CAAC;EAC9C,MAAM,CAACQ,KAAK,EAAEC,QAAQ,CAAC,GAAGT,QAAQ,CAAC,EAAE,CAAC;EACtC,MAAM,CAACU,OAAO,EAAEC,UAAU,CAAC,GAAGX,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAACY,OAAO,EAAEC,UAAU,CAAC,GAAGb,QAAQ,CAAC,KAAK,CAAC;EAE7C,MAAMc,IAAI,GAAGC,OAAO,CAACC,GAAG,CAACC,qBAAqB,IAAI,uBAAuB;EAEzE,eAAeC,WAAWA,CAAA,EAAG;IAC3B,IAAI,CAACV,KAAK,CAACW,IAAI,CAAC,CAAC,EAAE;IACnB,MAAML,IAAI,GAAGC,OAAO,CAACC,GAAG,CAACC,qBAAqB,IAAI,uBAAuB;IACzEN,UAAU,CAAC,IAAI,CAAC;IAChB,IAAIC,OAAO,EAAE;MACX;MACA,MAAMQ,OAAO,GAAG,CAAC,GAAGd,QAAQ,EAAE;QAAEe,IAAI,EAAE,MAAM;QAAEC,OAAO,EAAEd;MAAM,CAAC,CAAC;MAC/DD,WAAW,CAACa,OAAO,CAAC;MACpBX,QAAQ,CAAC,EAAE,CAAC;MACZ,IAAI;QACF,MAAMc,GAAG,GAAG,MAAMC,KAAK,CAAC,GAAGV,IAAI,iBAAiB,EAAE;UAChDW,MAAM,EAAE,MAAM;UACdC,OAAO,EAAE;YAAE,cAAc,EAAE;UAAmB,CAAC;UAC/CC,IAAI,EAAEC,IAAI,CAACC,SAAS,CAAC;YAAEC,QAAQ,EAAEtB,KAAK;YAAEJ;UAAM,CAAC;QACjD,CAAC,CAAC;QACF,MAAM2B,IAAI,GAAG,MAAMR,GAAG,CAACS,IAAI,CAAC,CAAC;QAC7B,IAAI,CAACT,GAAG,CAACU,EAAE,EAAE,MAAM,IAAIC,KAAK,CAACH,IAAI,CAACI,MAAM,CAAC;QACzC5B,WAAW,CAAC,CAAC,GAAGa,OAAO,EAAE;UAAEC,IAAI,EAAE,WAAW;UAAEC,OAAO,EAAES,IAAI,CAACK;QAAO,CAAC,CAAC,CAAC;MACxE,CAAC,CAAC,OAAOC,CAAC,EAAE;QACVC,KAAK,CAACD,CAAC,CAACE,OAAO,CAAC;MAClB,CAAC,SAAS;QACR5B,UAAU,CAAC,KAAK,CAAC;MACnB;MACA;IACF;IACA;IACA,MAAMS,OAAO,GAAG,CAAC,GAAGd,QAAQ,EAAE;MAAEe,IAAI,EAAE,MAAM;MAAEC,OAAO,EAAEd;IAAM,CAAC,CAAC;IAC/DD,WAAW,CAACa,OAAO,CAAC;IACpBX,QAAQ,CAAC,EAAE,CAAC;IACZ,IAAI;MACF,MAAMc,GAAG,GAAG,MAAMC,KAAK,CAAC,GAAGV,IAAI,sBAAsB,EAAE;QACrDW,MAAM,EAAE,MAAM;QACdC,OAAO,EAAE;UAAE,cAAc,EAAE;QAAmB,CAAC;QAC/CC,IAAI,EAAEC,IAAI,CAACC,SAAS,CAAC;UAAEvB,QAAQ,EAAEc,OAAO;UAAEhB;QAAM,CAAC;MACnD,CAAC,CAAC;MACF,MAAM2B,IAAI,GAAG,MAAMR,GAAG,CAACS,IAAI,CAAC,CAAC;MAC7B,IAAI,CAACT,GAAG,CAACU,EAAE,EAAE,MAAM,IAAIC,KAAK,CAACH,IAAI,CAACI,MAAM,CAAC;MACzC5B,WAAW,CAAC,CAAC,GAAGa,OAAO,EAAE;QAAEC,IAAI,EAAE,WAAW;QAAEC,OAAO,EAAES,IAAI,CAACT;MAAQ,CAAC,CAAC,CAAC;IACzE,CAAC,CAAC,OAAOe,CAAC,EAAE;MACVC,KAAK,CAACD,CAAC,CAACE,OAAO,CAAC;IAClB,CAAC,SAAS;MACR5B,UAAU,CAAC,KAAK,CAAC;IACnB;EACF;EAEA,oBACET,OAAA;IAAKsC,KAAK,EAAE;MAAEC,OAAO,EAAE,MAAM;MAAEC,aAAa,EAAE,QAAQ;MAAEC,MAAM,EAAE;IAAO,CAAE;IAAAC,QAAA,gBACvE1C,OAAA;MAAKsC,KAAK,EAAE;QAAEK,IAAI,EAAE,CAAC;QAAEC,SAAS,EAAE,MAAM;QAAEC,OAAO,EAAE;MAAE,CAAE;MAAAH,QAAA,GACpDtC,QAAQ,CAAC0C,GAAG,CAAC,CAACC,CAAC,EAAEC,CAAC,kBACjBhD,OAAA;QAAasC,KAAK,EAAE;UAAEW,YAAY,EAAE;QAAE,CAAE;QAAAP,QAAA,gBACtC1C,OAAA;UAAA0C,QAAA,GAAIK,CAAC,CAAC5B,IAAI,KAAK,MAAM,GAAG,KAAK,GAAG,IAAI,EAAC,IAAE;QAAA;UAAA+B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC,eAC3CrD,OAAA;UAAA0C,QAAA,EAAOK,CAAC,CAAC3B;QAAO;UAAA8B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC;MAAA,GAFhBL,CAAC;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAGN,CACN,CAAC,EACD7C,OAAO,iBAAIR,OAAA;QAAA0C,QAAA,EAAK;MAAW;QAAAQ,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC/B,CAAC,eACNrD,OAAA;MAAKsC,KAAK,EAAE;QAAEC,OAAO,EAAE,MAAM;QAAEM,OAAO,EAAE,CAAC;QAAES,SAAS,EAAE,gBAAgB;QAAEC,UAAU,EAAE;MAAS,CAAE;MAAAb,QAAA,gBAC7F1C,OAAA;QAAOsC,KAAK,EAAE;UAAEkB,WAAW,EAAE;QAAE,CAAE;QAAAd,QAAA,gBAC/B1C,OAAA;UAAOyD,IAAI,EAAC,UAAU;UAACC,OAAO,EAAEhD,OAAQ;UAACiD,QAAQ,EAAExB,CAAC,IAAIxB,UAAU,CAACwB,CAAC,CAACyB,MAAM,CAACF,OAAO;QAAE;UAAAR,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,YAE1F;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAO,CAAC,eACRrD,OAAA;QACEsC,KAAK,EAAE;UAAEK,IAAI,EAAE,CAAC;UAAEa,WAAW,EAAE;QAAE,CAAE;QACnCK,KAAK,EAAEvD,KAAM;QACbqD,QAAQ,EAAGxB,CAAC,IAAK5B,QAAQ,CAAC4B,CAAC,CAACyB,MAAM,CAACC,KAAK,CAAE;QAC1CC,SAAS,EAAG3B,CAAC,IAAK;UAChB,IAAIA,CAAC,CAAC4B,GAAG,KAAK,OAAO,EAAE/C,WAAW,CAAC,CAAC;QACtC;MAAE;QAAAkC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eACFrD,OAAA;QAAQgE,OAAO,EAAEhD,WAAY;QAAA0B,QAAA,EAAC;MAAI;QAAAQ,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACxC,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV;AAAClD,EAAA,CAjFuBF,SAAS;AAAAgE,EAAA,GAAThE,SAAS;AAAA,IAAAgE,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}