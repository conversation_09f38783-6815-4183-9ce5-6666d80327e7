{"ast": null, "code": "var _jsxFileName = \"D:\\\\Documents_2\\\\New Coding\\\\CoderBuddy\\\\CoderBuddy\\\\frontend\\\\src\\\\components\\\\ChatPanel.js\",\n  _s = $RefreshSig$();\nimport React, { useState } from 'react';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nexport default function ChatPanel({\n  model,\n  projectId,\n  onProposeEdit\n}) {\n  _s();\n  const [messages, setMessages] = useState([]); // {role, content}\n  const [input, setInput] = useState('');\n  const [loading, setLoading] = useState(false);\n  const [agentic, setAgentic] = useState(false);\n  const BASE = process.env.REACT_APP_BACKEND_URL || 'http://localhost:8000';\n  async function sendMessage() {\n    if (!input.trim()) return;\n    const BASE = process.env.REACT_APP_BACKEND_URL || 'http://localhost:8000';\n    setLoading(true);\n    if (agentic) {\n      // Agentic tool-calling chat\n      const newMsgs = [...messages, {\n        role: 'user',\n        content: input\n      }];\n      setMessages(newMsgs);\n      setInput('');\n      try {\n        const res = await fetch(`${BASE}/api/chat/agent`, {\n          method: 'POST',\n          headers: {\n            'Content-Type': 'application/json'\n          },\n          body: JSON.stringify({\n            question: input,\n            project_id: projectId,\n            model\n          })\n        });\n        const data = await res.json();\n        if (!res.ok) throw new Error(data.detail);\n        setMessages([...newMsgs, {\n          role: 'assistant',\n          content: data.answer\n        }]);\n        if (data.file_edit) {\n          onProposeEdit(data.file_edit);\n        }\n      } catch (e) {\n        alert(e.message);\n      } finally {\n        setLoading(false);\n      }\n      return;\n    }\n    // Normal chat\n    const newMsgs = [...messages, {\n      role: 'user',\n      content: input\n    }];\n    setMessages(newMsgs);\n    setInput('');\n    try {\n      const res = await fetch(`${BASE}/api/chat/completion`, {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json'\n        },\n        body: JSON.stringify({\n          messages: newMsgs,\n          model\n        })\n      });\n      const data = await res.json();\n      if (!res.ok) throw new Error(data.detail);\n      setMessages([...newMsgs, {\n        role: 'assistant',\n        content: data.content\n      }]);\n    } catch (e) {\n      alert(e.message);\n    } finally {\n      setLoading(false);\n    }\n  }\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    style: {\n      display: 'flex',\n      flexDirection: 'column',\n      height: '100%'\n    },\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        flex: 1,\n        overflowY: 'auto',\n        padding: 8\n      },\n      children: [messages.map((m, i) => /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          marginBottom: 8\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"b\", {\n          children: [m.role === 'user' ? 'You' : 'AI', \": \"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 64,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n          children: m.content\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 65,\n          columnNumber: 13\n        }, this)]\n      }, i, true, {\n        fileName: _jsxFileName,\n        lineNumber: 63,\n        columnNumber: 11\n      }, this)), loading && /*#__PURE__*/_jsxDEV(\"div\", {\n        children: \"Thinking...\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 68,\n        columnNumber: 21\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 61,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        display: 'flex',\n        padding: 8,\n        borderTop: '1px solid #ccc',\n        alignItems: 'center'\n      },\n      children: [/*#__PURE__*/_jsxDEV(\"label\", {\n        style: {\n          marginRight: 8\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"input\", {\n          type: \"checkbox\",\n          checked: agentic,\n          onChange: e => setAgentic(e.target.checked)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 72,\n          columnNumber: 11\n        }, this), \"AI-Tools\"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 71,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n        style: {\n          flex: 1,\n          marginRight: 8\n        },\n        value: input,\n        onChange: e => setInput(e.target.value),\n        onKeyDown: e => {\n          if (e.key === 'Enter') sendMessage();\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 75,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n        onClick: sendMessage,\n        children: \"Send\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 83,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 70,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 60,\n    columnNumber: 5\n  }, this);\n}\n_s(ChatPanel, \"ZjaYfLTinkRYmzgFXZs2EGFWgbk=\");\n_c = ChatPanel;\nvar _c;\n$RefreshReg$(_c, \"ChatPanel\");", "map": {"version": 3, "names": ["React", "useState", "jsxDEV", "_jsxDEV", "ChatPanel", "model", "projectId", "onProposeEdit", "_s", "messages", "setMessages", "input", "setInput", "loading", "setLoading", "agentic", "setAgentic", "BASE", "process", "env", "REACT_APP_BACKEND_URL", "sendMessage", "trim", "newMsgs", "role", "content", "res", "fetch", "method", "headers", "body", "JSON", "stringify", "question", "project_id", "data", "json", "ok", "Error", "detail", "answer", "file_edit", "e", "alert", "message", "style", "display", "flexDirection", "height", "children", "flex", "overflowY", "padding", "map", "m", "i", "marginBottom", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "borderTop", "alignItems", "marginRight", "type", "checked", "onChange", "target", "value", "onKeyDown", "key", "onClick", "_c", "$RefreshReg$"], "sources": ["D:/Documents_2/New Coding/CoderBuddy/CoderBuddy/frontend/src/components/ChatPanel.js"], "sourcesContent": ["import React, { useState } from 'react';\r\n\r\nexport default function ChatPanel({ model, projectId, onProposeEdit }) {\r\n  const [messages, setMessages] = useState([]); // {role, content}\r\n  const [input, setInput] = useState('');\r\n  const [loading, setLoading] = useState(false);\r\n  const [agentic, setAgentic] = useState(false);\r\n\r\n  const BASE = process.env.REACT_APP_BACKEND_URL || 'http://localhost:8000';\r\n\r\n  async function sendMessage() {\r\n    if (!input.trim()) return;\r\n    const BASE = process.env.REACT_APP_BACKEND_URL || 'http://localhost:8000';\r\n    setLoading(true);\r\n    if (agentic) {\r\n      // Agentic tool-calling chat\r\n      const newMsgs = [...messages, { role: 'user', content: input }];\r\n      setMessages(newMsgs);\r\n      setInput('');\r\n      try {\r\n        const res = await fetch(`${BASE}/api/chat/agent`, {\r\n          method: 'POST',\r\n          headers: { 'Content-Type': 'application/json' },\r\n          body: JSON.stringify({ question: input, project_id: projectId, model }),\r\n        });\r\n        const data = await res.json();\r\n        if (!res.ok) throw new Error(data.detail);\r\n        setMessages([...newMsgs, { role: 'assistant', content: data.answer }]);\r\n        if (data.file_edit) {\r\n          onProposeEdit(data.file_edit);\r\n        }\r\n      } catch (e) {\r\n        alert(e.message);\r\n      } finally {\r\n        setLoading(false);\r\n      }\r\n      return;\r\n    }\r\n    // Normal chat\r\n    const newMsgs = [...messages, { role: 'user', content: input }];\r\n    setMessages(newMsgs);\r\n    setInput('');\r\n    try {\r\n      const res = await fetch(`${BASE}/api/chat/completion`, {\r\n        method: 'POST',\r\n        headers: { 'Content-Type': 'application/json' },\r\n        body: JSON.stringify({ messages: newMsgs, model }),\r\n      });\r\n      const data = await res.json();\r\n      if (!res.ok) throw new Error(data.detail);\r\n      setMessages([...newMsgs, { role: 'assistant', content: data.content }]);\r\n    } catch (e) {\r\n      alert(e.message);\r\n    } finally {\r\n      setLoading(false);\r\n    }\r\n  }\r\n\r\n  return (\r\n    <div style={{ display: 'flex', flexDirection: 'column', height: '100%' }}>\r\n      <div style={{ flex: 1, overflowY: 'auto', padding: 8 }}>\r\n        {messages.map((m, i) => (\r\n          <div key={i} style={{ marginBottom: 8 }}>\r\n            <b>{m.role === 'user' ? 'You' : 'AI'}: </b>\r\n            <span>{m.content}</span>\r\n          </div>\r\n        ))}\r\n        {loading && <div>Thinking...</div>}\r\n      </div>\r\n      <div style={{ display: 'flex', padding: 8, borderTop: '1px solid #ccc', alignItems: 'center' }}>\r\n        <label style={{ marginRight: 8 }}>\r\n          <input type=\"checkbox\" checked={agentic} onChange={e => setAgentic(e.target.checked)} />\r\n          AI-Tools\r\n        </label>\r\n        <input\r\n          style={{ flex: 1, marginRight: 8 }}\r\n          value={input}\r\n          onChange={(e) => setInput(e.target.value)}\r\n          onKeyDown={(e) => {\r\n            if (e.key === 'Enter') sendMessage();\r\n          }}\r\n        />\r\n        <button onClick={sendMessage}>Send</button>\r\n      </div>\r\n    </div>\r\n  );\r\n} "], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAExC,eAAe,SAASC,SAASA,CAAC;EAAEC,KAAK;EAAEC,SAAS;EAAEC;AAAc,CAAC,EAAE;EAAAC,EAAA;EACrE,MAAM,CAACC,QAAQ,EAAEC,WAAW,CAAC,GAAGT,QAAQ,CAAC,EAAE,CAAC,CAAC,CAAC;EAC9C,MAAM,CAACU,KAAK,EAAEC,QAAQ,CAAC,GAAGX,QAAQ,CAAC,EAAE,CAAC;EACtC,MAAM,CAACY,OAAO,EAAEC,UAAU,CAAC,GAAGb,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAACc,OAAO,EAAEC,UAAU,CAAC,GAAGf,QAAQ,CAAC,KAAK,CAAC;EAE7C,MAAMgB,IAAI,GAAGC,OAAO,CAACC,GAAG,CAACC,qBAAqB,IAAI,uBAAuB;EAEzE,eAAeC,WAAWA,CAAA,EAAG;IAC3B,IAAI,CAACV,KAAK,CAACW,IAAI,CAAC,CAAC,EAAE;IACnB,MAAML,IAAI,GAAGC,OAAO,CAACC,GAAG,CAACC,qBAAqB,IAAI,uBAAuB;IACzEN,UAAU,CAAC,IAAI,CAAC;IAChB,IAAIC,OAAO,EAAE;MACX;MACA,MAAMQ,OAAO,GAAG,CAAC,GAAGd,QAAQ,EAAE;QAAEe,IAAI,EAAE,MAAM;QAAEC,OAAO,EAAEd;MAAM,CAAC,CAAC;MAC/DD,WAAW,CAACa,OAAO,CAAC;MACpBX,QAAQ,CAAC,EAAE,CAAC;MACZ,IAAI;QACF,MAAMc,GAAG,GAAG,MAAMC,KAAK,CAAC,GAAGV,IAAI,iBAAiB,EAAE;UAChDW,MAAM,EAAE,MAAM;UACdC,OAAO,EAAE;YAAE,cAAc,EAAE;UAAmB,CAAC;UAC/CC,IAAI,EAAEC,IAAI,CAACC,SAAS,CAAC;YAAEC,QAAQ,EAAEtB,KAAK;YAAEuB,UAAU,EAAE5B,SAAS;YAAED;UAAM,CAAC;QACxE,CAAC,CAAC;QACF,MAAM8B,IAAI,GAAG,MAAMT,GAAG,CAACU,IAAI,CAAC,CAAC;QAC7B,IAAI,CAACV,GAAG,CAACW,EAAE,EAAE,MAAM,IAAIC,KAAK,CAACH,IAAI,CAACI,MAAM,CAAC;QACzC7B,WAAW,CAAC,CAAC,GAAGa,OAAO,EAAE;UAAEC,IAAI,EAAE,WAAW;UAAEC,OAAO,EAAEU,IAAI,CAACK;QAAO,CAAC,CAAC,CAAC;QACtE,IAAIL,IAAI,CAACM,SAAS,EAAE;UAClBlC,aAAa,CAAC4B,IAAI,CAACM,SAAS,CAAC;QAC/B;MACF,CAAC,CAAC,OAAOC,CAAC,EAAE;QACVC,KAAK,CAACD,CAAC,CAACE,OAAO,CAAC;MAClB,CAAC,SAAS;QACR9B,UAAU,CAAC,KAAK,CAAC;MACnB;MACA;IACF;IACA;IACA,MAAMS,OAAO,GAAG,CAAC,GAAGd,QAAQ,EAAE;MAAEe,IAAI,EAAE,MAAM;MAAEC,OAAO,EAAEd;IAAM,CAAC,CAAC;IAC/DD,WAAW,CAACa,OAAO,CAAC;IACpBX,QAAQ,CAAC,EAAE,CAAC;IACZ,IAAI;MACF,MAAMc,GAAG,GAAG,MAAMC,KAAK,CAAC,GAAGV,IAAI,sBAAsB,EAAE;QACrDW,MAAM,EAAE,MAAM;QACdC,OAAO,EAAE;UAAE,cAAc,EAAE;QAAmB,CAAC;QAC/CC,IAAI,EAAEC,IAAI,CAACC,SAAS,CAAC;UAAEvB,QAAQ,EAAEc,OAAO;UAAElB;QAAM,CAAC;MACnD,CAAC,CAAC;MACF,MAAM8B,IAAI,GAAG,MAAMT,GAAG,CAACU,IAAI,CAAC,CAAC;MAC7B,IAAI,CAACV,GAAG,CAACW,EAAE,EAAE,MAAM,IAAIC,KAAK,CAACH,IAAI,CAACI,MAAM,CAAC;MACzC7B,WAAW,CAAC,CAAC,GAAGa,OAAO,EAAE;QAAEC,IAAI,EAAE,WAAW;QAAEC,OAAO,EAAEU,IAAI,CAACV;MAAQ,CAAC,CAAC,CAAC;IACzE,CAAC,CAAC,OAAOiB,CAAC,EAAE;MACVC,KAAK,CAACD,CAAC,CAACE,OAAO,CAAC;IAClB,CAAC,SAAS;MACR9B,UAAU,CAAC,KAAK,CAAC;IACnB;EACF;EAEA,oBACEX,OAAA;IAAK0C,KAAK,EAAE;MAAEC,OAAO,EAAE,MAAM;MAAEC,aAAa,EAAE,QAAQ;MAAEC,MAAM,EAAE;IAAO,CAAE;IAAAC,QAAA,gBACvE9C,OAAA;MAAK0C,KAAK,EAAE;QAAEK,IAAI,EAAE,CAAC;QAAEC,SAAS,EAAE,MAAM;QAAEC,OAAO,EAAE;MAAE,CAAE;MAAAH,QAAA,GACpDxC,QAAQ,CAAC4C,GAAG,CAAC,CAACC,CAAC,EAAEC,CAAC,kBACjBpD,OAAA;QAAa0C,KAAK,EAAE;UAAEW,YAAY,EAAE;QAAE,CAAE;QAAAP,QAAA,gBACtC9C,OAAA;UAAA8C,QAAA,GAAIK,CAAC,CAAC9B,IAAI,KAAK,MAAM,GAAG,KAAK,GAAG,IAAI,EAAC,IAAE;QAAA;UAAAiC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC,eAC3CzD,OAAA;UAAA8C,QAAA,EAAOK,CAAC,CAAC7B;QAAO;UAAAgC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC;MAAA,GAFhBL,CAAC;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAGN,CACN,CAAC,EACD/C,OAAO,iBAAIV,OAAA;QAAA8C,QAAA,EAAK;MAAW;QAAAQ,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC/B,CAAC,eACNzD,OAAA;MAAK0C,KAAK,EAAE;QAAEC,OAAO,EAAE,MAAM;QAAEM,OAAO,EAAE,CAAC;QAAES,SAAS,EAAE,gBAAgB;QAAEC,UAAU,EAAE;MAAS,CAAE;MAAAb,QAAA,gBAC7F9C,OAAA;QAAO0C,KAAK,EAAE;UAAEkB,WAAW,EAAE;QAAE,CAAE;QAAAd,QAAA,gBAC/B9C,OAAA;UAAO6D,IAAI,EAAC,UAAU;UAACC,OAAO,EAAElD,OAAQ;UAACmD,QAAQ,EAAExB,CAAC,IAAI1B,UAAU,CAAC0B,CAAC,CAACyB,MAAM,CAACF,OAAO;QAAE;UAAAR,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,YAE1F;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAO,CAAC,eACRzD,OAAA;QACE0C,KAAK,EAAE;UAAEK,IAAI,EAAE,CAAC;UAAEa,WAAW,EAAE;QAAE,CAAE;QACnCK,KAAK,EAAEzD,KAAM;QACbuD,QAAQ,EAAGxB,CAAC,IAAK9B,QAAQ,CAAC8B,CAAC,CAACyB,MAAM,CAACC,KAAK,CAAE;QAC1CC,SAAS,EAAG3B,CAAC,IAAK;UAChB,IAAIA,CAAC,CAAC4B,GAAG,KAAK,OAAO,EAAEjD,WAAW,CAAC,CAAC;QACtC;MAAE;QAAAoC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eACFzD,OAAA;QAAQoE,OAAO,EAAElD,WAAY;QAAA4B,QAAA,EAAC;MAAI;QAAAQ,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACxC,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV;AAACpD,EAAA,CApFuBJ,SAAS;AAAAoE,EAAA,GAATpE,SAAS;AAAA,IAAAoE,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}