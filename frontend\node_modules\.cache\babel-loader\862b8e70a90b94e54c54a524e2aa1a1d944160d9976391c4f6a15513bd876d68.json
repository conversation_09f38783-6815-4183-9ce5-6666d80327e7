{"ast": null, "code": "var _jsxFileName = \"D:\\\\Documents_2\\\\New Coding\\\\CoderBuddy\\\\CoderBuddy\\\\frontend\\\\src\\\\components\\\\EditorTabs.js\",\n  _s = $RefreshSig$();\nimport React, { useState } from 'react';\nimport { readFile } from '../services/api';\nimport Editor from '@monaco-editor/react';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nexport default function EditorTabs({\n  selectedFile\n}) {\n  _s();\n  var _tabs$find;\n  const [tabs, setTabs] = useState([]);\n  const [activePath, setActivePath] = useState(null);\n  React.useEffect(() => {\n    if (selectedFile) {\n      openFile(selectedFile);\n    }\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n  }, [selectedFile]);\n  async function openFile(path) {\n    // if already open, just activate\n    const existing = tabs.find(t => t.path === path);\n    if (existing) {\n      setActivePath(path);\n      return;\n    }\n    try {\n      const {\n        content\n      } = await readFile(path);\n      setTabs(prev => [...prev, {\n        path,\n        content\n      }]);\n      setActivePath(path);\n    } catch (e) {\n      alert(`Failed to open file: ${e.message}`);\n    }\n  }\n  function handleChange(content, ev) {\n    setTabs(prev => prev.map(t => t.path === activePath ? {\n      ...t,\n      content\n    } : t));\n  }\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    style: {\n      flex: 1,\n      display: 'flex',\n      flexDirection: 'column'\n    },\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        display: 'flex',\n        borderBottom: '1px solid #ccc',\n        overflowX: 'auto'\n      },\n      children: tabs.map(tab => /*#__PURE__*/_jsxDEV(\"div\", {\n        onClick: () => setActivePath(tab.path),\n        style: {\n          padding: '4px 8px',\n          cursor: 'pointer',\n          backgroundColor: tab.path === activePath ? '#eee' : 'transparent'\n        },\n        children: tab.path.split('/').pop()\n      }, tab.path, false, {\n        fileName: _jsxFileName,\n        lineNumber: 42,\n        columnNumber: 11\n      }, this))\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 40,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        flex: 1\n      },\n      children: tabs.length > 0 && activePath && /*#__PURE__*/_jsxDEV(Editor, {\n        height: \"100%\",\n        defaultLanguage: \"javascript\",\n        value: ((_tabs$find = tabs.find(t => t.path === activePath)) === null || _tabs$find === void 0 ? void 0 : _tabs$find.content) || '',\n        onChange: handleChange,\n        options: {\n          fontSize: 14\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 57,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 55,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 39,\n    columnNumber: 5\n  }, this);\n}\n_s(EditorTabs, \"SAur01WC/grlkZILeaSsdpSSJF4=\");\n_c = EditorTabs;\nvar _c;\n$RefreshReg$(_c, \"EditorTabs\");", "map": {"version": 3, "names": ["React", "useState", "readFile", "Editor", "jsxDEV", "_jsxDEV", "EditorTabs", "selectedFile", "_s", "_tabs$find", "tabs", "setTabs", "activePath", "setActivePath", "useEffect", "openFile", "path", "existing", "find", "t", "content", "prev", "e", "alert", "message", "handleChange", "ev", "map", "style", "flex", "display", "flexDirection", "children", "borderBottom", "overflowX", "tab", "onClick", "padding", "cursor", "backgroundColor", "split", "pop", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "length", "height", "defaultLanguage", "value", "onChange", "options", "fontSize", "_c", "$RefreshReg$"], "sources": ["D:/Documents_2/New Coding/CoderBuddy/CoderBuddy/frontend/src/components/EditorTabs.js"], "sourcesContent": ["import React, { useState } from 'react';\r\nimport { readFile } from '../services/api';\r\nimport Editor from '@monaco-editor/react';\r\n\r\nexport default function EditorTabs({ selectedFile }) {\r\n  const [tabs, setTabs] = useState([]);\r\n  const [activePath, setActivePath] = useState(null);\r\n\r\n  React.useEffect(() => {\r\n    if (selectedFile) {\r\n      openFile(selectedFile);\r\n    }\r\n    // eslint-disable-next-line react-hooks/exhaustive-deps\r\n  }, [selectedFile]);\r\n\r\n  async function openFile(path) {\r\n    // if already open, just activate\r\n    const existing = tabs.find((t) => t.path === path);\r\n    if (existing) {\r\n      setActivePath(path);\r\n      return;\r\n    }\r\n    try {\r\n      const { content } = await readFile(path);\r\n      setTabs((prev) => [...prev, { path, content }]);\r\n      setActivePath(path);\r\n    } catch (e) {\r\n      alert(`Failed to open file: ${e.message}`);\r\n    }\r\n  }\r\n\r\n  function handleChange(content, ev) {\r\n    setTabs((prev) =>\r\n      prev.map((t) => (t.path === activePath ? { ...t, content } : t))\r\n    );\r\n  }\r\n\r\n  return (\r\n    <div style={{ flex: 1, display: 'flex', flexDirection: 'column' }}>\r\n      <div style={{ display: 'flex', borderBottom: '1px solid #ccc', overflowX: 'auto' }}>\r\n        {tabs.map((tab) => (\r\n          <div\r\n            key={tab.path}\r\n            onClick={() => setActivePath(tab.path)}\r\n            style={{\r\n              padding: '4px 8px',\r\n              cursor: 'pointer',\r\n              backgroundColor: tab.path === activePath ? '#eee' : 'transparent',\r\n            }}\r\n          >\r\n            {tab.path.split('/').pop()}\r\n          </div>\r\n        ))}\r\n      </div>\r\n      <div style={{ flex: 1 }}>\r\n        {tabs.length > 0 && activePath && (\r\n          <Editor\r\n            height=\"100%\"\r\n            defaultLanguage=\"javascript\"\r\n            value={tabs.find((t) => t.path === activePath)?.content || ''}\r\n            onChange={handleChange}\r\n            options={{ fontSize: 14 }}\r\n          />\r\n        )}\r\n      </div>\r\n    </div>\r\n  );\r\n} "], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,SAASC,QAAQ,QAAQ,iBAAiB;AAC1C,OAAOC,MAAM,MAAM,sBAAsB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE1C,eAAe,SAASC,UAAUA,CAAC;EAAEC;AAAa,CAAC,EAAE;EAAAC,EAAA;EAAA,IAAAC,UAAA;EACnD,MAAM,CAACC,IAAI,EAAEC,OAAO,CAAC,GAAGV,QAAQ,CAAC,EAAE,CAAC;EACpC,MAAM,CAACW,UAAU,EAAEC,aAAa,CAAC,GAAGZ,QAAQ,CAAC,IAAI,CAAC;EAElDD,KAAK,CAACc,SAAS,CAAC,MAAM;IACpB,IAAIP,YAAY,EAAE;MAChBQ,QAAQ,CAACR,YAAY,CAAC;IACxB;IACA;EACF,CAAC,EAAE,CAACA,YAAY,CAAC,CAAC;EAElB,eAAeQ,QAAQA,CAACC,IAAI,EAAE;IAC5B;IACA,MAAMC,QAAQ,GAAGP,IAAI,CAACQ,IAAI,CAAEC,CAAC,IAAKA,CAAC,CAACH,IAAI,KAAKA,IAAI,CAAC;IAClD,IAAIC,QAAQ,EAAE;MACZJ,aAAa,CAACG,IAAI,CAAC;MACnB;IACF;IACA,IAAI;MACF,MAAM;QAAEI;MAAQ,CAAC,GAAG,MAAMlB,QAAQ,CAACc,IAAI,CAAC;MACxCL,OAAO,CAAEU,IAAI,IAAK,CAAC,GAAGA,IAAI,EAAE;QAAEL,IAAI;QAAEI;MAAQ,CAAC,CAAC,CAAC;MAC/CP,aAAa,CAACG,IAAI,CAAC;IACrB,CAAC,CAAC,OAAOM,CAAC,EAAE;MACVC,KAAK,CAAC,wBAAwBD,CAAC,CAACE,OAAO,EAAE,CAAC;IAC5C;EACF;EAEA,SAASC,YAAYA,CAACL,OAAO,EAAEM,EAAE,EAAE;IACjCf,OAAO,CAAEU,IAAI,IACXA,IAAI,CAACM,GAAG,CAAER,CAAC,IAAMA,CAAC,CAACH,IAAI,KAAKJ,UAAU,GAAG;MAAE,GAAGO,CAAC;MAAEC;IAAQ,CAAC,GAAGD,CAAE,CACjE,CAAC;EACH;EAEA,oBACEd,OAAA;IAAKuB,KAAK,EAAE;MAAEC,IAAI,EAAE,CAAC;MAAEC,OAAO,EAAE,MAAM;MAAEC,aAAa,EAAE;IAAS,CAAE;IAAAC,QAAA,gBAChE3B,OAAA;MAAKuB,KAAK,EAAE;QAAEE,OAAO,EAAE,MAAM;QAAEG,YAAY,EAAE,gBAAgB;QAAEC,SAAS,EAAE;MAAO,CAAE;MAAAF,QAAA,EAChFtB,IAAI,CAACiB,GAAG,CAAEQ,GAAG,iBACZ9B,OAAA;QAEE+B,OAAO,EAAEA,CAAA,KAAMvB,aAAa,CAACsB,GAAG,CAACnB,IAAI,CAAE;QACvCY,KAAK,EAAE;UACLS,OAAO,EAAE,SAAS;UAClBC,MAAM,EAAE,SAAS;UACjBC,eAAe,EAAEJ,GAAG,CAACnB,IAAI,KAAKJ,UAAU,GAAG,MAAM,GAAG;QACtD,CAAE;QAAAoB,QAAA,EAEDG,GAAG,CAACnB,IAAI,CAACwB,KAAK,CAAC,GAAG,CAAC,CAACC,GAAG,CAAC;MAAC,GARrBN,GAAG,CAACnB,IAAI;QAAA0B,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OASV,CACN;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC,eACNxC,OAAA;MAAKuB,KAAK,EAAE;QAAEC,IAAI,EAAE;MAAE,CAAE;MAAAG,QAAA,EACrBtB,IAAI,CAACoC,MAAM,GAAG,CAAC,IAAIlC,UAAU,iBAC5BP,OAAA,CAACF,MAAM;QACL4C,MAAM,EAAC,MAAM;QACbC,eAAe,EAAC,YAAY;QAC5BC,KAAK,EAAE,EAAAxC,UAAA,GAAAC,IAAI,CAACQ,IAAI,CAAEC,CAAC,IAAKA,CAAC,CAACH,IAAI,KAAKJ,UAAU,CAAC,cAAAH,UAAA,uBAAvCA,UAAA,CAAyCW,OAAO,KAAI,EAAG;QAC9D8B,QAAQ,EAAEzB,YAAa;QACvB0B,OAAO,EAAE;UAAEC,QAAQ,EAAE;QAAG;MAAE;QAAAV,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC3B;IACF;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV;AAACrC,EAAA,CA/DuBF,UAAU;AAAA+C,EAAA,GAAV/C,UAAU;AAAA,IAAA+C,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}