{"ast": null, "code": "var _jsxFileName = \"D:\\\\Documents_2\\\\New Coding\\\\CoderBuddy\\\\CoderBuddy\\\\frontend\\\\src\\\\components\\\\EditorTabs.js\",\n  _s = $RefreshSig$();\nimport React, { useState } from 'react';\nimport { readFile, writeFile } from '../services/api';\nimport Editor from '@monaco-editor/react';\nimport { useHotkeys } from 'react-hotkeys-hook';\nimport NiceModal from '@ebay/nice-modal-react';\nimport SaveDiffModal from './SaveDiffModal';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nexport default function EditorTabs({\n  selectedFile\n}) {\n  _s();\n  var _tabs$find;\n  const [tabs, setTabs] = useState([]);\n  const [activePath, setActivePath] = useState(null);\n  React.useEffect(() => {\n    if (selectedFile) {\n      openFile(selectedFile);\n    }\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n  }, [selectedFile]);\n  async function openFile(path) {\n    // if already open, just activate\n    const existing = tabs.find(t => t.path === path);\n    if (existing) {\n      setActivePath(path);\n      return;\n    }\n    try {\n      const {\n        content\n      } = await readFile(path);\n      setTabs(prev => [...prev, {\n        path,\n        content,\n        savedContent: content\n      }]);\n      setActivePath(path);\n    } catch (e) {\n      alert(`Failed to open file: ${e.message}`);\n    }\n  }\n  function handleChange(content, ev) {\n    setTabs(prev => prev.map(t => t.path === activePath ? {\n      ...t,\n      content\n    } : t));\n  }\n  const handleSave = async () => {\n    if (!activePath) return;\n    const tab = tabs.find(t => t.path === activePath);\n    if (!tab || tab.content === tab.savedContent) return;\n    try {\n      const confirm = await NiceModal.show(SaveDiffModal, {\n        oldText: tab.savedContent,\n        newText: tab.content,\n        filePath: tab.path\n      });\n      if (!confirm) return;\n      await writeFile(tab.path, tab.content);\n      setTabs(prev => prev.map(t => t.path === tab.path ? {\n        ...t,\n        savedContent: t.content\n      } : t));\n    } catch (e) {\n      console.error(e);\n      alert('Save failed: ' + e.message);\n    }\n  };\n\n  // Ctrl+S hotkey\n  useHotkeys('ctrl+s, command+s', e => {\n    e.preventDefault();\n    handleSave();\n  }, [tabs, activePath]);\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    style: {\n      flex: 1,\n      display: 'flex',\n      flexDirection: 'column'\n    },\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        display: 'flex',\n        borderBottom: '1px solid #ccc',\n        overflowX: 'auto',\n        alignItems: 'center'\n      },\n      children: [tabs.map(tab => /*#__PURE__*/_jsxDEV(\"div\", {\n        onClick: () => setActivePath(tab.path),\n        style: {\n          padding: '4px 8px',\n          cursor: 'pointer',\n          backgroundColor: tab.path === activePath ? '#eee' : 'transparent'\n        },\n        children: tab.path.split('/').pop()\n      }, tab.path, false, {\n        fileName: _jsxFileName,\n        lineNumber: 76,\n        columnNumber: 11\n      }, this)), activePath && /*#__PURE__*/_jsxDEV(\"button\", {\n        style: {\n          marginLeft: 'auto',\n          marginRight: 8\n        },\n        onClick: handleSave,\n        children: \"Save\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 89,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 74,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        flex: 1\n      },\n      children: tabs.length > 0 && activePath && /*#__PURE__*/_jsxDEV(Editor, {\n        height: \"100%\",\n        defaultLanguage: \"javascript\",\n        value: ((_tabs$find = tabs.find(t => t.path === activePath)) === null || _tabs$find === void 0 ? void 0 : _tabs$find.content) || '',\n        onChange: handleChange,\n        options: {\n          fontSize: 14\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 96,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 94,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 73,\n    columnNumber: 5\n  }, this);\n}\n_s(EditorTabs, \"HeYRT4VygpGzPug32ayGzf6lBzQ=\", false, function () {\n  return [useHotkeys];\n});\n_c = EditorTabs;\nvar _c;\n$RefreshReg$(_c, \"EditorTabs\");", "map": {"version": 3, "names": ["React", "useState", "readFile", "writeFile", "Editor", "useHotkeys", "NiceModal", "SaveDiffModal", "jsxDEV", "_jsxDEV", "EditorTabs", "selectedFile", "_s", "_tabs$find", "tabs", "setTabs", "activePath", "setActivePath", "useEffect", "openFile", "path", "existing", "find", "t", "content", "prev", "saved<PERSON><PERSON>nt", "e", "alert", "message", "handleChange", "ev", "map", "handleSave", "tab", "confirm", "show", "oldText", "newText", "filePath", "console", "error", "preventDefault", "style", "flex", "display", "flexDirection", "children", "borderBottom", "overflowX", "alignItems", "onClick", "padding", "cursor", "backgroundColor", "split", "pop", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "marginLeft", "marginRight", "length", "height", "defaultLanguage", "value", "onChange", "options", "fontSize", "_c", "$RefreshReg$"], "sources": ["D:/Documents_2/New Coding/CoderBuddy/CoderBuddy/frontend/src/components/EditorTabs.js"], "sourcesContent": ["import React, { useState } from 'react';\r\nimport { readFile, writeFile } from '../services/api';\r\nimport Editor from '@monaco-editor/react';\r\nimport { useHotkeys } from 'react-hotkeys-hook';\r\nimport NiceModal from '@ebay/nice-modal-react';\r\nimport SaveDiffModal from './SaveDiffModal';\r\n\r\nexport default function EditorTabs({ selectedFile }) {\r\n  const [tabs, setTabs] = useState([]);\r\n  const [activePath, setActivePath] = useState(null);\r\n\r\n  React.useEffect(() => {\r\n    if (selectedFile) {\r\n      openFile(selectedFile);\r\n    }\r\n    // eslint-disable-next-line react-hooks/exhaustive-deps\r\n  }, [selectedFile]);\r\n\r\n  async function openFile(path) {\r\n    // if already open, just activate\r\n    const existing = tabs.find((t) => t.path === path);\r\n    if (existing) {\r\n      setActivePath(path);\r\n      return;\r\n    }\r\n    try {\r\n      const { content } = await readFile(path);\r\n      setTabs((prev) => [...prev, { path, content, savedContent: content }]);\r\n      setActivePath(path);\r\n    } catch (e) {\r\n      alert(`Failed to open file: ${e.message}`);\r\n    }\r\n  }\r\n\r\n  function handleChange(content, ev) {\r\n    setTabs((prev) =>\r\n      prev.map((t) =>\r\n        t.path === activePath ? { ...t, content } : t\r\n      )\r\n    );\r\n  }\r\n\r\n  const handleSave = async () => {\r\n    if (!activePath) return;\r\n    const tab = tabs.find((t) => t.path === activePath);\r\n    if (!tab || tab.content === tab.savedContent) return;\r\n    try {\r\n      const confirm = await NiceModal.show(SaveDiffModal, {\r\n        oldText: tab.savedContent,\r\n        newText: tab.content,\r\n        filePath: tab.path,\r\n      });\r\n      if (!confirm) return;\r\n      await writeFile(tab.path, tab.content);\r\n      setTabs((prev) =>\r\n        prev.map((t) =>\r\n          t.path === tab.path ? { ...t, savedContent: t.content } : t\r\n        )\r\n      );\r\n    } catch (e) {\r\n      console.error(e);\r\n      alert('Save failed: ' + e.message);\r\n    }\r\n  };\r\n\r\n  // Ctrl+S hotkey\r\n  useHotkeys('ctrl+s, command+s', (e) => {\r\n    e.preventDefault();\r\n    handleSave();\r\n  }, [tabs, activePath]);\r\n\r\n  return (\r\n    <div style={{ flex: 1, display: 'flex', flexDirection: 'column' }}>\r\n      <div style={{ display: 'flex', borderBottom: '1px solid #ccc', overflowX: 'auto', alignItems: 'center' }}>\r\n        {tabs.map((tab) => (\r\n          <div\r\n            key={tab.path}\r\n            onClick={() => setActivePath(tab.path)}\r\n            style={{\r\n              padding: '4px 8px',\r\n              cursor: 'pointer',\r\n              backgroundColor: tab.path === activePath ? '#eee' : 'transparent',\r\n            }}\r\n          >\r\n            {tab.path.split('/').pop()}\r\n          </div>\r\n        ))}\r\n        {activePath && (\r\n          <button style={{ marginLeft: 'auto', marginRight: 8 }} onClick={handleSave}>\r\n            Save\r\n          </button>\r\n        )}\r\n      </div>\r\n      <div style={{ flex: 1 }}>\r\n        {tabs.length > 0 && activePath && (\r\n          <Editor\r\n            height=\"100%\"\r\n            defaultLanguage=\"javascript\"\r\n            value={tabs.find((t) => t.path === activePath)?.content || ''}\r\n            onChange={handleChange}\r\n            options={{ fontSize: 14 }}\r\n          />\r\n        )}\r\n      </div>\r\n    </div>\r\n  );\r\n} "], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,SAASC,QAAQ,EAAEC,SAAS,QAAQ,iBAAiB;AACrD,OAAOC,MAAM,MAAM,sBAAsB;AACzC,SAASC,UAAU,QAAQ,oBAAoB;AAC/C,OAAOC,SAAS,MAAM,wBAAwB;AAC9C,OAAOC,aAAa,MAAM,iBAAiB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE5C,eAAe,SAASC,UAAUA,CAAC;EAAEC;AAAa,CAAC,EAAE;EAAAC,EAAA;EAAA,IAAAC,UAAA;EACnD,MAAM,CAACC,IAAI,EAAEC,OAAO,CAAC,GAAGd,QAAQ,CAAC,EAAE,CAAC;EACpC,MAAM,CAACe,UAAU,EAAEC,aAAa,CAAC,GAAGhB,QAAQ,CAAC,IAAI,CAAC;EAElDD,KAAK,CAACkB,SAAS,CAAC,MAAM;IACpB,IAAIP,YAAY,EAAE;MAChBQ,QAAQ,CAACR,YAAY,CAAC;IACxB;IACA;EACF,CAAC,EAAE,CAACA,YAAY,CAAC,CAAC;EAElB,eAAeQ,QAAQA,CAACC,IAAI,EAAE;IAC5B;IACA,MAAMC,QAAQ,GAAGP,IAAI,CAACQ,IAAI,CAAEC,CAAC,IAAKA,CAAC,CAACH,IAAI,KAAKA,IAAI,CAAC;IAClD,IAAIC,QAAQ,EAAE;MACZJ,aAAa,CAACG,IAAI,CAAC;MACnB;IACF;IACA,IAAI;MACF,MAAM;QAAEI;MAAQ,CAAC,GAAG,MAAMtB,QAAQ,CAACkB,IAAI,CAAC;MACxCL,OAAO,CAAEU,IAAI,IAAK,CAAC,GAAGA,IAAI,EAAE;QAAEL,IAAI;QAAEI,OAAO;QAAEE,YAAY,EAAEF;MAAQ,CAAC,CAAC,CAAC;MACtEP,aAAa,CAACG,IAAI,CAAC;IACrB,CAAC,CAAC,OAAOO,CAAC,EAAE;MACVC,KAAK,CAAC,wBAAwBD,CAAC,CAACE,OAAO,EAAE,CAAC;IAC5C;EACF;EAEA,SAASC,YAAYA,CAACN,OAAO,EAAEO,EAAE,EAAE;IACjChB,OAAO,CAAEU,IAAI,IACXA,IAAI,CAACO,GAAG,CAAET,CAAC,IACTA,CAAC,CAACH,IAAI,KAAKJ,UAAU,GAAG;MAAE,GAAGO,CAAC;MAAEC;IAAQ,CAAC,GAAGD,CAC9C,CACF,CAAC;EACH;EAEA,MAAMU,UAAU,GAAG,MAAAA,CAAA,KAAY;IAC7B,IAAI,CAACjB,UAAU,EAAE;IACjB,MAAMkB,GAAG,GAAGpB,IAAI,CAACQ,IAAI,CAAEC,CAAC,IAAKA,CAAC,CAACH,IAAI,KAAKJ,UAAU,CAAC;IACnD,IAAI,CAACkB,GAAG,IAAIA,GAAG,CAACV,OAAO,KAAKU,GAAG,CAACR,YAAY,EAAE;IAC9C,IAAI;MACF,MAAMS,OAAO,GAAG,MAAM7B,SAAS,CAAC8B,IAAI,CAAC7B,aAAa,EAAE;QAClD8B,OAAO,EAAEH,GAAG,CAACR,YAAY;QACzBY,OAAO,EAAEJ,GAAG,CAACV,OAAO;QACpBe,QAAQ,EAAEL,GAAG,CAACd;MAChB,CAAC,CAAC;MACF,IAAI,CAACe,OAAO,EAAE;MACd,MAAMhC,SAAS,CAAC+B,GAAG,CAACd,IAAI,EAAEc,GAAG,CAACV,OAAO,CAAC;MACtCT,OAAO,CAAEU,IAAI,IACXA,IAAI,CAACO,GAAG,CAAET,CAAC,IACTA,CAAC,CAACH,IAAI,KAAKc,GAAG,CAACd,IAAI,GAAG;QAAE,GAAGG,CAAC;QAAEG,YAAY,EAAEH,CAAC,CAACC;MAAQ,CAAC,GAAGD,CAC5D,CACF,CAAC;IACH,CAAC,CAAC,OAAOI,CAAC,EAAE;MACVa,OAAO,CAACC,KAAK,CAACd,CAAC,CAAC;MAChBC,KAAK,CAAC,eAAe,GAAGD,CAAC,CAACE,OAAO,CAAC;IACpC;EACF,CAAC;;EAED;EACAxB,UAAU,CAAC,mBAAmB,EAAGsB,CAAC,IAAK;IACrCA,CAAC,CAACe,cAAc,CAAC,CAAC;IAClBT,UAAU,CAAC,CAAC;EACd,CAAC,EAAE,CAACnB,IAAI,EAAEE,UAAU,CAAC,CAAC;EAEtB,oBACEP,OAAA;IAAKkC,KAAK,EAAE;MAAEC,IAAI,EAAE,CAAC;MAAEC,OAAO,EAAE,MAAM;MAAEC,aAAa,EAAE;IAAS,CAAE;IAAAC,QAAA,gBAChEtC,OAAA;MAAKkC,KAAK,EAAE;QAAEE,OAAO,EAAE,MAAM;QAAEG,YAAY,EAAE,gBAAgB;QAAEC,SAAS,EAAE,MAAM;QAAEC,UAAU,EAAE;MAAS,CAAE;MAAAH,QAAA,GACtGjC,IAAI,CAACkB,GAAG,CAAEE,GAAG,iBACZzB,OAAA;QAEE0C,OAAO,EAAEA,CAAA,KAAMlC,aAAa,CAACiB,GAAG,CAACd,IAAI,CAAE;QACvCuB,KAAK,EAAE;UACLS,OAAO,EAAE,SAAS;UAClBC,MAAM,EAAE,SAAS;UACjBC,eAAe,EAAEpB,GAAG,CAACd,IAAI,KAAKJ,UAAU,GAAG,MAAM,GAAG;QACtD,CAAE;QAAA+B,QAAA,EAEDb,GAAG,CAACd,IAAI,CAACmC,KAAK,CAAC,GAAG,CAAC,CAACC,GAAG,CAAC;MAAC,GARrBtB,GAAG,CAACd,IAAI;QAAAqC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OASV,CACN,CAAC,EACD5C,UAAU,iBACTP,OAAA;QAAQkC,KAAK,EAAE;UAAEkB,UAAU,EAAE,MAAM;UAAEC,WAAW,EAAE;QAAE,CAAE;QAACX,OAAO,EAAElB,UAAW;QAAAc,QAAA,EAAC;MAE5E;QAAAU,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CACT;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC,eACNnD,OAAA;MAAKkC,KAAK,EAAE;QAAEC,IAAI,EAAE;MAAE,CAAE;MAAAG,QAAA,EACrBjC,IAAI,CAACiD,MAAM,GAAG,CAAC,IAAI/C,UAAU,iBAC5BP,OAAA,CAACL,MAAM;QACL4D,MAAM,EAAC,MAAM;QACbC,eAAe,EAAC,YAAY;QAC5BC,KAAK,EAAE,EAAArD,UAAA,GAAAC,IAAI,CAACQ,IAAI,CAAEC,CAAC,IAAKA,CAAC,CAACH,IAAI,KAAKJ,UAAU,CAAC,cAAAH,UAAA,uBAAvCA,UAAA,CAAyCW,OAAO,KAAI,EAAG;QAC9D2C,QAAQ,EAAErC,YAAa;QACvBsC,OAAO,EAAE;UAAEC,QAAQ,EAAE;QAAG;MAAE;QAAAZ,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC3B;IACF;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV;AAAChD,EAAA,CAnGuBF,UAAU;EAAA,QA2DhCL,UAAU;AAAA;AAAAiE,EAAA,GA3DY5D,UAAU;AAAA,IAAA4D,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}