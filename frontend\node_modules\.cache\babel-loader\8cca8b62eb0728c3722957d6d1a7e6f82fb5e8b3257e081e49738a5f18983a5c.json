{"ast": null, "code": "import { slicedToArray as _slicedToArray, objectWithoutProperties as _objectWithoutProperties } from '../_virtual/_rollupPluginBabelHelpers.js';\nimport state from 'state-local';\nimport config$1 from '../config/index.js';\nimport validators from '../validators/index.js';\nimport compose from '../utils/compose.js';\nimport merge from '../utils/deepMerge.js';\nimport makeCancelable from '../utils/makeCancelable.js';\n\n/** the local state of the module */\n\nvar _state$create = state.create({\n    config: config$1,\n    isInitialized: false,\n    resolve: null,\n    reject: null,\n    monaco: null\n  }),\n  _state$create2 = _slicedToArray(_state$create, 2),\n  getState = _state$create2[0],\n  setState = _state$create2[1];\n/**\n * set the loader configuration\n * @param {Object} config - the configuration object\n */\n\nfunction config(globalConfig) {\n  var _validators$config = validators.config(globalConfig),\n    monaco = _validators$config.monaco,\n    config = _objectWithoutProperties(_validators$config, [\"monaco\"]);\n  setState(function (state) {\n    return {\n      config: merge(state.config, config),\n      monaco: monaco\n    };\n  });\n}\n/**\n * handles the initialization of the monaco-editor\n * @return {Promise} - returns an instance of monaco (with a cancelable promise)\n */\n\nfunction init() {\n  var state = getState(function (_ref) {\n    var monaco = _ref.monaco,\n      isInitialized = _ref.isInitialized,\n      resolve = _ref.resolve;\n    return {\n      monaco: monaco,\n      isInitialized: isInitialized,\n      resolve: resolve\n    };\n  });\n  if (!state.isInitialized) {\n    setState({\n      isInitialized: true\n    });\n    if (state.monaco) {\n      state.resolve(state.monaco);\n      return makeCancelable(wrapperPromise);\n    }\n    if (window.monaco && window.monaco.editor) {\n      storeMonacoInstance(window.monaco);\n      state.resolve(window.monaco);\n      return makeCancelable(wrapperPromise);\n    }\n    compose(injectScripts, getMonacoLoaderScript)(configureLoader);\n  }\n  return makeCancelable(wrapperPromise);\n}\n/**\n * injects provided scripts into the document.body\n * @param {Object} script - an HTML script element\n * @return {Object} - the injected HTML script element\n */\n\nfunction injectScripts(script) {\n  return document.body.appendChild(script);\n}\n/**\n * creates an HTML script element with/without provided src\n * @param {string} [src] - the source path of the script\n * @return {Object} - the created HTML script element\n */\n\nfunction createScript(src) {\n  var script = document.createElement('script');\n  return src && (script.src = src), script;\n}\n/**\n * creates an HTML script element with the monaco loader src\n * @return {Object} - the created HTML script element\n */\n\nfunction getMonacoLoaderScript(configureLoader) {\n  var state = getState(function (_ref2) {\n    var config = _ref2.config,\n      reject = _ref2.reject;\n    return {\n      config: config,\n      reject: reject\n    };\n  });\n  var loaderScript = createScript(\"\".concat(state.config.paths.vs, \"/loader.js\"));\n  loaderScript.onload = function () {\n    return configureLoader();\n  };\n  loaderScript.onerror = state.reject;\n  return loaderScript;\n}\n/**\n * configures the monaco loader\n */\n\nfunction configureLoader() {\n  var state = getState(function (_ref3) {\n    var config = _ref3.config,\n      resolve = _ref3.resolve,\n      reject = _ref3.reject;\n    return {\n      config: config,\n      resolve: resolve,\n      reject: reject\n    };\n  });\n  var require = window.require;\n  require.config(state.config);\n  require(['vs/editor/editor.main'], function (monaco) {\n    storeMonacoInstance(monaco);\n    state.resolve(monaco);\n  }, function (error) {\n    state.reject(error);\n  });\n}\n/**\n * store monaco instance in local state\n */\n\nfunction storeMonacoInstance(monaco) {\n  if (!getState().monaco) {\n    setState({\n      monaco: monaco\n    });\n  }\n}\n/**\n * internal helper function\n * extracts stored monaco instance\n * @return {Object|null} - the monaco instance\n */\n\nfunction __getMonacoInstance() {\n  return getState(function (_ref4) {\n    var monaco = _ref4.monaco;\n    return monaco;\n  });\n}\nvar wrapperPromise = new Promise(function (resolve, reject) {\n  return setState({\n    resolve: resolve,\n    reject: reject\n  });\n});\nvar loader = {\n  config: config,\n  init: init,\n  __getMonacoInstance: __getMonacoInstance\n};\nexport default loader;", "map": {"version": 3, "names": ["slicedToArray", "_slicedToArray", "objectWithoutProperties", "_objectWithoutProperties", "state", "config$1", "validators", "compose", "merge", "makeCancelable", "_state$create", "create", "config", "isInitialized", "resolve", "reject", "monaco", "_state$create2", "getState", "setState", "globalConfig", "_validators$config", "init", "_ref", "wrapperPromise", "window", "editor", "storeMonacoInstance", "injectScripts", "getMonacoLoaderScript", "configure<PERSON><PERSON><PERSON>", "script", "document", "body", "append<PERSON><PERSON><PERSON>", "createScript", "src", "createElement", "_ref2", "loaderScript", "concat", "paths", "vs", "onload", "onerror", "_ref3", "require", "error", "__getMonacoInstance", "_ref4", "Promise", "loader"], "sources": ["D:/Documents_2/New Coding/CoderBuddy/CoderBuddy/frontend/node_modules/@monaco-editor/loader/lib/es/loader/index.js"], "sourcesContent": ["import { slicedToArray as _slicedToArray, objectWithoutProperties as _objectWithoutProperties } from '../_virtual/_rollupPluginBabelHelpers.js';\nimport state from 'state-local';\nimport config$1 from '../config/index.js';\nimport validators from '../validators/index.js';\nimport compose from '../utils/compose.js';\nimport merge from '../utils/deepMerge.js';\nimport makeCancelable from '../utils/makeCancelable.js';\n\n/** the local state of the module */\n\nvar _state$create = state.create({\n  config: config$1,\n  isInitialized: false,\n  resolve: null,\n  reject: null,\n  monaco: null\n}),\n    _state$create2 = _slicedToArray(_state$create, 2),\n    getState = _state$create2[0],\n    setState = _state$create2[1];\n/**\n * set the loader configuration\n * @param {Object} config - the configuration object\n */\n\n\nfunction config(globalConfig) {\n  var _validators$config = validators.config(globalConfig),\n      monaco = _validators$config.monaco,\n      config = _objectWithoutProperties(_validators$config, [\"monaco\"]);\n\n  setState(function (state) {\n    return {\n      config: merge(state.config, config),\n      monaco: monaco\n    };\n  });\n}\n/**\n * handles the initialization of the monaco-editor\n * @return {Promise} - returns an instance of monaco (with a cancelable promise)\n */\n\n\nfunction init() {\n  var state = getState(function (_ref) {\n    var monaco = _ref.monaco,\n        isInitialized = _ref.isInitialized,\n        resolve = _ref.resolve;\n    return {\n      monaco: monaco,\n      isInitialized: isInitialized,\n      resolve: resolve\n    };\n  });\n\n  if (!state.isInitialized) {\n    setState({\n      isInitialized: true\n    });\n\n    if (state.monaco) {\n      state.resolve(state.monaco);\n      return makeCancelable(wrapperPromise);\n    }\n\n    if (window.monaco && window.monaco.editor) {\n      storeMonacoInstance(window.monaco);\n      state.resolve(window.monaco);\n      return makeCancelable(wrapperPromise);\n    }\n\n    compose(injectScripts, getMonacoLoaderScript)(configureLoader);\n  }\n\n  return makeCancelable(wrapperPromise);\n}\n/**\n * injects provided scripts into the document.body\n * @param {Object} script - an HTML script element\n * @return {Object} - the injected HTML script element\n */\n\n\nfunction injectScripts(script) {\n  return document.body.appendChild(script);\n}\n/**\n * creates an HTML script element with/without provided src\n * @param {string} [src] - the source path of the script\n * @return {Object} - the created HTML script element\n */\n\n\nfunction createScript(src) {\n  var script = document.createElement('script');\n  return src && (script.src = src), script;\n}\n/**\n * creates an HTML script element with the monaco loader src\n * @return {Object} - the created HTML script element\n */\n\n\nfunction getMonacoLoaderScript(configureLoader) {\n  var state = getState(function (_ref2) {\n    var config = _ref2.config,\n        reject = _ref2.reject;\n    return {\n      config: config,\n      reject: reject\n    };\n  });\n  var loaderScript = createScript(\"\".concat(state.config.paths.vs, \"/loader.js\"));\n\n  loaderScript.onload = function () {\n    return configureLoader();\n  };\n\n  loaderScript.onerror = state.reject;\n  return loaderScript;\n}\n/**\n * configures the monaco loader\n */\n\n\nfunction configureLoader() {\n  var state = getState(function (_ref3) {\n    var config = _ref3.config,\n        resolve = _ref3.resolve,\n        reject = _ref3.reject;\n    return {\n      config: config,\n      resolve: resolve,\n      reject: reject\n    };\n  });\n  var require = window.require;\n\n  require.config(state.config);\n\n  require(['vs/editor/editor.main'], function (monaco) {\n    storeMonacoInstance(monaco);\n    state.resolve(monaco);\n  }, function (error) {\n    state.reject(error);\n  });\n}\n/**\n * store monaco instance in local state\n */\n\n\nfunction storeMonacoInstance(monaco) {\n  if (!getState().monaco) {\n    setState({\n      monaco: monaco\n    });\n  }\n}\n/**\n * internal helper function\n * extracts stored monaco instance\n * @return {Object|null} - the monaco instance\n */\n\n\nfunction __getMonacoInstance() {\n  return getState(function (_ref4) {\n    var monaco = _ref4.monaco;\n    return monaco;\n  });\n}\n\nvar wrapperPromise = new Promise(function (resolve, reject) {\n  return setState({\n    resolve: resolve,\n    reject: reject\n  });\n});\nvar loader = {\n  config: config,\n  init: init,\n  __getMonacoInstance: __getMonacoInstance\n};\n\nexport default loader;\n"], "mappings": "AAAA,SAASA,aAAa,IAAIC,cAAc,EAAEC,uBAAuB,IAAIC,wBAAwB,QAAQ,0CAA0C;AAC/I,OAAOC,KAAK,MAAM,aAAa;AAC/B,OAAOC,QAAQ,MAAM,oBAAoB;AACzC,OAAOC,UAAU,MAAM,wBAAwB;AAC/C,OAAOC,OAAO,MAAM,qBAAqB;AACzC,OAAOC,KAAK,MAAM,uBAAuB;AACzC,OAAOC,cAAc,MAAM,4BAA4B;;AAEvD;;AAEA,IAAIC,aAAa,GAAGN,KAAK,CAACO,MAAM,CAAC;IAC/BC,MAAM,EAAEP,QAAQ;IAChBQ,aAAa,EAAE,KAAK;IACpBC,OAAO,EAAE,IAAI;IACbC,MAAM,EAAE,IAAI;IACZC,MAAM,EAAE;EACV,CAAC,CAAC;EACEC,cAAc,GAAGhB,cAAc,CAACS,aAAa,EAAE,CAAC,CAAC;EACjDQ,QAAQ,GAAGD,cAAc,CAAC,CAAC,CAAC;EAC5BE,QAAQ,GAAGF,cAAc,CAAC,CAAC,CAAC;AAChC;AACA;AACA;AACA;;AAGA,SAASL,MAAMA,CAACQ,YAAY,EAAE;EAC5B,IAAIC,kBAAkB,GAAGf,UAAU,CAACM,MAAM,CAACQ,YAAY,CAAC;IACpDJ,MAAM,GAAGK,kBAAkB,CAACL,MAAM;IAClCJ,MAAM,GAAGT,wBAAwB,CAACkB,kBAAkB,EAAE,CAAC,QAAQ,CAAC,CAAC;EAErEF,QAAQ,CAAC,UAAUf,KAAK,EAAE;IACxB,OAAO;MACLQ,MAAM,EAAEJ,KAAK,CAACJ,KAAK,CAACQ,MAAM,EAAEA,MAAM,CAAC;MACnCI,MAAM,EAAEA;IACV,CAAC;EACH,CAAC,CAAC;AACJ;AACA;AACA;AACA;AACA;;AAGA,SAASM,IAAIA,CAAA,EAAG;EACd,IAAIlB,KAAK,GAAGc,QAAQ,CAAC,UAAUK,IAAI,EAAE;IACnC,IAAIP,MAAM,GAAGO,IAAI,CAACP,MAAM;MACpBH,aAAa,GAAGU,IAAI,CAACV,aAAa;MAClCC,OAAO,GAAGS,IAAI,CAACT,OAAO;IAC1B,OAAO;MACLE,MAAM,EAAEA,MAAM;MACdH,aAAa,EAAEA,aAAa;MAC5BC,OAAO,EAAEA;IACX,CAAC;EACH,CAAC,CAAC;EAEF,IAAI,CAACV,KAAK,CAACS,aAAa,EAAE;IACxBM,QAAQ,CAAC;MACPN,aAAa,EAAE;IACjB,CAAC,CAAC;IAEF,IAAIT,KAAK,CAACY,MAAM,EAAE;MAChBZ,KAAK,CAACU,OAAO,CAACV,KAAK,CAACY,MAAM,CAAC;MAC3B,OAAOP,cAAc,CAACe,cAAc,CAAC;IACvC;IAEA,IAAIC,MAAM,CAACT,MAAM,IAAIS,MAAM,CAACT,MAAM,CAACU,MAAM,EAAE;MACzCC,mBAAmB,CAACF,MAAM,CAACT,MAAM,CAAC;MAClCZ,KAAK,CAACU,OAAO,CAACW,MAAM,CAACT,MAAM,CAAC;MAC5B,OAAOP,cAAc,CAACe,cAAc,CAAC;IACvC;IAEAjB,OAAO,CAACqB,aAAa,EAAEC,qBAAqB,CAAC,CAACC,eAAe,CAAC;EAChE;EAEA,OAAOrB,cAAc,CAACe,cAAc,CAAC;AACvC;AACA;AACA;AACA;AACA;AACA;;AAGA,SAASI,aAAaA,CAACG,MAAM,EAAE;EAC7B,OAAOC,QAAQ,CAACC,IAAI,CAACC,WAAW,CAACH,MAAM,CAAC;AAC1C;AACA;AACA;AACA;AACA;AACA;;AAGA,SAASI,YAAYA,CAACC,GAAG,EAAE;EACzB,IAAIL,MAAM,GAAGC,QAAQ,CAACK,aAAa,CAAC,QAAQ,CAAC;EAC7C,OAAOD,GAAG,KAAKL,MAAM,CAACK,GAAG,GAAGA,GAAG,CAAC,EAAEL,MAAM;AAC1C;AACA;AACA;AACA;AACA;;AAGA,SAASF,qBAAqBA,CAACC,eAAe,EAAE;EAC9C,IAAI1B,KAAK,GAAGc,QAAQ,CAAC,UAAUoB,KAAK,EAAE;IACpC,IAAI1B,MAAM,GAAG0B,KAAK,CAAC1B,MAAM;MACrBG,MAAM,GAAGuB,KAAK,CAACvB,MAAM;IACzB,OAAO;MACLH,MAAM,EAAEA,MAAM;MACdG,MAAM,EAAEA;IACV,CAAC;EACH,CAAC,CAAC;EACF,IAAIwB,YAAY,GAAGJ,YAAY,CAAC,EAAE,CAACK,MAAM,CAACpC,KAAK,CAACQ,MAAM,CAAC6B,KAAK,CAACC,EAAE,EAAE,YAAY,CAAC,CAAC;EAE/EH,YAAY,CAACI,MAAM,GAAG,YAAY;IAChC,OAAOb,eAAe,CAAC,CAAC;EAC1B,CAAC;EAEDS,YAAY,CAACK,OAAO,GAAGxC,KAAK,CAACW,MAAM;EACnC,OAAOwB,YAAY;AACrB;AACA;AACA;AACA;;AAGA,SAAST,eAAeA,CAAA,EAAG;EACzB,IAAI1B,KAAK,GAAGc,QAAQ,CAAC,UAAU2B,KAAK,EAAE;IACpC,IAAIjC,MAAM,GAAGiC,KAAK,CAACjC,MAAM;MACrBE,OAAO,GAAG+B,KAAK,CAAC/B,OAAO;MACvBC,MAAM,GAAG8B,KAAK,CAAC9B,MAAM;IACzB,OAAO;MACLH,MAAM,EAAEA,MAAM;MACdE,OAAO,EAAEA,OAAO;MAChBC,MAAM,EAAEA;IACV,CAAC;EACH,CAAC,CAAC;EACF,IAAI+B,OAAO,GAAGrB,MAAM,CAACqB,OAAO;EAE5BA,OAAO,CAAClC,MAAM,CAACR,KAAK,CAACQ,MAAM,CAAC;EAE5BkC,OAAO,CAAC,CAAC,uBAAuB,CAAC,EAAE,UAAU9B,MAAM,EAAE;IACnDW,mBAAmB,CAACX,MAAM,CAAC;IAC3BZ,KAAK,CAACU,OAAO,CAACE,MAAM,CAAC;EACvB,CAAC,EAAE,UAAU+B,KAAK,EAAE;IAClB3C,KAAK,CAACW,MAAM,CAACgC,KAAK,CAAC;EACrB,CAAC,CAAC;AACJ;AACA;AACA;AACA;;AAGA,SAASpB,mBAAmBA,CAACX,MAAM,EAAE;EACnC,IAAI,CAACE,QAAQ,CAAC,CAAC,CAACF,MAAM,EAAE;IACtBG,QAAQ,CAAC;MACPH,MAAM,EAAEA;IACV,CAAC,CAAC;EACJ;AACF;AACA;AACA;AACA;AACA;AACA;;AAGA,SAASgC,mBAAmBA,CAAA,EAAG;EAC7B,OAAO9B,QAAQ,CAAC,UAAU+B,KAAK,EAAE;IAC/B,IAAIjC,MAAM,GAAGiC,KAAK,CAACjC,MAAM;IACzB,OAAOA,MAAM;EACf,CAAC,CAAC;AACJ;AAEA,IAAIQ,cAAc,GAAG,IAAI0B,OAAO,CAAC,UAAUpC,OAAO,EAAEC,MAAM,EAAE;EAC1D,OAAOI,QAAQ,CAAC;IACdL,OAAO,EAAEA,OAAO;IAChBC,MAAM,EAAEA;EACV,CAAC,CAAC;AACJ,CAAC,CAAC;AACF,IAAIoC,MAAM,GAAG;EACXvC,MAAM,EAAEA,MAAM;EACdU,IAAI,EAAEA,IAAI;EACV0B,mBAAmB,EAAEA;AACvB,CAAC;AAED,eAAeG,MAAM", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}