{"ast": null, "code": "var _jsxFileName = \"D:\\\\Documents_2\\\\New Coding\\\\CoderBuddy\\\\CoderBuddy\\\\frontend\\\\src\\\\components\\\\ProjectSelector.js\",\n  _s = $RefreshSig$();\nimport React, { useEffect, useState } from 'react';\nimport { listProjects, createProject } from '../services/api';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nexport default function ProjectSelector({\n  projectId,\n  setProjectId\n}) {\n  _s();\n  const [projects, setProjects] = useState([]);\n  const [showModal, setShowModal] = useState(false);\n  const [newProject, setNewProject] = useState('');\n  const [error, setError] = useState('');\n  useEffect(() => {\n    loadProjects();\n  }, []);\n  async function loadProjects() {\n    try {\n      const data = await listProjects();\n      setProjects(data);\n    } catch (e) {\n      setError(e.message);\n    }\n  }\n  async function handleCreate() {\n    if (!newProject.trim()) return;\n    try {\n      await createProject(newProject.trim());\n      setShowModal(false);\n      setNewProject('');\n      setError('');\n      await loadProjects();\n      setProjectId(newProject.trim());\n    } catch (e) {\n      setError(e.message);\n    }\n  }\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    style: {\n      display: 'flex',\n      alignItems: 'center',\n      gap: 8\n    },\n    children: [/*#__PURE__*/_jsxDEV(\"b\", {\n      children: \"Project:\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 39,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n      value: projectId,\n      onChange: e => setProjectId(e.target.value),\n      children: projects.map(p => /*#__PURE__*/_jsxDEV(\"option\", {\n        value: p.project_id,\n        children: p.project_id\n      }, p.project_id, false, {\n        fileName: _jsxFileName,\n        lineNumber: 42,\n        columnNumber: 11\n      }, this))\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 40,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n      onClick: () => setShowModal(true),\n      children: \"New Project\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 45,\n      columnNumber: 7\n    }, this), showModal && /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        position: 'fixed',\n        inset: 0,\n        background: 'rgba(0,0,0,0.3)',\n        display: 'flex',\n        alignItems: 'center',\n        justifyContent: 'center',\n        zIndex: 1000\n      },\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          background: '#fff',\n          padding: 24,\n          borderRadius: 8,\n          minWidth: 300\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n          children: \"Create New Project\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 49,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n          value: newProject,\n          onChange: e => setNewProject(e.target.value),\n          placeholder: \"Project name\",\n          style: {\n            width: '100%',\n            marginBottom: 8\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 50,\n          columnNumber: 13\n        }, this), error && /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            color: 'red',\n            marginBottom: 8\n          },\n          children: error\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 51,\n          columnNumber: 23\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            display: 'flex',\n            justifyContent: 'flex-end',\n            gap: 8\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: () => setShowModal(false),\n            children: \"Cancel\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 53,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: handleCreate,\n            children: \"Create\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 54,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 52,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 48,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 47,\n      columnNumber: 9\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 38,\n    columnNumber: 5\n  }, this);\n}\n_s(ProjectSelector, \"2XKEwd+QxQC5gJ5ujsy3PaovbLg=\");\n_c = ProjectSelector;\nvar _c;\n$RefreshReg$(_c, \"ProjectSelector\");", "map": {"version": 3, "names": ["React", "useEffect", "useState", "listProjects", "createProject", "jsxDEV", "_jsxDEV", "ProjectSelector", "projectId", "setProjectId", "_s", "projects", "setProjects", "showModal", "setShowModal", "newProject", "setNewProject", "error", "setError", "loadProjects", "data", "e", "message", "handleCreate", "trim", "style", "display", "alignItems", "gap", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "value", "onChange", "target", "map", "p", "project_id", "onClick", "position", "inset", "background", "justifyContent", "zIndex", "padding", "borderRadius", "min<PERSON><PERSON><PERSON>", "placeholder", "width", "marginBottom", "color", "_c", "$RefreshReg$"], "sources": ["D:/Documents_2/New Coding/CoderBuddy/CoderBuddy/frontend/src/components/ProjectSelector.js"], "sourcesContent": ["import React, { useEffect, useState } from 'react';\r\nimport { listProjects, createProject } from '../services/api';\r\n\r\nexport default function ProjectSelector({ projectId, setProjectId }) {\r\n  const [projects, setProjects] = useState([]);\r\n  const [showModal, setShowModal] = useState(false);\r\n  const [newProject, setNewProject] = useState('');\r\n  const [error, setError] = useState('');\r\n\r\n  useEffect(() => {\r\n    loadProjects();\r\n  }, []);\r\n\r\n  async function loadProjects() {\r\n    try {\r\n      const data = await listProjects();\r\n      setProjects(data);\r\n    } catch (e) {\r\n      setError(e.message);\r\n    }\r\n  }\r\n\r\n  async function handleCreate() {\r\n    if (!newProject.trim()) return;\r\n    try {\r\n      await createProject(newProject.trim());\r\n      setShowModal(false);\r\n      setNewProject('');\r\n      setError('');\r\n      await loadProjects();\r\n      setProjectId(newProject.trim());\r\n    } catch (e) {\r\n      setError(e.message);\r\n    }\r\n  }\r\n\r\n  return (\r\n    <div style={{ display: 'flex', alignItems: 'center', gap: 8 }}>\r\n      <b>Project:</b>\r\n      <select value={projectId} onChange={e => setProjectId(e.target.value)}>\r\n        {projects.map(p => (\r\n          <option key={p.project_id} value={p.project_id}>{p.project_id}</option>\r\n        ))}\r\n      </select>\r\n      <button onClick={() => setShowModal(true)}>New Project</button>\r\n      {showModal && (\r\n        <div style={{ position: 'fixed', inset: 0, background: 'rgba(0,0,0,0.3)', display: 'flex', alignItems: 'center', justifyContent: 'center', zIndex: 1000 }}>\r\n          <div style={{ background: '#fff', padding: 24, borderRadius: 8, minWidth: 300 }}>\r\n            <h3>Create New Project</h3>\r\n            <input value={newProject} onChange={e => setNewProject(e.target.value)} placeholder=\"Project name\" style={{ width: '100%', marginBottom: 8 }} />\r\n            {error && <div style={{ color: 'red', marginBottom: 8 }}>{error}</div>}\r\n            <div style={{ display: 'flex', justifyContent: 'flex-end', gap: 8 }}>\r\n              <button onClick={() => setShowModal(false)}>Cancel</button>\r\n              <button onClick={handleCreate}>Create</button>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      )}\r\n    </div>\r\n  );\r\n} "], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,SAAS,EAAEC,QAAQ,QAAQ,OAAO;AAClD,SAASC,YAAY,EAAEC,aAAa,QAAQ,iBAAiB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE9D,eAAe,SAASC,eAAeA,CAAC;EAAEC,SAAS;EAAEC;AAAa,CAAC,EAAE;EAAAC,EAAA;EACnE,MAAM,CAACC,QAAQ,EAAEC,WAAW,CAAC,GAAGV,QAAQ,CAAC,EAAE,CAAC;EAC5C,MAAM,CAACW,SAAS,EAAEC,YAAY,CAAC,GAAGZ,QAAQ,CAAC,KAAK,CAAC;EACjD,MAAM,CAACa,UAAU,EAAEC,aAAa,CAAC,GAAGd,QAAQ,CAAC,EAAE,CAAC;EAChD,MAAM,CAACe,KAAK,EAAEC,QAAQ,CAAC,GAAGhB,QAAQ,CAAC,EAAE,CAAC;EAEtCD,SAAS,CAAC,MAAM;IACdkB,YAAY,CAAC,CAAC;EAChB,CAAC,EAAE,EAAE,CAAC;EAEN,eAAeA,YAAYA,CAAA,EAAG;IAC5B,IAAI;MACF,MAAMC,IAAI,GAAG,MAAMjB,YAAY,CAAC,CAAC;MACjCS,WAAW,CAACQ,IAAI,CAAC;IACnB,CAAC,CAAC,OAAOC,CAAC,EAAE;MACVH,QAAQ,CAACG,CAAC,CAACC,OAAO,CAAC;IACrB;EACF;EAEA,eAAeC,YAAYA,CAAA,EAAG;IAC5B,IAAI,CAACR,UAAU,CAACS,IAAI,CAAC,CAAC,EAAE;IACxB,IAAI;MACF,MAAMpB,aAAa,CAACW,UAAU,CAACS,IAAI,CAAC,CAAC,CAAC;MACtCV,YAAY,CAAC,KAAK,CAAC;MACnBE,aAAa,CAAC,EAAE,CAAC;MACjBE,QAAQ,CAAC,EAAE,CAAC;MACZ,MAAMC,YAAY,CAAC,CAAC;MACpBV,YAAY,CAACM,UAAU,CAACS,IAAI,CAAC,CAAC,CAAC;IACjC,CAAC,CAAC,OAAOH,CAAC,EAAE;MACVH,QAAQ,CAACG,CAAC,CAACC,OAAO,CAAC;IACrB;EACF;EAEA,oBACEhB,OAAA;IAAKmB,KAAK,EAAE;MAAEC,OAAO,EAAE,MAAM;MAAEC,UAAU,EAAE,QAAQ;MAAEC,GAAG,EAAE;IAAE,CAAE;IAAAC,QAAA,gBAC5DvB,OAAA;MAAAuB,QAAA,EAAG;IAAQ;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAG,CAAC,eACf3B,OAAA;MAAQ4B,KAAK,EAAE1B,SAAU;MAAC2B,QAAQ,EAAEd,CAAC,IAAIZ,YAAY,CAACY,CAAC,CAACe,MAAM,CAACF,KAAK,CAAE;MAAAL,QAAA,EACnElB,QAAQ,CAAC0B,GAAG,CAACC,CAAC,iBACbhC,OAAA;QAA2B4B,KAAK,EAAEI,CAAC,CAACC,UAAW;QAAAV,QAAA,EAAES,CAAC,CAACC;MAAU,GAAhDD,CAAC,CAACC,UAAU;QAAAT,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAA6C,CACvE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACI,CAAC,eACT3B,OAAA;MAAQkC,OAAO,EAAEA,CAAA,KAAM1B,YAAY,CAAC,IAAI,CAAE;MAAAe,QAAA,EAAC;IAAW;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAQ,CAAC,EAC9DpB,SAAS,iBACRP,OAAA;MAAKmB,KAAK,EAAE;QAAEgB,QAAQ,EAAE,OAAO;QAAEC,KAAK,EAAE,CAAC;QAAEC,UAAU,EAAE,iBAAiB;QAAEjB,OAAO,EAAE,MAAM;QAAEC,UAAU,EAAE,QAAQ;QAAEiB,cAAc,EAAE,QAAQ;QAAEC,MAAM,EAAE;MAAK,CAAE;MAAAhB,QAAA,eACxJvB,OAAA;QAAKmB,KAAK,EAAE;UAAEkB,UAAU,EAAE,MAAM;UAAEG,OAAO,EAAE,EAAE;UAAEC,YAAY,EAAE,CAAC;UAAEC,QAAQ,EAAE;QAAI,CAAE;QAAAnB,QAAA,gBAC9EvB,OAAA;UAAAuB,QAAA,EAAI;QAAkB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAC3B3B,OAAA;UAAO4B,KAAK,EAAEnB,UAAW;UAACoB,QAAQ,EAAEd,CAAC,IAAIL,aAAa,CAACK,CAAC,CAACe,MAAM,CAACF,KAAK,CAAE;UAACe,WAAW,EAAC,cAAc;UAACxB,KAAK,EAAE;YAAEyB,KAAK,EAAE,MAAM;YAAEC,YAAY,EAAE;UAAE;QAAE;UAAArB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,EAC/IhB,KAAK,iBAAIX,OAAA;UAAKmB,KAAK,EAAE;YAAE2B,KAAK,EAAE,KAAK;YAAED,YAAY,EAAE;UAAE,CAAE;UAAAtB,QAAA,EAAEZ;QAAK;UAAAa,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eACtE3B,OAAA;UAAKmB,KAAK,EAAE;YAAEC,OAAO,EAAE,MAAM;YAAEkB,cAAc,EAAE,UAAU;YAAEhB,GAAG,EAAE;UAAE,CAAE;UAAAC,QAAA,gBAClEvB,OAAA;YAAQkC,OAAO,EAAEA,CAAA,KAAM1B,YAAY,CAAC,KAAK,CAAE;YAAAe,QAAA,EAAC;UAAM;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eAC3D3B,OAAA;YAAQkC,OAAO,EAAEjB,YAAa;YAAAM,QAAA,EAAC;UAAM;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC3C,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CACN;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACE,CAAC;AAEV;AAACvB,EAAA,CAzDuBH,eAAe;AAAA8C,EAAA,GAAf9C,eAAe;AAAA,IAAA8C,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}