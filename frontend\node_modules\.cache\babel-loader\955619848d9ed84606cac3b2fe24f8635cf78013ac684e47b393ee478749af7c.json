{"ast": null, "code": "var _jsxFileName = \"D:\\\\Documents_2\\\\New Coding\\\\CoderBuddy\\\\CoderBuddy\\\\frontend\\\\src\\\\App.js\",\n  _s = $RefreshSig$();\nimport React, { useState } from 'react';\nimport FileExplorer from './components/FileExplorer';\nimport EditorTabs from './components/EditorTabs';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nexport default function App() {\n  _s();\n  const [selectedFile, setSelectedFile] = useState(null);\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    style: {\n      display: 'flex',\n      height: '100vh'\n    },\n    children: [/*#__PURE__*/_jsxDEV(FileExplorer, {\n      onSelectFile: setSelectedFile\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 10,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(EditorTabs, {\n      selectedFile: selectedFile\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 11,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 9,\n    columnNumber: 5\n  }, this);\n}\n_s(App, \"cz3B5sQOxlrekAnUFUPeMaA2gqY=\");\n_c = App;\nvar _c;\n$RefreshReg$(_c, \"App\");", "map": {"version": 3, "names": ["React", "useState", "FileExplorer", "EditorTabs", "jsxDEV", "_jsxDEV", "App", "_s", "selectedFile", "setSelectedFile", "style", "display", "height", "children", "onSelectFile", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "$RefreshReg$"], "sources": ["D:/Documents_2/New Coding/CoderBuddy/CoderBuddy/frontend/src/App.js"], "sourcesContent": ["import React, { useState } from 'react';\r\nimport FileExplorer from './components/FileExplorer';\r\nimport EditorTabs from './components/EditorTabs';\r\n\r\nexport default function App() {\r\n  const [selectedFile, setSelectedFile] = useState(null);\r\n\r\n  return (\r\n    <div style={{ display: 'flex', height: '100vh' }}>\r\n      <FileExplorer onSelectFile={setSelectedFile} />\r\n      <EditorTabs selectedFile={selectedFile} />\r\n    </div>\r\n  );\r\n} "], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,OAAOC,YAAY,MAAM,2BAA2B;AACpD,OAAOC,UAAU,MAAM,yBAAyB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEjD,eAAe,SAASC,GAAGA,CAAA,EAAG;EAAAC,EAAA;EAC5B,MAAM,CAACC,YAAY,EAAEC,eAAe,CAAC,GAAGR,QAAQ,CAAC,IAAI,CAAC;EAEtD,oBACEI,OAAA;IAAKK,KAAK,EAAE;MAAEC,OAAO,EAAE,MAAM;MAAEC,MAAM,EAAE;IAAQ,CAAE;IAAAC,QAAA,gBAC/CR,OAAA,CAACH,YAAY;MAACY,YAAY,EAAEL;IAAgB;MAAAM,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eAC/Cb,OAAA,CAACF,UAAU;MAACK,YAAY,EAAEA;IAAa;MAAAO,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACvC,CAAC;AAEV;AAACX,EAAA,CATuBD,GAAG;AAAAa,EAAA,GAAHb,GAAG;AAAA,IAAAa,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}