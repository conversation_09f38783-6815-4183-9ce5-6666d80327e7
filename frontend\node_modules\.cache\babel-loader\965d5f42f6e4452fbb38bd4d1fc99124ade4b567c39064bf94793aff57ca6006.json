{"ast": null, "code": "function isObject(value) {\n  return {}.toString.call(value).includes('Object');\n}\nexport default isObject;", "map": {"version": 3, "names": ["isObject", "value", "toString", "call", "includes"], "sources": ["D:/Documents_2/New Coding/CoderBuddy/CoderBuddy/frontend/node_modules/@monaco-editor/loader/lib/es/utils/isObject.js"], "sourcesContent": ["function isObject(value) {\n  return {}.toString.call(value).includes('Object');\n}\n\nexport default isObject;\n"], "mappings": "AAAA,SAASA,QAAQA,CAACC,KAAK,EAAE;EACvB,OAAO,CAAC,CAAC,CAACC,QAAQ,CAACC,IAAI,CAACF,KAAK,CAAC,CAACG,QAAQ,CAAC,QAAQ,CAAC;AACnD;AAEA,eAAeJ,QAAQ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}