{"ast": null, "code": "var _jsxFileName = \"D:\\\\Documents_2\\\\New Coding\\\\CoderBuddy\\\\CoderBuddy\\\\frontend\\\\src\\\\components\\\\SaveDiffModal.js\",\n  _s = $RefreshSig$();\nimport React from 'react';\nimport NiceModal, { useModal } from '@ebay/nice-modal-react';\nimport DiffViewer from 'react-diff-viewer-continued';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nfunction SaveDiffModal({\n  oldText,\n  newText,\n  filePath\n}) {\n  _s();\n  const modal = useModal();\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    style: {\n      padding: 16,\n      background: '#fff',\n      width: '80vw',\n      maxHeight: '80vh',\n      overflow: 'auto'\n    },\n    children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n      style: {\n        marginTop: 0\n      },\n      children: [\"Confirm Save \\u2013 \", filePath]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 9,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(DiffViewer, {\n      oldValue: oldText,\n      newValue: newText,\n      splitView: true,\n      showDiffOnly: false,\n      styles: {\n        variables: {\n          light: {\n            diffViewerBackground: '#f7f7f7'\n          }\n        }\n      }\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 10,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        marginTop: 16,\n        textAlign: 'right'\n      },\n      children: [/*#__PURE__*/_jsxDEV(\"button\", {\n        onClick: () => modal.resolve(true),\n        style: {\n          marginRight: 8\n        },\n        children: \"Save\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 24,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n        onClick: () => modal.remove(),\n        children: \"Cancel\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 27,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 23,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 8,\n    columnNumber: 5\n  }, this);\n}\n_s(SaveDiffModal, \"hDyDP9waVoSwitriRQZZW/zZCNw=\", false, function () {\n  return [useModal];\n});\n_c = SaveDiffModal;\nexport default _c2 = NiceModal.create(SaveDiffModal);\nvar _c, _c2;\n$RefreshReg$(_c, \"SaveDiffModal\");\n$RefreshReg$(_c2, \"%default%\");", "map": {"version": 3, "names": ["React", "NiceModal", "useModal", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "jsxDEV", "_jsxDEV", "SaveDiffModal", "oldText", "newText", "filePath", "_s", "modal", "style", "padding", "background", "width", "maxHeight", "overflow", "children", "marginTop", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "oldValue", "newValue", "splitView", "showDiffOnly", "styles", "variables", "light", "diffViewerBackground", "textAlign", "onClick", "resolve", "marginRight", "remove", "_c", "_c2", "create", "$RefreshReg$"], "sources": ["D:/Documents_2/New Coding/CoderBuddy/CoderBuddy/frontend/src/components/SaveDiffModal.js"], "sourcesContent": ["import React from 'react';\r\nimport NiceModal, { useModal } from '@ebay/nice-modal-react';\r\nimport Diff<PERSON>iewer from 'react-diff-viewer-continued';\r\n\r\nfunction SaveDiffModal({ oldText, newText, filePath }) {\r\n  const modal = useModal();\r\n  return (\r\n    <div style={{ padding: 16, background: '#fff', width: '80vw', maxHeight: '80vh', overflow: 'auto' }}>\r\n      <h3 style={{ marginTop: 0 }}>Confirm Save – {filePath}</h3>\r\n      <DiffViewer\r\n        oldValue={oldText}\r\n        newValue={newText}\r\n        splitView={true}\r\n        showDiffOnly={false}\r\n        styles={{\r\n          variables: {\r\n            light: {\r\n              diffViewerBackground: '#f7f7f7',\r\n            },\r\n          },\r\n        }}\r\n      />\r\n      <div style={{ marginTop: 16, textAlign: 'right' }}>\r\n        <button onClick={() => modal.resolve(true)} style={{ marginRight: 8 }}>\r\n          Save\r\n        </button>\r\n        <button onClick={() => modal.remove()}>Cancel</button>\r\n      </div>\r\n    </div>\r\n  );\r\n}\r\n\r\nexport default NiceModal.create(SaveDiffModal); "], "mappings": ";;AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,OAAOC,SAAS,IAAIC,QAAQ,QAAQ,wBAAwB;AAC5D,OAAOC,UAAU,MAAM,6BAA6B;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAErD,SAASC,aAAaA,CAAC;EAAEC,OAAO;EAAEC,OAAO;EAAEC;AAAS,CAAC,EAAE;EAAAC,EAAA;EACrD,MAAMC,KAAK,GAAGT,QAAQ,CAAC,CAAC;EACxB,oBACEG,OAAA;IAAKO,KAAK,EAAE;MAAEC,OAAO,EAAE,EAAE;MAAEC,UAAU,EAAE,MAAM;MAAEC,KAAK,EAAE,MAAM;MAAEC,SAAS,EAAE,MAAM;MAAEC,QAAQ,EAAE;IAAO,CAAE;IAAAC,QAAA,gBAClGb,OAAA;MAAIO,KAAK,EAAE;QAAEO,SAAS,EAAE;MAAE,CAAE;MAAAD,QAAA,GAAC,sBAAe,EAACT,QAAQ;IAAA;MAAAW,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAK,CAAC,eAC3DlB,OAAA,CAACF,UAAU;MACTqB,QAAQ,EAAEjB,OAAQ;MAClBkB,QAAQ,EAAEjB,OAAQ;MAClBkB,SAAS,EAAE,IAAK;MAChBC,YAAY,EAAE,KAAM;MACpBC,MAAM,EAAE;QACNC,SAAS,EAAE;UACTC,KAAK,EAAE;YACLC,oBAAoB,EAAE;UACxB;QACF;MACF;IAAE;MAAAX,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eACFlB,OAAA;MAAKO,KAAK,EAAE;QAAEO,SAAS,EAAE,EAAE;QAAEa,SAAS,EAAE;MAAQ,CAAE;MAAAd,QAAA,gBAChDb,OAAA;QAAQ4B,OAAO,EAAEA,CAAA,KAAMtB,KAAK,CAACuB,OAAO,CAAC,IAAI,CAAE;QAACtB,KAAK,EAAE;UAAEuB,WAAW,EAAE;QAAE,CAAE;QAAAjB,QAAA,EAAC;MAEvE;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC,eACTlB,OAAA;QAAQ4B,OAAO,EAAEA,CAAA,KAAMtB,KAAK,CAACyB,MAAM,CAAC,CAAE;QAAAlB,QAAA,EAAC;MAAM;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACnD,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV;AAACb,EAAA,CA1BQJ,aAAa;EAAA,QACNJ,QAAQ;AAAA;AAAAmC,EAAA,GADf/B,aAAa;AA4BtB,eAAAgC,GAAA,GAAerC,SAAS,CAACsC,MAAM,CAACjC,aAAa,CAAC;AAAC,IAAA+B,EAAA,EAAAC,GAAA;AAAAE,YAAA,CAAAH,EAAA;AAAAG,YAAA,CAAAF,GAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}