{"ast": null, "code": "var _jsxFileName = \"D:\\\\Documents_2\\\\New Coding\\\\CoderBuddy\\\\CoderBuddy\\\\frontend\\\\src\\\\components\\\\SettingsModal.js\";\nimport React from 'react';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst MODELS = [{\n  value: 'openrouter/auto',\n  label: 'Auto (OpenRouter)'\n}, {\n  value: 'openrouter/4o',\n  label: 'GPT-4o'\n}, {\n  value: 'openrouter/4o-mini',\n  label: 'GPT-4o Mini'\n}, {\n  value: 'openrouter/gpt-3.5-turbo',\n  label: 'GPT-3.5 Turbo'\n}, {\n  value: 'openrouter/deepseek-coder',\n  label: 'DeepSeek Coder'\n}, {\n  value: 'openrouter/gemini-pro',\n  label: 'Gemini Pro'\n}\n// Add more as desired\n];\nexport default function SettingsModal({\n  open,\n  onClose,\n  model,\n  setModel\n}) {\n  if (!open) return null;\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    style: {\n      position: 'fixed',\n      top: 0,\n      left: 0,\n      width: '100vw',\n      height: '100vh',\n      background: 'rgba(0,0,0,0.3)',\n      zIndex: 1000,\n      display: 'flex',\n      alignItems: 'center',\n      justifyContent: 'center'\n    },\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        background: '#fff',\n        borderRadius: 8,\n        padding: 32,\n        minWidth: 350,\n        boxShadow: '0 2px 16px #0002'\n      },\n      children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n        children: \"Settings\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 21,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          marginBottom: 24\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"label\", {\n          style: {\n            fontWeight: 'bold'\n          },\n          children: \"AI Model\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 23,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 23,\n          columnNumber: 65\n        }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n          value: model,\n          onChange: e => setModel(e.target.value),\n          style: {\n            width: '100%',\n            padding: 8,\n            marginTop: 4\n          },\n          children: MODELS.map(m => /*#__PURE__*/_jsxDEV(\"option\", {\n            value: m.value,\n            children: m.label\n          }, m.value, false, {\n            fileName: _jsxFileName,\n            lineNumber: 25,\n            columnNumber: 30\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 24,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 22,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          marginBottom: 24\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"label\", {\n          style: {\n            fontWeight: 'bold'\n          },\n          children: \"Other Settings\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 29,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            color: '#888',\n            fontSize: 13\n          },\n          children: \"More settings coming soon...\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 30,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 28,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n        onClick: onClose,\n        style: {\n          marginTop: 8,\n          padding: '8px 24px',\n          borderRadius: 4,\n          border: '1px solid #ccc',\n          background: '#f5f5f5',\n          cursor: 'pointer'\n        },\n        children: \"Close\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 32,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 20,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 16,\n    columnNumber: 5\n  }, this);\n}\n_c = SettingsModal;\nvar _c;\n$RefreshReg$(_c, \"SettingsModal\");", "map": {"version": 3, "names": ["React", "jsxDEV", "_jsxDEV", "MODELS", "value", "label", "SettingsModal", "open", "onClose", "model", "setModel", "style", "position", "top", "left", "width", "height", "background", "zIndex", "display", "alignItems", "justifyContent", "children", "borderRadius", "padding", "min<PERSON><PERSON><PERSON>", "boxShadow", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "marginBottom", "fontWeight", "onChange", "e", "target", "marginTop", "map", "m", "color", "fontSize", "onClick", "border", "cursor", "_c", "$RefreshReg$"], "sources": ["D:/Documents_2/New Coding/CoderBuddy/CoderBuddy/frontend/src/components/SettingsModal.js"], "sourcesContent": ["import React from 'react';\r\n\r\nconst MODELS = [\r\n  { value: 'openrouter/auto', label: 'Auto (OpenRouter)' },\r\n  { value: 'openrouter/4o', label: 'GPT-4o' },\r\n  { value: 'openrouter/4o-mini', label: 'GPT-4o Mini' },\r\n  { value: 'openrouter/gpt-3.5-turbo', label: 'GPT-3.5 Turbo' },\r\n  { value: 'openrouter/deepseek-coder', label: 'DeepSeek Coder' },\r\n  { value: 'openrouter/gemini-pro', label: 'Gemini Pro' },\r\n  // Add more as desired\r\n];\r\n\r\nexport default function SettingsModal({ open, onClose, model, setModel }) {\r\n  if (!open) return null;\r\n  return (\r\n    <div style={{\r\n      position: 'fixed', top: 0, left: 0, width: '100vw', height: '100vh',\r\n      background: 'rgba(0,0,0,0.3)', zIndex: 1000, display: 'flex', alignItems: 'center', justifyContent: 'center'\r\n    }}>\r\n      <div style={{ background: '#fff', borderRadius: 8, padding: 32, minWidth: 350, boxShadow: '0 2px 16px #0002' }}>\r\n        <h2>Settings</h2>\r\n        <div style={{ marginBottom: 24 }}>\r\n          <label style={{ fontWeight: 'bold' }}>AI Model</label><br />\r\n          <select value={model} onChange={e => setModel(e.target.value)} style={{ width: '100%', padding: 8, marginTop: 4 }}>\r\n            {MODELS.map(m => <option key={m.value} value={m.value}>{m.label}</option>)}\r\n          </select>\r\n        </div>\r\n        <div style={{ marginBottom: 24 }}>\r\n          <label style={{ fontWeight: 'bold' }}>Other Settings</label>\r\n          <div style={{ color: '#888', fontSize: 13 }}>More settings coming soon...</div>\r\n        </div>\r\n        <button onClick={onClose} style={{ marginTop: 8, padding: '8px 24px', borderRadius: 4, border: '1px solid #ccc', background: '#f5f5f5', cursor: 'pointer' }}>Close</button>\r\n      </div>\r\n    </div>\r\n  );\r\n} "], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE1B,MAAMC,MAAM,GAAG,CACb;EAAEC,KAAK,EAAE,iBAAiB;EAAEC,KAAK,EAAE;AAAoB,CAAC,EACxD;EAAED,KAAK,EAAE,eAAe;EAAEC,KAAK,EAAE;AAAS,CAAC,EAC3C;EAAED,KAAK,EAAE,oBAAoB;EAAEC,KAAK,EAAE;AAAc,CAAC,EACrD;EAAED,KAAK,EAAE,0BAA0B;EAAEC,KAAK,EAAE;AAAgB,CAAC,EAC7D;EAAED,KAAK,EAAE,2BAA2B;EAAEC,KAAK,EAAE;AAAiB,CAAC,EAC/D;EAAED,KAAK,EAAE,uBAAuB;EAAEC,KAAK,EAAE;AAAa;AACtD;AAAA,CACD;AAED,eAAe,SAASC,aAAaA,CAAC;EAAEC,IAAI;EAAEC,OAAO;EAAEC,KAAK;EAAEC;AAAS,CAAC,EAAE;EACxE,IAAI,CAACH,IAAI,EAAE,OAAO,IAAI;EACtB,oBACEL,OAAA;IAAKS,KAAK,EAAE;MACVC,QAAQ,EAAE,OAAO;MAAEC,GAAG,EAAE,CAAC;MAAEC,IAAI,EAAE,CAAC;MAAEC,KAAK,EAAE,OAAO;MAAEC,MAAM,EAAE,OAAO;MACnEC,UAAU,EAAE,iBAAiB;MAAEC,MAAM,EAAE,IAAI;MAAEC,OAAO,EAAE,MAAM;MAAEC,UAAU,EAAE,QAAQ;MAAEC,cAAc,EAAE;IACtG,CAAE;IAAAC,QAAA,eACApB,OAAA;MAAKS,KAAK,EAAE;QAAEM,UAAU,EAAE,MAAM;QAAEM,YAAY,EAAE,CAAC;QAAEC,OAAO,EAAE,EAAE;QAAEC,QAAQ,EAAE,GAAG;QAAEC,SAAS,EAAE;MAAmB,CAAE;MAAAJ,QAAA,gBAC7GpB,OAAA;QAAAoB,QAAA,EAAI;MAAQ;QAAAK,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eACjB5B,OAAA;QAAKS,KAAK,EAAE;UAAEoB,YAAY,EAAE;QAAG,CAAE;QAAAT,QAAA,gBAC/BpB,OAAA;UAAOS,KAAK,EAAE;YAAEqB,UAAU,EAAE;UAAO,CAAE;UAAAV,QAAA,EAAC;QAAQ;UAAAK,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC,eAAA5B,OAAA;UAAAyB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,eAC5D5B,OAAA;UAAQE,KAAK,EAAEK,KAAM;UAACwB,QAAQ,EAAEC,CAAC,IAAIxB,QAAQ,CAACwB,CAAC,CAACC,MAAM,CAAC/B,KAAK,CAAE;UAACO,KAAK,EAAE;YAAEI,KAAK,EAAE,MAAM;YAAES,OAAO,EAAE,CAAC;YAAEY,SAAS,EAAE;UAAE,CAAE;UAAAd,QAAA,EAC/GnB,MAAM,CAACkC,GAAG,CAACC,CAAC,iBAAIpC,OAAA;YAAsBE,KAAK,EAAEkC,CAAC,CAAClC,KAAM;YAAAkB,QAAA,EAAEgB,CAAC,CAACjC;UAAK,GAAjCiC,CAAC,CAAClC,KAAK;YAAAuB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAmC,CAAC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACpE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC,eACN5B,OAAA;QAAKS,KAAK,EAAE;UAAEoB,YAAY,EAAE;QAAG,CAAE;QAAAT,QAAA,gBAC/BpB,OAAA;UAAOS,KAAK,EAAE;YAAEqB,UAAU,EAAE;UAAO,CAAE;UAAAV,QAAA,EAAC;QAAc;UAAAK,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC,eAC5D5B,OAAA;UAAKS,KAAK,EAAE;YAAE4B,KAAK,EAAE,MAAM;YAAEC,QAAQ,EAAE;UAAG,CAAE;UAAAlB,QAAA,EAAC;QAA4B;UAAAK,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC5E,CAAC,eACN5B,OAAA;QAAQuC,OAAO,EAAEjC,OAAQ;QAACG,KAAK,EAAE;UAAEyB,SAAS,EAAE,CAAC;UAAEZ,OAAO,EAAE,UAAU;UAAED,YAAY,EAAE,CAAC;UAAEmB,MAAM,EAAE,gBAAgB;UAAEzB,UAAU,EAAE,SAAS;UAAE0B,MAAM,EAAE;QAAU,CAAE;QAAArB,QAAA,EAAC;MAAK;QAAAK,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACxK;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV;AAACc,EAAA,GAvBuBtC,aAAa;AAAA,IAAAsC,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}