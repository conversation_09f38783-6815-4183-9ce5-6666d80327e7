{"ast": null, "code": "export function computeHiddenBlocks(lineInformation, diffLines, extraLines) {\n  let newBlockIndex = 0;\n  let currentBlock;\n  const lineBlocks = {};\n  const blocks = [];\n  lineInformation.forEach((line, lineIndex) => {\n    const isDiffLine = diffLines.some(diffLine => diffLine >= lineIndex - extraLines && diffLine <= lineIndex + extraLines);\n    if (!isDiffLine && currentBlock === undefined) {\n      // block begins\n      currentBlock = {\n        index: newBlockIndex,\n        startLine: lineIndex,\n        endLine: lineIndex,\n        lines: 1\n      };\n      blocks.push(currentBlock);\n      lineBlocks[lineIndex] = currentBlock.index;\n      newBlockIndex++;\n    } else if (!isDiffLine && currentBlock) {\n      // block continues\n      currentBlock.endLine = lineIndex;\n      currentBlock.lines++;\n      lineBlocks[lineIndex] = currentBlock.index;\n    } else {\n      // not a block anymore\n      currentBlock = undefined;\n    }\n  });\n  return {\n    lineBlocks,\n    blocks: blocks\n  };\n}", "map": {"version": 3, "names": ["computeHiddenBlocks", "lineInformation", "diffLines", "extraLines", "newBlockIndex", "currentBlock", "lineBlocks", "blocks", "for<PERSON>ach", "line", "lineIndex", "isDiffLine", "some", "diffLine", "undefined", "index", "startLine", "endLine", "lines", "push"], "sources": ["D:/Documents_2/New Coding/CoderBuddy/CoderBuddy/frontend/node_modules/react-diff-viewer-continued/lib/esm/src/compute-hidden-blocks.js"], "sourcesContent": ["export function computeHiddenBlocks(lineInformation, diffLines, extraLines) {\n    let newBlockIndex = 0;\n    let currentBlock;\n    const lineBlocks = {};\n    const blocks = [];\n    lineInformation.forEach((line, lineIndex) => {\n        const isDiffLine = diffLines.some((diffLine) => diffLine >= lineIndex - extraLines &&\n            diffLine <= lineIndex + extraLines);\n        if (!isDiffLine && currentBlock === undefined) {\n            // block begins\n            currentBlock = {\n                index: newBlockIndex,\n                startLine: lineIndex,\n                endLine: lineIndex,\n                lines: 1,\n            };\n            blocks.push(currentBlock);\n            lineBlocks[lineIndex] = currentBlock.index;\n            newBlockIndex++;\n        }\n        else if (!isDiffLine && currentBlock) {\n            // block continues\n            currentBlock.endLine = lineIndex;\n            currentBlock.lines++;\n            lineBlocks[lineIndex] = currentBlock.index;\n        }\n        else {\n            // not a block anymore\n            currentBlock = undefined;\n        }\n    });\n    return {\n        lineBlocks,\n        blocks: blocks,\n    };\n}\n"], "mappings": "AAAA,OAAO,SAASA,mBAAmBA,CAACC,eAAe,EAAEC,SAAS,EAAEC,UAAU,EAAE;EACxE,IAAIC,aAAa,GAAG,CAAC;EACrB,IAAIC,YAAY;EAChB,MAAMC,UAAU,GAAG,CAAC,CAAC;EACrB,MAAMC,MAAM,GAAG,EAAE;EACjBN,eAAe,CAACO,OAAO,CAAC,CAACC,IAAI,EAAEC,SAAS,KAAK;IACzC,MAAMC,UAAU,GAAGT,SAAS,CAACU,IAAI,CAAEC,QAAQ,IAAKA,QAAQ,IAAIH,SAAS,GAAGP,UAAU,IAC9EU,QAAQ,IAAIH,SAAS,GAAGP,UAAU,CAAC;IACvC,IAAI,CAACQ,UAAU,IAAIN,YAAY,KAAKS,SAAS,EAAE;MAC3C;MACAT,YAAY,GAAG;QACXU,KAAK,EAAEX,aAAa;QACpBY,SAAS,EAAEN,SAAS;QACpBO,OAAO,EAAEP,SAAS;QAClBQ,KAAK,EAAE;MACX,CAAC;MACDX,MAAM,CAACY,IAAI,CAACd,YAAY,CAAC;MACzBC,UAAU,CAACI,SAAS,CAAC,GAAGL,YAAY,CAACU,KAAK;MAC1CX,aAAa,EAAE;IACnB,CAAC,MACI,IAAI,CAACO,UAAU,IAAIN,YAAY,EAAE;MAClC;MACAA,YAAY,CAACY,OAAO,GAAGP,SAAS;MAChCL,YAAY,CAACa,KAAK,EAAE;MACpBZ,UAAU,CAACI,SAAS,CAAC,GAAGL,YAAY,CAACU,KAAK;IAC9C,CAAC,MACI;MACD;MACAV,YAAY,GAAGS,SAAS;IAC5B;EACJ,CAAC,CAAC;EACF,OAAO;IACHR,UAAU;IACVC,MAAM,EAAEA;EACZ,CAAC;AACL", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}