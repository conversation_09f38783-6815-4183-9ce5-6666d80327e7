{"ast": null, "code": "var _jsxFileName = \"D:\\\\Documents_2\\\\New Coding\\\\CoderBuddy\\\\CoderBuddy\\\\frontend\\\\src\\\\components\\\\TerminalPanel.js\",\n  _s = $RefreshSig$();\nimport React, { useEffect, useRef, useState } from 'react';\nimport { Terminal } from 'xterm';\nimport { runCommand } from '../services/api';\nimport 'xterm/css/xterm.css';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nexport default function TerminalPanel() {\n  _s();\n  const containerRef = useRef(null);\n  const termRef = useRef(null);\n  const [input, setInput] = useState('');\n  useEffect(() => {\n    if (!termRef.current) {\n      termRef.current = new Terminal({\n        fontSize: 14,\n        theme: {\n          background: '#1e1e1e'\n        }\n      });\n      termRef.current.open(containerRef.current);\n      termRef.current.writeln('Coding Buddy Terminal');\n      termRef.current.write('> ');\n    }\n  }, []);\n  async function handleRun() {\n    const cmd = input.trim();\n    if (!cmd) return;\n    termRef.current.writeln(cmd);\n    setInput('');\n    const ok = window.confirm(`Run command: ${cmd}`);\n    if (!ok) {\n      termRef.current.write('> ');\n      return;\n    }\n    try {\n      const {\n        stdout,\n        stderr\n      } = await runCommand(cmd);\n      if (stdout) termRef.current.writeln(stdout);\n      if (stderr) termRef.current.writeln(stderr);\n    } catch (e) {\n      termRef.current.writeln(`Error: ${e.message}`);\n    } finally {\n      termRef.current.write('> ');\n    }\n  }\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    style: {\n      display: 'flex',\n      flexDirection: 'column',\n      height: '100%'\n    },\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      ref: containerRef,\n      style: {\n        flex: 1,\n        background: '#1e1e1e'\n      }\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 43,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        display: 'flex'\n      },\n      children: [/*#__PURE__*/_jsxDEV(\"input\", {\n        style: {\n          flex: 1\n        },\n        value: input,\n        onChange: e => setInput(e.target.value),\n        onKeyDown: e => {\n          if (e.key === 'Enter') handleRun();\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 45,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n        onClick: handleRun,\n        children: \"Run\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 53,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 44,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 42,\n    columnNumber: 5\n  }, this);\n}\n_s(TerminalPanel, \"gyr3riVArz61JNuFV+FLEt/ODDQ=\");\n_c = TerminalPanel;\nvar _c;\n$RefreshReg$(_c, \"TerminalPanel\");", "map": {"version": 3, "names": ["React", "useEffect", "useRef", "useState", "Terminal", "runCommand", "jsxDEV", "_jsxDEV", "TerminalPanel", "_s", "containerRef", "termRef", "input", "setInput", "current", "fontSize", "theme", "background", "open", "writeln", "write", "handleRun", "cmd", "trim", "ok", "window", "confirm", "stdout", "stderr", "e", "message", "style", "display", "flexDirection", "height", "children", "ref", "flex", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "value", "onChange", "target", "onKeyDown", "key", "onClick", "_c", "$RefreshReg$"], "sources": ["D:/Documents_2/New Coding/CoderBuddy/CoderBuddy/frontend/src/components/TerminalPanel.js"], "sourcesContent": ["import React, { useEffect, useRef, useState } from 'react';\r\nimport { Terminal } from 'xterm';\r\nimport { runCommand } from '../services/api';\r\nimport 'xterm/css/xterm.css';\r\n\r\nexport default function TerminalPanel() {\r\n  const containerRef = useRef(null);\r\n  const termRef = useRef(null);\r\n  const [input, setInput] = useState('');\r\n\r\n  useEffect(() => {\r\n    if (!termRef.current) {\r\n      termRef.current = new Terminal({ fontSize: 14, theme: { background: '#1e1e1e' } });\r\n      termRef.current.open(containerRef.current);\r\n      termRef.current.writeln('Coding Buddy Terminal');\r\n      termRef.current.write('> ');\r\n    }\r\n  }, []);\r\n\r\n  async function handleRun() {\r\n    const cmd = input.trim();\r\n    if (!cmd) return;\r\n    termRef.current.writeln(cmd);\r\n    setInput('');\r\n    const ok = window.confirm(`Run command: ${cmd}`);\r\n    if (!ok) {\r\n      termRef.current.write('> ');\r\n      return;\r\n    }\r\n    try {\r\n      const { stdout, stderr } = await runCommand(cmd);\r\n      if (stdout) termRef.current.writeln(stdout);\r\n      if (stderr) termRef.current.writeln(stderr);\r\n    } catch (e) {\r\n      termRef.current.writeln(`Error: ${e.message}`);\r\n    } finally {\r\n      termRef.current.write('> ');\r\n    }\r\n  }\r\n\r\n  return (\r\n    <div style={{ display: 'flex', flexDirection: 'column', height: '100%' }}>\r\n      <div ref={containerRef} style={{ flex: 1, background: '#1e1e1e' }} />\r\n      <div style={{ display: 'flex' }}>\r\n        <input\r\n          style={{ flex: 1 }}\r\n          value={input}\r\n          onChange={(e) => setInput(e.target.value)}\r\n          onKeyDown={(e) => {\r\n            if (e.key === 'Enter') handleRun();\r\n          }}\r\n        />\r\n        <button onClick={handleRun}>Run</button>\r\n      </div>\r\n    </div>\r\n  );\r\n} "], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,SAAS,EAAEC,MAAM,EAAEC,QAAQ,QAAQ,OAAO;AAC1D,SAASC,QAAQ,QAAQ,OAAO;AAChC,SAASC,UAAU,QAAQ,iBAAiB;AAC5C,OAAO,qBAAqB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE7B,eAAe,SAASC,aAAaA,CAAA,EAAG;EAAAC,EAAA;EACtC,MAAMC,YAAY,GAAGR,MAAM,CAAC,IAAI,CAAC;EACjC,MAAMS,OAAO,GAAGT,MAAM,CAAC,IAAI,CAAC;EAC5B,MAAM,CAACU,KAAK,EAAEC,QAAQ,CAAC,GAAGV,QAAQ,CAAC,EAAE,CAAC;EAEtCF,SAAS,CAAC,MAAM;IACd,IAAI,CAACU,OAAO,CAACG,OAAO,EAAE;MACpBH,OAAO,CAACG,OAAO,GAAG,IAAIV,QAAQ,CAAC;QAAEW,QAAQ,EAAE,EAAE;QAAEC,KAAK,EAAE;UAAEC,UAAU,EAAE;QAAU;MAAE,CAAC,CAAC;MAClFN,OAAO,CAACG,OAAO,CAACI,IAAI,CAACR,YAAY,CAACI,OAAO,CAAC;MAC1CH,OAAO,CAACG,OAAO,CAACK,OAAO,CAAC,uBAAuB,CAAC;MAChDR,OAAO,CAACG,OAAO,CAACM,KAAK,CAAC,IAAI,CAAC;IAC7B;EACF,CAAC,EAAE,EAAE,CAAC;EAEN,eAAeC,SAASA,CAAA,EAAG;IACzB,MAAMC,GAAG,GAAGV,KAAK,CAACW,IAAI,CAAC,CAAC;IACxB,IAAI,CAACD,GAAG,EAAE;IACVX,OAAO,CAACG,OAAO,CAACK,OAAO,CAACG,GAAG,CAAC;IAC5BT,QAAQ,CAAC,EAAE,CAAC;IACZ,MAAMW,EAAE,GAAGC,MAAM,CAACC,OAAO,CAAC,gBAAgBJ,GAAG,EAAE,CAAC;IAChD,IAAI,CAACE,EAAE,EAAE;MACPb,OAAO,CAACG,OAAO,CAACM,KAAK,CAAC,IAAI,CAAC;MAC3B;IACF;IACA,IAAI;MACF,MAAM;QAAEO,MAAM;QAAEC;MAAO,CAAC,GAAG,MAAMvB,UAAU,CAACiB,GAAG,CAAC;MAChD,IAAIK,MAAM,EAAEhB,OAAO,CAACG,OAAO,CAACK,OAAO,CAACQ,MAAM,CAAC;MAC3C,IAAIC,MAAM,EAAEjB,OAAO,CAACG,OAAO,CAACK,OAAO,CAACS,MAAM,CAAC;IAC7C,CAAC,CAAC,OAAOC,CAAC,EAAE;MACVlB,OAAO,CAACG,OAAO,CAACK,OAAO,CAAC,UAAUU,CAAC,CAACC,OAAO,EAAE,CAAC;IAChD,CAAC,SAAS;MACRnB,OAAO,CAACG,OAAO,CAACM,KAAK,CAAC,IAAI,CAAC;IAC7B;EACF;EAEA,oBACEb,OAAA;IAAKwB,KAAK,EAAE;MAAEC,OAAO,EAAE,MAAM;MAAEC,aAAa,EAAE,QAAQ;MAAEC,MAAM,EAAE;IAAO,CAAE;IAAAC,QAAA,gBACvE5B,OAAA;MAAK6B,GAAG,EAAE1B,YAAa;MAACqB,KAAK,EAAE;QAAEM,IAAI,EAAE,CAAC;QAAEpB,UAAU,EAAE;MAAU;IAAE;MAAAqB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eACrElC,OAAA;MAAKwB,KAAK,EAAE;QAAEC,OAAO,EAAE;MAAO,CAAE;MAAAG,QAAA,gBAC9B5B,OAAA;QACEwB,KAAK,EAAE;UAAEM,IAAI,EAAE;QAAE,CAAE;QACnBK,KAAK,EAAE9B,KAAM;QACb+B,QAAQ,EAAGd,CAAC,IAAKhB,QAAQ,CAACgB,CAAC,CAACe,MAAM,CAACF,KAAK,CAAE;QAC1CG,SAAS,EAAGhB,CAAC,IAAK;UAChB,IAAIA,CAAC,CAACiB,GAAG,KAAK,OAAO,EAAEzB,SAAS,CAAC,CAAC;QACpC;MAAE;QAAAiB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eACFlC,OAAA;QAAQwC,OAAO,EAAE1B,SAAU;QAAAc,QAAA,EAAC;MAAG;QAAAG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACrC,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV;AAAChC,EAAA,CAnDuBD,aAAa;AAAAwC,EAAA,GAAbxC,aAAa;AAAA,IAAAwC,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}