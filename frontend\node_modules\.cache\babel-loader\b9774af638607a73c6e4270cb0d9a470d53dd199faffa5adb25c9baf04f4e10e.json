{"ast": null, "code": "var _jsxFileName = \"D:\\\\Documents_2\\\\New Coding\\\\CoderBuddy\\\\CoderBuddy\\\\frontend\\\\src\\\\App.js\",\n  _s = $RefreshSig$();\nimport React, { useState } from 'react';\nimport FileExplorer from './components/FileExplorer';\nimport EditorTabs from './components/EditorTabs';\nimport ChatPanel from './components/ChatPanel';\nimport TerminalPanel from './components/TerminalPanel';\nimport ProjectSelector from './components/ProjectSelector';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nexport default function App() {\n  _s();\n  const [selectedFile, setSelectedFile] = useState(null);\n  const [projectId, setProjectId] = useState('demo');\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    style: {\n      display: 'flex',\n      flexDirection: 'column',\n      height: '100vh'\n    },\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        padding: 8,\n        borderBottom: '1px solid #ccc',\n        display: 'flex',\n        alignItems: 'center',\n        gap: 16\n      },\n      children: /*#__PURE__*/_jsxDEV(ProjectSelector, {\n        projectId: projectId,\n        setProjectId: setProjectId\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 15,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 14,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        display: 'flex',\n        flex: 1,\n        minHeight: 0\n      },\n      children: [/*#__PURE__*/_jsxDEV(FileExplorer, {\n        onSelectFile: setSelectedFile,\n        projectId: projectId\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 18,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(EditorTabs, {\n        selectedFile: selectedFile,\n        projectId: projectId\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 19,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          width: '30%',\n          borderLeft: '1px solid #ccc',\n          display: 'flex',\n          flexDirection: 'column'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            flex: 1\n          },\n          children: /*#__PURE__*/_jsxDEV(ChatPanel, {\n            projectId: projectId\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 22,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 21,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            flex: 1\n          },\n          children: /*#__PURE__*/_jsxDEV(TerminalPanel, {\n            projectId: projectId\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 25,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 24,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 20,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 17,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 13,\n    columnNumber: 5\n  }, this);\n}\n_s(App, \"Wwa2MRZlqduTACPxPU1YTwl8UqQ=\");\n_c = App;\nvar _c;\n$RefreshReg$(_c, \"App\");", "map": {"version": 3, "names": ["React", "useState", "FileExplorer", "EditorTabs", "ChatPanel", "TerminalPanel", "ProjectSelector", "jsxDEV", "_jsxDEV", "App", "_s", "selectedFile", "setSelectedFile", "projectId", "setProjectId", "style", "display", "flexDirection", "height", "children", "padding", "borderBottom", "alignItems", "gap", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "flex", "minHeight", "onSelectFile", "width", "borderLeft", "_c", "$RefreshReg$"], "sources": ["D:/Documents_2/New Coding/CoderBuddy/CoderBuddy/frontend/src/App.js"], "sourcesContent": ["import React, { useState } from 'react';\r\nimport FileExplorer from './components/FileExplorer';\r\nimport EditorTabs from './components/EditorTabs';\r\nimport ChatPanel from './components/ChatPanel';\r\nimport TerminalPanel from './components/TerminalPanel';\r\nimport ProjectSelector from './components/ProjectSelector';\r\n\r\nexport default function App() {\r\n  const [selectedFile, setSelectedFile] = useState(null);\r\n  const [projectId, setProjectId] = useState('demo');\r\n\r\n  return (\r\n    <div style={{ display: 'flex', flexDirection: 'column', height: '100vh' }}>\r\n      <div style={{ padding: 8, borderBottom: '1px solid #ccc', display: 'flex', alignItems: 'center', gap: 16 }}>\r\n        <ProjectSelector projectId={projectId} setProjectId={setProjectId} />\r\n      </div>\r\n      <div style={{ display: 'flex', flex: 1, minHeight: 0 }}>\r\n        <FileExplorer onSelectFile={setSelectedFile} projectId={projectId} />\r\n        <EditorTabs selectedFile={selectedFile} projectId={projectId} />\r\n        <div style={{ width: '30%', borderLeft: '1px solid #ccc', display: 'flex', flexDirection: 'column' }}>\r\n          <div style={{ flex: 1 }}>\r\n            <ChatPanel projectId={projectId} />\r\n          </div>\r\n          <div style={{ flex: 1 }}>\r\n            <TerminalPanel projectId={projectId} />\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  );\r\n} "], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,OAAOC,YAAY,MAAM,2BAA2B;AACpD,OAAOC,UAAU,MAAM,yBAAyB;AAChD,OAAOC,SAAS,MAAM,wBAAwB;AAC9C,OAAOC,aAAa,MAAM,4BAA4B;AACtD,OAAOC,eAAe,MAAM,8BAA8B;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE3D,eAAe,SAASC,GAAGA,CAAA,EAAG;EAAAC,EAAA;EAC5B,MAAM,CAACC,YAAY,EAAEC,eAAe,CAAC,GAAGX,QAAQ,CAAC,IAAI,CAAC;EACtD,MAAM,CAACY,SAAS,EAAEC,YAAY,CAAC,GAAGb,QAAQ,CAAC,MAAM,CAAC;EAElD,oBACEO,OAAA;IAAKO,KAAK,EAAE;MAAEC,OAAO,EAAE,MAAM;MAAEC,aAAa,EAAE,QAAQ;MAAEC,MAAM,EAAE;IAAQ,CAAE;IAAAC,QAAA,gBACxEX,OAAA;MAAKO,KAAK,EAAE;QAAEK,OAAO,EAAE,CAAC;QAAEC,YAAY,EAAE,gBAAgB;QAAEL,OAAO,EAAE,MAAM;QAAEM,UAAU,EAAE,QAAQ;QAAEC,GAAG,EAAE;MAAG,CAAE;MAAAJ,QAAA,eACzGX,OAAA,CAACF,eAAe;QAACO,SAAS,EAAEA,SAAU;QAACC,YAAY,EAAEA;MAAa;QAAAU,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAClE,CAAC,eACNnB,OAAA;MAAKO,KAAK,EAAE;QAAEC,OAAO,EAAE,MAAM;QAAEY,IAAI,EAAE,CAAC;QAAEC,SAAS,EAAE;MAAE,CAAE;MAAAV,QAAA,gBACrDX,OAAA,CAACN,YAAY;QAAC4B,YAAY,EAAElB,eAAgB;QAACC,SAAS,EAAEA;MAAU;QAAAW,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eACrEnB,OAAA,CAACL,UAAU;QAACQ,YAAY,EAAEA,YAAa;QAACE,SAAS,EAAEA;MAAU;QAAAW,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eAChEnB,OAAA;QAAKO,KAAK,EAAE;UAAEgB,KAAK,EAAE,KAAK;UAAEC,UAAU,EAAE,gBAAgB;UAAEhB,OAAO,EAAE,MAAM;UAAEC,aAAa,EAAE;QAAS,CAAE;QAAAE,QAAA,gBACnGX,OAAA;UAAKO,KAAK,EAAE;YAAEa,IAAI,EAAE;UAAE,CAAE;UAAAT,QAAA,eACtBX,OAAA,CAACJ,SAAS;YAACS,SAAS,EAAEA;UAAU;YAAAW,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAChC,CAAC,eACNnB,OAAA;UAAKO,KAAK,EAAE;YAAEa,IAAI,EAAE;UAAE,CAAE;UAAAT,QAAA,eACtBX,OAAA,CAACH,aAAa;YAACQ,SAAS,EAAEA;UAAU;YAAAW,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACpC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV;AAACjB,EAAA,CAvBuBD,GAAG;AAAAwB,EAAA,GAAHxB,GAAG;AAAA,IAAAwB,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}