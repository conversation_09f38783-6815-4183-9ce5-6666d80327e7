{"ast": null, "code": "var _jsxFileName = \"D:\\\\Documents_2\\\\New Coding\\\\CoderBuddy\\\\CoderBuddy\\\\frontend\\\\src\\\\index.js\";\nimport React from 'react';\nimport { createRoot } from 'react-dom/client';\nimport App from './App';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst container = document.getElementById('root');\nconst root = createRoot(container);\nroot.render(/*#__PURE__*/_jsxDEV(App, {}, void 0, false, {\n  fileName: _jsxFileName,\n  lineNumber: 7,\n  columnNumber: 13\n}, this));", "map": {"version": 3, "names": ["React", "createRoot", "App", "jsxDEV", "_jsxDEV", "container", "document", "getElementById", "root", "render", "fileName", "_jsxFileName", "lineNumber", "columnNumber"], "sources": ["D:/Documents_2/New Coding/CoderBuddy/CoderBuddy/frontend/src/index.js"], "sourcesContent": ["import React from 'react';\r\nimport { createRoot } from 'react-dom/client';\r\nimport App from './App';\r\n\r\nconst container = document.getElementById('root');\r\nconst root = createRoot(container);\r\nroot.render(<App />); "], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,UAAU,QAAQ,kBAAkB;AAC7C,OAAOC,GAAG,MAAM,OAAO;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAExB,MAAMC,SAAS,GAAGC,QAAQ,CAACC,cAAc,CAAC,MAAM,CAAC;AACjD,MAAMC,IAAI,GAAGP,UAAU,CAACI,SAAS,CAAC;AAClCG,IAAI,CAACC,MAAM,cAACL,OAAA,CAACF,GAAG;EAAAQ,QAAA,EAAAC,YAAA;EAAAC,UAAA;EAAAC,YAAA;AAAA,OAAE,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}