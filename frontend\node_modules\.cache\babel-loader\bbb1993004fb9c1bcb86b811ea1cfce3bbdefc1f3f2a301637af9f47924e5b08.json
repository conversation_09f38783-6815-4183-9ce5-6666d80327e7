{"ast": null, "code": "/* *********************************************************\n * Copyright 2021 eBay Inc.\n\n * Use of this source code is governed by an MIT-style\n * license that can be found in the LICENSE file or at\n * https://opensource.org/licenses/MIT.\n*********************************************************** */\nvar __assign = this && this.__assign || function () {\n  __assign = Object.assign || function (t) {\n    for (var s, i = 1, n = arguments.length; i < n; i++) {\n      s = arguments[i];\n      for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p)) t[p] = s[p];\n    }\n    return t;\n  };\n  return __assign.apply(this, arguments);\n};\nvar __rest = this && this.__rest || function (s, e) {\n  var t = {};\n  for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0) t[p] = s[p];\n  if (s != null && typeof Object.getOwnPropertySymbols === \"function\") for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {\n    if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i])) t[p[i]] = s[p[i]];\n  }\n  return t;\n};\n/* eslint-disable @typescript-eslint/no-explicit-any */\n/**\n * @module NiceModal\n * */\nimport React, { useEffect, useCallback, useContext, useReducer, useMemo } from 'react';\nvar symModalId = Symbol('NiceModalId');\nvar initialState = {};\nexport var NiceModalContext = React.createContext(initialState);\nvar NiceModalIdContext = React.createContext(null);\nvar MODAL_REGISTRY = {};\nvar ALREADY_MOUNTED = {};\nvar uidSeed = 0;\nvar dispatch = function () {\n  throw new Error('No dispatch method detected, did you embed your app with NiceModal.Provider?');\n};\nvar getUid = function () {\n  return \"_nice_modal_\" + uidSeed++;\n};\n// Modal reducer used in useReducer hook.\nexport var reducer = function (state, action) {\n  var _a, _b, _c;\n  if (state === void 0) {\n    state = initialState;\n  }\n  switch (action.type) {\n    case 'nice-modal/show':\n      {\n        var _d = action.payload,\n          modalId = _d.modalId,\n          args = _d.args;\n        return __assign(__assign({}, state), (_a = {}, _a[modalId] = __assign(__assign({}, state[modalId]), {\n          id: modalId,\n          args: args,\n          // If modal is not mounted, mount it first then make it visible.\n          // There is logic inside HOC wrapper to make it visible after its first mount.\n          // This mechanism ensures the entering transition.\n          visible: !!ALREADY_MOUNTED[modalId],\n          delayVisible: !ALREADY_MOUNTED[modalId]\n        }), _a));\n      }\n    case 'nice-modal/hide':\n      {\n        var modalId = action.payload.modalId;\n        if (!state[modalId]) return state;\n        return __assign(__assign({}, state), (_b = {}, _b[modalId] = __assign(__assign({}, state[modalId]), {\n          visible: false\n        }), _b));\n      }\n    case 'nice-modal/remove':\n      {\n        var modalId = action.payload.modalId;\n        var newState = __assign({}, state);\n        delete newState[modalId];\n        return newState;\n      }\n    case 'nice-modal/set-flags':\n      {\n        var _e = action.payload,\n          modalId = _e.modalId,\n          flags = _e.flags;\n        return __assign(__assign({}, state), (_c = {}, _c[modalId] = __assign(__assign({}, state[modalId]), flags), _c));\n      }\n    default:\n      return state;\n  }\n};\n// Get modal component by modal id\nfunction getModal(modalId) {\n  var _a;\n  return (_a = MODAL_REGISTRY[modalId]) === null || _a === void 0 ? void 0 : _a.comp;\n}\n// action creator to show a modal\nfunction showModal(modalId, args) {\n  return {\n    type: 'nice-modal/show',\n    payload: {\n      modalId: modalId,\n      args: args\n    }\n  };\n}\n// action creator to set flags of a modal\nfunction setModalFlags(modalId, flags) {\n  return {\n    type: 'nice-modal/set-flags',\n    payload: {\n      modalId: modalId,\n      flags: flags\n    }\n  };\n}\n// action creator to hide a modal\nfunction hideModal(modalId) {\n  return {\n    type: 'nice-modal/hide',\n    payload: {\n      modalId: modalId\n    }\n  };\n}\n// action creator to remove a modal\nfunction removeModal(modalId) {\n  return {\n    type: 'nice-modal/remove',\n    payload: {\n      modalId: modalId\n    }\n  };\n}\nvar modalCallbacks = {};\nvar hideModalCallbacks = {};\nvar getModalId = function (modal) {\n  if (typeof modal === 'string') return modal;\n  if (!modal[symModalId]) {\n    modal[symModalId] = getUid();\n  }\n  return modal[symModalId];\n};\n// eslint-disable-next-line @typescript-eslint/explicit-module-boundary-types\nexport function show(modal, args) {\n  var modalId = getModalId(modal);\n  if (typeof modal !== 'string' && !MODAL_REGISTRY[modalId]) {\n    register(modalId, modal);\n  }\n  dispatch(showModal(modalId, args));\n  if (!modalCallbacks[modalId]) {\n    // `!` tell ts that theResolve will be written before it is used\n    var theResolve_1;\n    // `!` tell ts that theResolve will be written before it is used\n    var theReject_1;\n    var promise = new Promise(function (resolve, reject) {\n      theResolve_1 = resolve;\n      theReject_1 = reject;\n    });\n    modalCallbacks[modalId] = {\n      resolve: theResolve_1,\n      reject: theReject_1,\n      promise: promise\n    };\n  }\n  return modalCallbacks[modalId].promise;\n}\n// eslint-disable-next-line @typescript-eslint/explicit-module-boundary-types\nexport function hide(modal) {\n  var modalId = getModalId(modal);\n  dispatch(hideModal(modalId));\n  // Should also delete the callback for modal.resolve #35\n  delete modalCallbacks[modalId];\n  if (!hideModalCallbacks[modalId]) {\n    // `!` tell ts that theResolve will be written before it is used\n    var theResolve_2;\n    // `!` tell ts that theResolve will be written before it is used\n    var theReject_2;\n    var promise = new Promise(function (resolve, reject) {\n      theResolve_2 = resolve;\n      theReject_2 = reject;\n    });\n    hideModalCallbacks[modalId] = {\n      resolve: theResolve_2,\n      reject: theReject_2,\n      promise: promise\n    };\n  }\n  return hideModalCallbacks[modalId].promise;\n}\nexport var remove = function (modal) {\n  var modalId = getModalId(modal);\n  dispatch(removeModal(modalId));\n  delete modalCallbacks[modalId];\n  delete hideModalCallbacks[modalId];\n};\nvar setFlags = function (modalId, flags) {\n  dispatch(setModalFlags(modalId, flags));\n};\n// eslint-disable-next-line @typescript-eslint/explicit-module-boundary-types\nexport function useModal(modal, args) {\n  var modals = useContext(NiceModalContext);\n  var contextModalId = useContext(NiceModalIdContext);\n  var modalId = null;\n  var isUseComponent = modal && typeof modal !== 'string';\n  if (!modal) {\n    modalId = contextModalId;\n  } else {\n    modalId = getModalId(modal);\n  }\n  // Only if contextModalId doesn't exist\n  if (!modalId) throw new Error('No modal id found in NiceModal.useModal.');\n  var mid = modalId;\n  // If use a component directly, register it.\n  useEffect(function () {\n    if (isUseComponent && !MODAL_REGISTRY[mid]) {\n      register(mid, modal, args);\n    }\n  }, [isUseComponent, mid, modal, args]);\n  var modalInfo = modals[mid];\n  var showCallback = useCallback(function (args) {\n    return show(mid, args);\n  }, [mid]);\n  var hideCallback = useCallback(function () {\n    return hide(mid);\n  }, [mid]);\n  var removeCallback = useCallback(function () {\n    return remove(mid);\n  }, [mid]);\n  var resolveCallback = useCallback(function (args) {\n    var _a;\n    (_a = modalCallbacks[mid]) === null || _a === void 0 ? void 0 : _a.resolve(args);\n    delete modalCallbacks[mid];\n  }, [mid]);\n  var rejectCallback = useCallback(function (args) {\n    var _a;\n    (_a = modalCallbacks[mid]) === null || _a === void 0 ? void 0 : _a.reject(args);\n    delete modalCallbacks[mid];\n  }, [mid]);\n  var resolveHide = useCallback(function (args) {\n    var _a;\n    (_a = hideModalCallbacks[mid]) === null || _a === void 0 ? void 0 : _a.resolve(args);\n    delete hideModalCallbacks[mid];\n  }, [mid]);\n  return useMemo(function () {\n    return {\n      id: mid,\n      args: modalInfo === null || modalInfo === void 0 ? void 0 : modalInfo.args,\n      visible: !!(modalInfo === null || modalInfo === void 0 ? void 0 : modalInfo.visible),\n      keepMounted: !!(modalInfo === null || modalInfo === void 0 ? void 0 : modalInfo.keepMounted),\n      show: showCallback,\n      hide: hideCallback,\n      remove: removeCallback,\n      resolve: resolveCallback,\n      reject: rejectCallback,\n      resolveHide: resolveHide\n    };\n  }, [mid, modalInfo === null || modalInfo === void 0 ? void 0 : modalInfo.args, modalInfo === null || modalInfo === void 0 ? void 0 : modalInfo.visible, modalInfo === null || modalInfo === void 0 ? void 0 : modalInfo.keepMounted, showCallback, hideCallback, removeCallback, resolveCallback, rejectCallback, resolveHide]);\n}\nexport var create = function (Comp) {\n  return function (_a) {\n    var _b;\n    var defaultVisible = _a.defaultVisible,\n      keepMounted = _a.keepMounted,\n      id = _a.id,\n      props = __rest(_a, [\"defaultVisible\", \"keepMounted\", \"id\"]);\n    var _c = useModal(id),\n      args = _c.args,\n      show = _c.show;\n    // If there's modal state, then should mount it.\n    var modals = useContext(NiceModalContext);\n    var shouldMount = !!modals[id];\n    useEffect(function () {\n      // If defaultVisible, show it after mounted.\n      if (defaultVisible) {\n        show();\n      }\n      ALREADY_MOUNTED[id] = true;\n      return function () {\n        delete ALREADY_MOUNTED[id];\n      };\n    }, [id, show, defaultVisible]);\n    useEffect(function () {\n      if (keepMounted) setFlags(id, {\n        keepMounted: true\n      });\n    }, [id, keepMounted]);\n    var delayVisible = (_b = modals[id]) === null || _b === void 0 ? void 0 : _b.delayVisible;\n    // If modal.show is called\n    //  1. If modal was mounted, should make it visible directly\n    //  2. If modal has not been mounted, should mount it first, then make it visible\n    useEffect(function () {\n      if (delayVisible) {\n        // delayVisible: false => true, it means the modal.show() is called, should show it.\n        show(args);\n      }\n    }, [delayVisible, args, show]);\n    if (!shouldMount) return null;\n    return React.createElement(NiceModalIdContext.Provider, {\n      value: id\n    }, React.createElement(Comp, __assign({}, props, args)));\n  };\n};\n// All registered modals will be rendered in modal placeholder\nexport var register = function (id, comp, props) {\n  if (!MODAL_REGISTRY[id]) {\n    MODAL_REGISTRY[id] = {\n      comp: comp,\n      props: props\n    };\n  } else {\n    MODAL_REGISTRY[id].props = props;\n  }\n};\n/**\n * Unregister a modal.\n * @param id - The id of the modal.\n */\nexport var unregister = function (id) {\n  delete MODAL_REGISTRY[id];\n};\n// The placeholder component is used to auto render modals when call modal.show()\n// When modal.show() is called, it means there've been modal info\nvar NiceModalPlaceholder = function () {\n  var modals = useContext(NiceModalContext);\n  var visibleModalIds = Object.keys(modals).filter(function (id) {\n    return !!modals[id];\n  });\n  visibleModalIds.forEach(function (id) {\n    if (!MODAL_REGISTRY[id] && !ALREADY_MOUNTED[id]) {\n      console.warn(\"No modal found for id: \" + id + \". Please check the id or if it is registered or declared via JSX.\");\n      return;\n    }\n  });\n  var toRender = visibleModalIds.filter(function (id) {\n    return MODAL_REGISTRY[id];\n  }).map(function (id) {\n    return __assign({\n      id: id\n    }, MODAL_REGISTRY[id]);\n  });\n  return React.createElement(React.Fragment, null, toRender.map(function (t) {\n    return React.createElement(t.comp, __assign({\n      key: t.id,\n      id: t.id\n    }, t.props));\n  }));\n};\nvar InnerContextProvider = function (_a) {\n  var children = _a.children;\n  var arr = useReducer(reducer, initialState);\n  var modals = arr[0];\n  dispatch = arr[1];\n  return React.createElement(NiceModalContext.Provider, {\n    value: modals\n  }, children, React.createElement(NiceModalPlaceholder, null));\n};\nexport var Provider = function (_a) {\n  var children = _a.children,\n    givenDispatch = _a.dispatch,\n    givenModals = _a.modals;\n  if (!givenDispatch || !givenModals) {\n    return React.createElement(InnerContextProvider, null, children);\n  }\n  dispatch = givenDispatch;\n  return React.createElement(NiceModalContext.Provider, {\n    value: givenModals\n  }, children, React.createElement(NiceModalPlaceholder, null));\n};\n/**\n * Declarative way to register a modal.\n * @param id - The id of the modal.\n * @param component - The modal Component.\n * @returns\n */\nexport var ModalDef = function (_a) {\n  var id = _a.id,\n    component = _a.component;\n  useEffect(function () {\n    register(id, component);\n    return function () {\n      unregister(id);\n    };\n  }, [id, component]);\n  return null;\n};\n/**\n * A place holder allows to bind props to a modal.\n * It assigns show/hide methods to handler object to show/hide the modal.\n *\n * Comparing to use the <MyNiceModal id=../> directly, this approach allows use registered modal id to find the modal component.\n * Also it avoids to create unique id for MyNiceModal.\n *\n * @param modal - The modal id registered or a modal component.\n * @param handler - The handler object to control the modal.\n * @returns\n */\nexport var ModalHolder = function (_a) {\n  var _b;\n  var modal = _a.modal,\n    _c = _a.handler,\n    handler = _c === void 0 ? {} : _c,\n    restProps = __rest(_a, [\"modal\", \"handler\"]);\n  var mid = useMemo(function () {\n    return getUid();\n  }, []);\n  var ModalComp = typeof modal === 'string' ? (_b = MODAL_REGISTRY[modal]) === null || _b === void 0 ? void 0 : _b.comp : modal;\n  if (!handler) {\n    throw new Error('No handler found in NiceModal.ModalHolder.');\n  }\n  if (!ModalComp) {\n    throw new Error(\"No modal found for id: \" + modal + \" in NiceModal.ModalHolder.\");\n  }\n  handler.show = useCallback(function (args) {\n    return show(mid, args);\n  }, [mid]);\n  handler.hide = useCallback(function () {\n    return hide(mid);\n  }, [mid]);\n  return React.createElement(ModalComp, __assign({\n    id: mid\n  }, restProps));\n};\nexport var antdModal = function (modal) {\n  return {\n    visible: modal.visible,\n    onOk: function () {\n      return modal.hide();\n    },\n    onCancel: function () {\n      return modal.hide();\n    },\n    afterClose: function () {\n      // Need to resolve before remove\n      modal.resolveHide();\n      if (!modal.keepMounted) modal.remove();\n    }\n  };\n};\nexport var antdModalV5 = function (modal) {\n  var _a = antdModal(modal),\n    onOk = _a.onOk,\n    onCancel = _a.onCancel,\n    afterClose = _a.afterClose;\n  return {\n    open: modal.visible,\n    onOk: onOk,\n    onCancel: onCancel,\n    afterClose: afterClose\n  };\n};\nexport var antdDrawer = function (modal) {\n  return {\n    visible: modal.visible,\n    onClose: function () {\n      return modal.hide();\n    },\n    afterVisibleChange: function (v) {\n      if (!v) {\n        modal.resolveHide();\n      }\n      !v && !modal.keepMounted && modal.remove();\n    }\n  };\n};\nexport var antdDrawerV5 = function (modal) {\n  var _a = antdDrawer(modal),\n    onClose = _a.onClose,\n    afterOpenChange = _a.afterVisibleChange;\n  return {\n    open: modal.visible,\n    onClose: onClose,\n    afterOpenChange: afterOpenChange\n  };\n};\nexport var muiDialog = function (modal) {\n  return {\n    open: modal.visible,\n    onClose: function () {\n      return modal.hide();\n    },\n    onExited: function () {\n      modal.resolveHide();\n      !modal.keepMounted && modal.remove();\n    }\n  };\n};\nexport var muiDialogV5 = function (modal) {\n  return {\n    open: modal.visible,\n    onClose: function () {\n      return modal.hide();\n    },\n    TransitionProps: {\n      onExited: function () {\n        modal.resolveHide();\n        !modal.keepMounted && modal.remove();\n      }\n    }\n  };\n};\nexport var bootstrapDialog = function (modal) {\n  return {\n    show: modal.visible,\n    onHide: function () {\n      return modal.hide();\n    },\n    onExited: function () {\n      modal.resolveHide();\n      !modal.keepMounted && modal.remove();\n    }\n  };\n};\nvar NiceModal = {\n  Provider: Provider,\n  ModalDef: ModalDef,\n  ModalHolder: ModalHolder,\n  NiceModalContext: NiceModalContext,\n  create: create,\n  register: register,\n  getModal: getModal,\n  show: show,\n  hide: hide,\n  remove: remove,\n  useModal: useModal,\n  reducer: reducer,\n  antdModal: antdModal,\n  antdDrawer: antdDrawer,\n  muiDialog: muiDialog,\n  bootstrapDialog: bootstrapDialog\n};\nexport default NiceModal;", "map": {"version": 3, "names": ["React", "useEffect", "useCallback", "useContext", "useReducer", "useMemo", "symModalId", "Symbol", "initialState", "NiceModalContext", "createContext", "NiceModalIdContext", "MODAL_REGISTRY", "ALREADY_MOUNTED", "uidSeed", "dispatch", "Error", "getUid", "reducer", "state", "action", "type", "_d", "payload", "modalId", "args", "__assign", "_a", "id", "visible", "delayVisible", "_b", "newState", "_e", "flags", "_c", "getModal", "comp", "showModal", "setModalFlags", "hideModal", "removeModal", "modalCallbacks", "hideModalCallbacks", "getModalId", "modal", "show", "register", "theResolve_1", "theReject_1", "promise", "Promise", "resolve", "reject", "hide", "theResolve_2", "theReject_2", "remove", "setFlags", "useModal", "modals", "contextModalId", "isUseComponent", "mid", "modalInfo", "showCallback", "hide<PERSON>allback", "removeCallback", "<PERSON><PERSON><PERSON>back", "<PERSON><PERSON><PERSON><PERSON>", "resolveHide", "keepMounted", "create", "Comp", "defaultVisible", "props", "__rest", "shouldMount", "createElement", "Provider", "value", "unregister", "NiceModalPlaceholder", "visibleModalIds", "Object", "keys", "filter", "for<PERSON>ach", "console", "warn", "to<PERSON><PERSON>", "map", "Fragment", "t", "key", "InnerContextProvider", "children", "arr", "givenDispatch", "givenModals", "ModalDef", "component", "ModalHolder", "handler", "restProps", "ModalComp", "antdModal", "onOk", "onCancel", "afterClose", "antdModalV5", "open", "ant<PERSON><PERSON><PERSON><PERSON>", "onClose", "afterVisibleChange", "v", "antdDrawerV5", "afterOpenChange", "mui<PERSON><PERSON><PERSON>", "onExited", "muiDialogV5", "TransitionProps", "bootstrapDialog", "onHide", "NiceModal"], "sources": ["D:\\Documents_2\\New Coding\\CoderBuddy\\CoderBuddy\\frontend\\node_modules\\@ebay\\nice-modal-react\\src\\index.tsx"], "sourcesContent": ["/* *********************************************************\n * Copyright 2021 eBay Inc.\n\n * Use of this source code is governed by an MIT-style\n * license that can be found in the LICENSE file or at\n * https://opensource.org/licenses/MIT.\n*********************************************************** */\n\n/* eslint-disable @typescript-eslint/no-explicit-any */\n/**\n * @module NiceModal\n * */\n\nimport React, { useEffect, useCallback, useContext, useReducer, useMemo, ReactNode } from 'react';\n\nexport interface NiceModalState {\n  id: string;\n  args?: Record<string, unknown>;\n  visible?: boolean;\n  delayVisible?: boolean;\n  keepMounted?: boolean;\n}\n\nexport interface NiceModalStore {\n  [key: string]: NiceModalState;\n}\n\nexport interface NiceModalAction {\n  type: string;\n  payload: {\n    modalId: string;\n    args?: Record<string, unknown>;\n    flags?: Record<string, unknown>;\n  };\n}\ninterface NiceModalCallbacks {\n  [modalId: string]: {\n    resolve: (args: unknown) => void;\n    reject: (args: unknown) => void;\n    promise: Promise<unknown>;\n  };\n}\n\n/**\n * The handler to manage a modal returned by {@link useModal | useModal} hook.\n */\nexport interface NiceModalHandler<Props = Record<string, unknown>> extends NiceModalState {\n  /**\n   * Whether a modal is visible, it's controlled by {@link NiceModalHandler.show | show}/{@link NiceModalHandler.hide | hide} method.\n   */\n  visible: boolean;\n  /**\n   * If you don't want to remove the modal from the tree after hide when using helpers, set it to true.\n   */\n  keepMounted: boolean;\n  /**\n   * Show the modal, it will change {@link NiceModalHandler.visible | visible} state to true.\n   * @param args - an object passed to modal component as props.\n   */\n  show: (args?: Props) => Promise<unknown>;\n  /**\n   * Hide the modal, it will change {@link NiceModalHandler.visible | visible} state to false.\n   */\n  hide: () => Promise<unknown>;\n  /**\n   * Resolve the promise returned by {@link NiceModalHandler.show | show} method.\n   */\n  resolve: (args?: unknown) => void;\n  /**\n   * Reject the promise returned by {@link NiceModalHandler.show | show} method.\n   */\n  reject: (args?: unknown) => void;\n  /**\n   * Remove the modal component from React component tree. It improves performance compared to just making a modal invisible.\n   */\n  remove: () => void;\n\n  /**\n   * Resolve the promise returned by {@link NiceModalHandler.hide | hide} method.\n   */\n  resolveHide: (args?: unknown) => void;\n}\n\n// Omit will not work if extends Record<string, unknown>, which is not needed here\nexport interface NiceModalHocProps {\n  id: string;\n  defaultVisible?: boolean;\n  keepMounted?: boolean;\n}\nconst symModalId = Symbol('NiceModalId');\nconst initialState: NiceModalStore = {};\nexport const NiceModalContext = React.createContext<NiceModalStore>(initialState);\nconst NiceModalIdContext = React.createContext<string | null>(null);\nconst MODAL_REGISTRY: {\n  [id: string]: {\n    comp: React.FC<any>;\n    props?: Record<string, unknown>;\n  };\n} = {};\nconst ALREADY_MOUNTED = {};\n\nlet uidSeed = 0;\nlet dispatch: React.Dispatch<NiceModalAction> = () => {\n  throw new Error('No dispatch method detected, did you embed your app with NiceModal.Provider?');\n};\nconst getUid = () => `_nice_modal_${uidSeed++}`;\n\n// Modal reducer used in useReducer hook.\nexport const reducer = (\n  state: NiceModalStore = initialState,\n  action: NiceModalAction,\n): NiceModalStore => {\n  switch (action.type) {\n    case 'nice-modal/show': {\n      const { modalId, args } = action.payload;\n      return {\n        ...state,\n        [modalId]: {\n          ...state[modalId],\n          id: modalId,\n          args,\n          // If modal is not mounted, mount it first then make it visible.\n          // There is logic inside HOC wrapper to make it visible after its first mount.\n          // This mechanism ensures the entering transition.\n          visible: !!ALREADY_MOUNTED[modalId],\n          delayVisible: !ALREADY_MOUNTED[modalId],\n        },\n      };\n    }\n    case 'nice-modal/hide': {\n      const { modalId } = action.payload;\n      if (!state[modalId]) return state;\n      return {\n        ...state,\n        [modalId]: {\n          ...state[modalId],\n          visible: false,\n        },\n      };\n    }\n    case 'nice-modal/remove': {\n      const { modalId } = action.payload;\n      const newState = { ...state };\n      delete newState[modalId];\n      return newState;\n    }\n    case 'nice-modal/set-flags': {\n      const { modalId, flags } = action.payload;\n      return {\n        ...state,\n        [modalId]: {\n          ...state[modalId],\n          ...flags,\n        },\n      };\n    }\n    default:\n      return state;\n  }\n};\n\n// Get modal component by modal id\nfunction getModal(modalId: string): React.FC<any> | undefined {\n  return MODAL_REGISTRY[modalId]?.comp;\n}\n\n// action creator to show a modal\nfunction showModal(modalId: string, args?: Record<string, unknown>): NiceModalAction {\n  return {\n    type: 'nice-modal/show',\n    payload: {\n      modalId,\n      args,\n    },\n  };\n}\n\n// action creator to set flags of a modal\nfunction setModalFlags(modalId: string, flags: Record<string, unknown>): NiceModalAction {\n  return {\n    type: 'nice-modal/set-flags',\n    payload: {\n      modalId,\n      flags,\n    },\n  };\n}\n// action creator to hide a modal\nfunction hideModal(modalId: string): NiceModalAction {\n  return {\n    type: 'nice-modal/hide',\n    payload: {\n      modalId,\n    },\n  };\n}\n\n// action creator to remove a modal\nfunction removeModal(modalId: string): NiceModalAction {\n  return {\n    type: 'nice-modal/remove',\n    payload: {\n      modalId,\n    },\n  };\n}\n\nconst modalCallbacks: NiceModalCallbacks = {};\nconst hideModalCallbacks: NiceModalCallbacks = {};\nconst getModalId = (modal: string | React.FC<any>): string => {\n  if (typeof modal === 'string') return modal as string;\n  if (!modal[symModalId]) {\n    modal[symModalId] = getUid();\n  }\n  return modal[symModalId];\n};\n\ntype NiceModalArgs<T> = T extends keyof JSX.IntrinsicElements | React.JSXElementConstructor<any>\n  ? React.ComponentProps<T>\n  : Record<string, unknown>;\n\nexport function show<T extends any, C extends any, P extends Partial<NiceModalArgs<React.FC<C>>>>(\n  modal: React.FC<C>,\n  args?: P,\n): Promise<T>;\n\nexport function show<T extends any>(modal: string, args?: Record<string, unknown>): Promise<T>;\nexport function show<T extends any, P extends any>(modal: string, args: P): Promise<T>;\n\n// eslint-disable-next-line @typescript-eslint/explicit-module-boundary-types\nexport function show(\n  modal: React.FC<any> | string,\n  args?: NiceModalArgs<React.FC<any>> | Record<string, unknown>,\n) {\n  const modalId = getModalId(modal);\n  if (typeof modal !== 'string' && !MODAL_REGISTRY[modalId]) {\n    register(modalId, modal as React.FC);\n  }\n\n  dispatch(showModal(modalId, args));\n  if (!modalCallbacks[modalId]) {\n    // `!` tell ts that theResolve will be written before it is used\n    let theResolve!: (args?: unknown) => void;\n    // `!` tell ts that theResolve will be written before it is used\n    let theReject!: (args?: unknown) => void;\n    const promise = new Promise((resolve, reject) => {\n      theResolve = resolve;\n      theReject = reject;\n    });\n    modalCallbacks[modalId] = {\n      resolve: theResolve,\n      reject: theReject,\n      promise,\n    };\n  }\n  return modalCallbacks[modalId].promise;\n}\n\nexport function hide<T>(modal: string | React.FC<any>): Promise<T>;\n// eslint-disable-next-line @typescript-eslint/explicit-module-boundary-types\nexport function hide(modal: string | React.FC<any>) {\n  const modalId = getModalId(modal);\n  dispatch(hideModal(modalId));\n  // Should also delete the callback for modal.resolve #35\n  delete modalCallbacks[modalId];\n  if (!hideModalCallbacks[modalId]) {\n    // `!` tell ts that theResolve will be written before it is used\n    let theResolve!: (args?: unknown) => void;\n    // `!` tell ts that theResolve will be written before it is used\n    let theReject!: (args?: unknown) => void;\n    const promise = new Promise((resolve, reject) => {\n      theResolve = resolve;\n      theReject = reject;\n    });\n    hideModalCallbacks[modalId] = {\n      resolve: theResolve,\n      reject: theReject,\n      promise,\n    };\n  }\n  return hideModalCallbacks[modalId].promise;\n}\n\nexport const remove = (modal: string | React.FC<any>): void => {\n  const modalId = getModalId(modal);\n  dispatch(removeModal(modalId));\n  delete modalCallbacks[modalId];\n  delete hideModalCallbacks[modalId];\n};\n\nconst setFlags = (modalId: string, flags: Record<string, unknown>): void => {\n  dispatch(setModalFlags(modalId, flags));\n};\nexport function useModal(): NiceModalHandler;\nexport function useModal(modal: string, args?: Record<string, unknown>): NiceModalHandler;\nexport function useModal<C extends any, P extends Partial<NiceModalArgs<React.FC<C>>>>(\n  modal: React.FC<C>,\n  args?: P,\n): Omit<NiceModalHandler, 'show'> & {\n  show: (args?: P) => Promise<unknown>;\n};\n\n// eslint-disable-next-line @typescript-eslint/explicit-module-boundary-types\nexport function useModal(modal?: any, args?: any): any {\n  const modals = useContext(NiceModalContext);\n  const contextModalId = useContext(NiceModalIdContext);\n  let modalId: string | null = null;\n  const isUseComponent = modal && typeof modal !== 'string';\n  if (!modal) {\n    modalId = contextModalId;\n  } else {\n    modalId = getModalId(modal);\n  }\n\n  // Only if contextModalId doesn't exist\n  if (!modalId) throw new Error('No modal id found in NiceModal.useModal.');\n\n  const mid = modalId as string;\n  // If use a component directly, register it.\n  useEffect(() => {\n    if (isUseComponent && !MODAL_REGISTRY[mid]) {\n      register(mid, modal as React.FC, args);\n    }\n  }, [isUseComponent, mid, modal, args]);\n\n  const modalInfo = modals[mid];\n\n  const showCallback = useCallback((args?: Record<string, unknown>) => show(mid, args), [mid]);\n  const hideCallback = useCallback(() => hide(mid), [mid]);\n  const removeCallback = useCallback(() => remove(mid), [mid]);\n  const resolveCallback = useCallback(\n    (args?: unknown) => {\n      modalCallbacks[mid]?.resolve(args);\n      delete modalCallbacks[mid];\n    },\n    [mid],\n  );\n  const rejectCallback = useCallback(\n    (args?: unknown) => {\n      modalCallbacks[mid]?.reject(args);\n      delete modalCallbacks[mid];\n    },\n    [mid],\n  );\n  const resolveHide = useCallback(\n    (args?: unknown) => {\n      hideModalCallbacks[mid]?.resolve(args);\n      delete hideModalCallbacks[mid];\n    },\n    [mid],\n  );\n\n  return useMemo(\n    () => ({\n      id: mid,\n      args: modalInfo?.args,\n      visible: !!modalInfo?.visible,\n      keepMounted: !!modalInfo?.keepMounted,\n      show: showCallback,\n      hide: hideCallback,\n      remove: removeCallback,\n      resolve: resolveCallback,\n      reject: rejectCallback,\n      resolveHide,\n    }),\n    [\n      mid,\n      modalInfo?.args,\n      modalInfo?.visible,\n      modalInfo?.keepMounted,\n      showCallback,\n      hideCallback,\n      removeCallback,\n      resolveCallback,\n      rejectCallback,\n      resolveHide,\n    ],\n  );\n}\nexport const create = <P extends {}>(\n  Comp: React.ComponentType<P>,\n): React.FC<P & NiceModalHocProps> => {\n  return ({ defaultVisible, keepMounted, id, ...props }) => {\n    const { args, show } = useModal(id);\n\n    // If there's modal state, then should mount it.\n    const modals = useContext(NiceModalContext);\n    const shouldMount = !!modals[id];\n\n    useEffect(() => {\n      // If defaultVisible, show it after mounted.\n      if (defaultVisible) {\n        show();\n      }\n\n      ALREADY_MOUNTED[id] = true;\n\n      return () => {\n        delete ALREADY_MOUNTED[id];\n      };\n    }, [id, show, defaultVisible]);\n\n    useEffect(() => {\n      if (keepMounted) setFlags(id, { keepMounted: true });\n    }, [id, keepMounted]);\n\n    const delayVisible = modals[id]?.delayVisible;\n    // If modal.show is called\n    //  1. If modal was mounted, should make it visible directly\n    //  2. If modal has not been mounted, should mount it first, then make it visible\n    useEffect(() => {\n      if (delayVisible) {\n        // delayVisible: false => true, it means the modal.show() is called, should show it.\n        show(args);\n      }\n    }, [delayVisible, args, show]);\n\n    if (!shouldMount) return null;\n    return (\n      <NiceModalIdContext.Provider value={id}>\n        <Comp {...(props as P)} {...args} />\n      </NiceModalIdContext.Provider>\n    );\n  };\n};\n\n// All registered modals will be rendered in modal placeholder\nexport const register = <T extends React.FC<any>>(\n  id: string,\n  comp: T,\n  props?: Partial<NiceModalArgs<T>>,\n): void => {\n  if (!MODAL_REGISTRY[id]) {\n    MODAL_REGISTRY[id] = { comp, props };\n  } else {\n    MODAL_REGISTRY[id].props = props;\n  }\n};\n\n/**\n * Unregister a modal.\n * @param id - The id of the modal.\n */\nexport const unregister = (id: string): void => {\n  delete MODAL_REGISTRY[id];\n};\n\n// The placeholder component is used to auto render modals when call modal.show()\n// When modal.show() is called, it means there've been modal info\nconst NiceModalPlaceholder: React.FC = () => {\n  const modals = useContext(NiceModalContext);\n  const visibleModalIds = Object.keys(modals).filter((id) => !!modals[id]);\n  visibleModalIds.forEach((id) => {\n    if (!MODAL_REGISTRY[id] && !ALREADY_MOUNTED[id]) {\n      console.warn(\n        `No modal found for id: ${id}. Please check the id or if it is registered or declared via JSX.`,\n      );\n      return;\n    }\n  });\n\n  const toRender = visibleModalIds\n    .filter((id) => MODAL_REGISTRY[id])\n    .map((id) => ({\n      id,\n      ...MODAL_REGISTRY[id],\n    }));\n\n  return (\n    <>\n      {toRender.map((t) => (\n        <t.comp key={t.id} id={t.id} {...t.props} />\n      ))}\n    </>\n  );\n};\n\nconst InnerContextProvider: React.FC = ({ children }) => {\n  const arr = useReducer(reducer, initialState);\n  const modals = arr[0];\n  dispatch = arr[1];\n  return (\n    <NiceModalContext.Provider value={modals}>\n      {children}\n      <NiceModalPlaceholder />\n    </NiceModalContext.Provider>\n  );\n};\n\nexport const Provider: React.FC<Record<string, unknown>> = ({\n  children,\n  dispatch: givenDispatch,\n  modals: givenModals,\n}: {\n  children: ReactNode;\n  dispatch?: React.Dispatch<NiceModalAction>;\n  modals?: NiceModalStore;\n}) => {\n  if (!givenDispatch || !givenModals) {\n    return <InnerContextProvider>{children}</InnerContextProvider>;\n  }\n  dispatch = givenDispatch;\n  return (\n    <NiceModalContext.Provider value={givenModals}>\n      {children}\n      <NiceModalPlaceholder />\n    </NiceModalContext.Provider>\n  );\n};\n\n/**\n * Declarative way to register a modal.\n * @param id - The id of the modal.\n * @param component - The modal Component.\n * @returns\n */\nexport const ModalDef: React.FC<Record<string, unknown>> = ({\n  id,\n  component,\n}: {\n  id: string;\n  component: React.FC<any>;\n}) => {\n  useEffect(() => {\n    register(id, component);\n    return () => {\n      unregister(id);\n    };\n  }, [id, component]);\n  return null;\n};\n\n/**\n * A place holder allows to bind props to a modal.\n * It assigns show/hide methods to handler object to show/hide the modal.\n *\n * Comparing to use the <MyNiceModal id=../> directly, this approach allows use registered modal id to find the modal component.\n * Also it avoids to create unique id for MyNiceModal.\n *\n * @param modal - The modal id registered or a modal component.\n * @param handler - The handler object to control the modal.\n * @returns\n */\nexport const ModalHolder: React.FC<Record<string, unknown>> = ({\n  modal,\n  handler = {},\n  ...restProps\n}: {\n  modal: string | React.FC<any>;\n  handler: any;\n  [key: string]: any;\n}) => {\n  const mid = useMemo(() => getUid(), []);\n  const ModalComp = typeof modal === 'string' ? MODAL_REGISTRY[modal]?.comp : modal;\n\n  if (!handler) {\n    throw new Error('No handler found in NiceModal.ModalHolder.');\n  }\n  if (!ModalComp) {\n    throw new Error(`No modal found for id: ${modal} in NiceModal.ModalHolder.`);\n  }\n  handler.show = useCallback((args: any) => show(mid, args), [mid]);\n  handler.hide = useCallback(() => hide(mid), [mid]);\n\n  return <ModalComp id={mid} {...restProps} />;\n};\n\nexport const antdModal = (\n  modal: NiceModalHandler,\n): { visible: boolean; onCancel: () => void; onOk: () => void; afterClose: () => void } => {\n  return {\n    visible: modal.visible,\n    onOk: () => modal.hide(),\n    onCancel: () => modal.hide(),\n    afterClose: () => {\n      // Need to resolve before remove\n      modal.resolveHide();\n      if (!modal.keepMounted) modal.remove();\n    },\n  };\n};\nexport const antdModalV5 = (\n  modal: NiceModalHandler,\n): { open: boolean; onCancel: () => void; onOk: () => void; afterClose: () => void } => {\n  const { onOk, onCancel, afterClose } = antdModal(modal);\n  return {\n    open: modal.visible,\n    onOk,\n    onCancel,\n    afterClose,\n  };\n};\nexport const antdDrawer = (\n  modal: NiceModalHandler,\n): { visible: boolean; onClose: () => void; afterVisibleChange: (visible: boolean) => void } => {\n  return {\n    visible: modal.visible,\n    onClose: () => modal.hide(),\n    afterVisibleChange: (v: boolean) => {\n      if (!v) {\n        modal.resolveHide();\n      }\n      !v && !modal.keepMounted && modal.remove();\n    },\n  };\n};\nexport const antdDrawerV5 = (\n  modal: NiceModalHandler,\n): { open: boolean; onClose: () => void; afterOpenChange: (visible: boolean) => void } => {\n  const { onClose, afterVisibleChange: afterOpenChange } = antdDrawer(modal);\n  return {\n    open: modal.visible,\n    onClose,\n    afterOpenChange,\n  };\n};\nexport const muiDialog = (\n  modal: NiceModalHandler,\n): { open: boolean; onClose: () => void; onExited: () => void } => {\n  return {\n    open: modal.visible,\n    onClose: () => modal.hide(),\n    onExited: () => {\n      modal.resolveHide();\n      !modal.keepMounted && modal.remove();\n    },\n  };\n};\n\nexport const muiDialogV5 = (\n  modal: NiceModalHandler,\n): { open: boolean; onClose: () => void; TransitionProps: { onExited: () => void } } => {\n  return {\n    open: modal.visible,\n    onClose: () => modal.hide(),\n    TransitionProps: {\n      onExited: () => {\n        modal.resolveHide();\n        !modal.keepMounted && modal.remove();\n      },\n    },\n  };\n};\nexport const bootstrapDialog = (\n  modal: NiceModalHandler,\n): { show: boolean; onHide: () => void; onExited: () => void } => {\n  return {\n    show: modal.visible,\n    onHide: () => modal.hide(),\n    onExited: () => {\n      modal.resolveHide();\n      !modal.keepMounted && modal.remove();\n    },\n  };\n};\n\nconst NiceModal = {\n  Provider,\n  ModalDef,\n  ModalHolder,\n  NiceModalContext,\n  create,\n  register,\n  getModal,\n  show,\n  hide,\n  remove,\n  useModal,\n  reducer,\n  antdModal,\n  antdDrawer,\n  muiDialog,\n  bootstrapDialog,\n};\n\nexport default NiceModal;\n"], "mappings": "AAAA;;;;;;;;;;;;;;;;;;;;;;;;;AAQA;AACA;;;AAIA,OAAOA,KAAK,IAAIC,SAAS,EAAEC,WAAW,EAAEC,UAAU,EAAEC,UAAU,EAAEC,OAAO,QAAmB,OAAO;AA4EjG,IAAMC,UAAU,GAAGC,MAAM,CAAC,aAAa,CAAC;AACxC,IAAMC,YAAY,GAAmB,EAAE;AACvC,OAAO,IAAMC,gBAAgB,GAAGT,KAAK,CAACU,aAAa,CAAiBF,YAAY,CAAC;AACjF,IAAMG,kBAAkB,GAAGX,KAAK,CAACU,aAAa,CAAgB,IAAI,CAAC;AACnE,IAAME,cAAc,GAKhB,EAAE;AACN,IAAMC,eAAe,GAAG,EAAE;AAE1B,IAAIC,OAAO,GAAG,CAAC;AACf,IAAIC,QAAQ,GAAoC,SAAAA,CAAA;EAC9C,MAAM,IAAIC,KAAK,CAAC,8EAA8E,CAAC;AACjG,CAAC;AACD,IAAMC,MAAM,GAAG,SAAAA,CAAA;EAAM,wBAAeH,OAAO,EAAI;AAA1B,CAA0B;AAE/C;AACA,OAAO,IAAMI,OAAO,GAAG,SAAAA,CACrBC,KAAoC,EACpCC,MAAuB;;EADvB,IAAAD,KAAA;IAAAA,KAAA,GAAAX,YAAoC;EAAA;EAGpC,QAAQY,MAAM,CAACC,IAAI;IACjB,KAAK,iBAAiB;MAAE;QAChB,IAAAC,EAAA,GAAoBF,MAAM,CAACG,OAAO;UAAhCC,OAAO,GAAAF,EAAA,CAAAE,OAAA;UAAEC,IAAI,GAAAH,EAAA,CAAAG,IAAmB;QACxC,OAAAC,QAAA,CAAAA,QAAA,KACKP,KAAK,IAAAQ,EAAA,OAAAA,EAAA,CACPH,OAAO,IAAAE,QAAA,CAAAA,QAAA,KACHP,KAAK,CAACK,OAAO,CAAC;UACjBI,EAAE,EAAEJ,OAAO;UACXC,IAAI,EAAAA,IAAA;UACJ;UACA;UACA;UACAI,OAAO,EAAE,CAAC,CAAChB,eAAe,CAACW,OAAO,CAAC;UACnCM,YAAY,EAAE,CAACjB,eAAe,CAACW,OAAO;QAAC,IAAAG,EAAA;;IAI7C,KAAK,iBAAiB;MAAE;QACd,IAAAH,OAAO,GAAKJ,MAAM,CAACG,OAAO,CAAAC,OAAnB;QACf,IAAI,CAACL,KAAK,CAACK,OAAO,CAAC,EAAE,OAAOL,KAAK;QACjC,OAAAO,QAAA,CAAAA,QAAA,KACKP,KAAK,IAAAY,EAAA,OAAAA,EAAA,CACPP,OAAO,IAAAE,QAAA,CAAAA,QAAA,KACHP,KAAK,CAACK,OAAO,CAAC;UACjBK,OAAO,EAAE;QAAK,IAAAE,EAAA;;IAIpB,KAAK,mBAAmB;MAAE;QAChB,IAAAP,OAAO,GAAKJ,MAAM,CAACG,OAAO,CAAAC,OAAnB;QACf,IAAMQ,QAAQ,GAAAN,QAAA,KAAQP,KAAK,CAAE;QAC7B,OAAOa,QAAQ,CAACR,OAAO,CAAC;QACxB,OAAOQ,QAAQ;;IAEjB,KAAK,sBAAsB;MAAE;QACrB,IAAAC,EAAA,GAAqBb,MAAM,CAACG,OAAO;UAAjCC,OAAO,GAAAS,EAAA,CAAAT,OAAA;UAAEU,KAAK,GAAAD,EAAA,CAAAC,KAAmB;QACzC,OAAAR,QAAA,CAAAA,QAAA,KACKP,KAAK,IAAAgB,EAAA,OAAAA,EAAA,CACPX,OAAO,IAAAE,QAAA,CAAAA,QAAA,KACHP,KAAK,CAACK,OAAO,CAAC,GACdU,KAAK,GAAAC,EAAA;;IAId;MACE,OAAOhB,KAAK;;AAElB,CAAC;AAED;AACA,SAASiB,QAAQA,CAACZ,OAAe;;EAC/B,OAAO,CAAAG,EAAA,GAAAf,cAAc,CAACY,OAAO,CAAC,cAAAG,EAAA,uBAAAA,EAAA,CAAEU,IAAI;AACtC;AAEA;AACA,SAASC,SAASA,CAACd,OAAe,EAAEC,IAA8B;EAChE,OAAO;IACLJ,IAAI,EAAE,iBAAiB;IACvBE,OAAO,EAAE;MACPC,OAAO,EAAAA,OAAA;MACPC,IAAI,EAAAA;;GAEP;AACH;AAEA;AACA,SAASc,aAAaA,CAACf,OAAe,EAAEU,KAA8B;EACpE,OAAO;IACLb,IAAI,EAAE,sBAAsB;IAC5BE,OAAO,EAAE;MACPC,OAAO,EAAAA,OAAA;MACPU,KAAK,EAAAA;;GAER;AACH;AACA;AACA,SAASM,SAASA,CAAChB,OAAe;EAChC,OAAO;IACLH,IAAI,EAAE,iBAAiB;IACvBE,OAAO,EAAE;MACPC,OAAO,EAAAA;;GAEV;AACH;AAEA;AACA,SAASiB,WAAWA,CAACjB,OAAe;EAClC,OAAO;IACLH,IAAI,EAAE,mBAAmB;IACzBE,OAAO,EAAE;MACPC,OAAO,EAAAA;;GAEV;AACH;AAEA,IAAMkB,cAAc,GAAuB,EAAE;AAC7C,IAAMC,kBAAkB,GAAuB,EAAE;AACjD,IAAMC,UAAU,GAAG,SAAAA,CAACC,KAA6B;EAC/C,IAAI,OAAOA,KAAK,KAAK,QAAQ,EAAE,OAAOA,KAAe;EACrD,IAAI,CAACA,KAAK,CAACvC,UAAU,CAAC,EAAE;IACtBuC,KAAK,CAACvC,UAAU,CAAC,GAAGW,MAAM,EAAE;;EAE9B,OAAO4B,KAAK,CAACvC,UAAU,CAAC;AAC1B,CAAC;AAcD;AACA,OAAM,SAAUwC,IAAIA,CAClBD,KAA6B,EAC7BpB,IAA6D;EAE7D,IAAMD,OAAO,GAAGoB,UAAU,CAACC,KAAK,CAAC;EACjC,IAAI,OAAOA,KAAK,KAAK,QAAQ,IAAI,CAACjC,cAAc,CAACY,OAAO,CAAC,EAAE;IACzDuB,QAAQ,CAACvB,OAAO,EAAEqB,KAAiB,CAAC;;EAGtC9B,QAAQ,CAACuB,SAAS,CAACd,OAAO,EAAEC,IAAI,CAAC,CAAC;EAClC,IAAI,CAACiB,cAAc,CAAClB,OAAO,CAAC,EAAE;IAC5B;IACA,IAAIwB,YAAqC;IACzC;IACA,IAAIC,WAAoC;IACxC,IAAMC,OAAO,GAAG,IAAIC,OAAO,CAAC,UAACC,OAAO,EAAEC,MAAM;MAC1CL,YAAU,GAAGI,OAAO;MACpBH,WAAS,GAAGI,MAAM;IACpB,CAAC,CAAC;IACFX,cAAc,CAAClB,OAAO,CAAC,GAAG;MACxB4B,OAAO,EAAEJ,YAAU;MACnBK,MAAM,EAAEJ,WAAS;MACjBC,OAAO,EAAAA;KACR;;EAEH,OAAOR,cAAc,CAAClB,OAAO,CAAC,CAAC0B,OAAO;AACxC;AAGA;AACA,OAAM,SAAUI,IAAIA,CAACT,KAA6B;EAChD,IAAMrB,OAAO,GAAGoB,UAAU,CAACC,KAAK,CAAC;EACjC9B,QAAQ,CAACyB,SAAS,CAAChB,OAAO,CAAC,CAAC;EAC5B;EACA,OAAOkB,cAAc,CAAClB,OAAO,CAAC;EAC9B,IAAI,CAACmB,kBAAkB,CAACnB,OAAO,CAAC,EAAE;IAChC;IACA,IAAI+B,YAAqC;IACzC;IACA,IAAIC,WAAoC;IACxC,IAAMN,OAAO,GAAG,IAAIC,OAAO,CAAC,UAACC,OAAO,EAAEC,MAAM;MAC1CE,YAAU,GAAGH,OAAO;MACpBI,WAAS,GAAGH,MAAM;IACpB,CAAC,CAAC;IACFV,kBAAkB,CAACnB,OAAO,CAAC,GAAG;MAC5B4B,OAAO,EAAEG,YAAU;MACnBF,MAAM,EAAEG,WAAS;MACjBN,OAAO,EAAAA;KACR;;EAEH,OAAOP,kBAAkB,CAACnB,OAAO,CAAC,CAAC0B,OAAO;AAC5C;AAEA,OAAO,IAAMO,MAAM,GAAG,SAAAA,CAACZ,KAA6B;EAClD,IAAMrB,OAAO,GAAGoB,UAAU,CAACC,KAAK,CAAC;EACjC9B,QAAQ,CAAC0B,WAAW,CAACjB,OAAO,CAAC,CAAC;EAC9B,OAAOkB,cAAc,CAAClB,OAAO,CAAC;EAC9B,OAAOmB,kBAAkB,CAACnB,OAAO,CAAC;AACpC,CAAC;AAED,IAAMkC,QAAQ,GAAG,SAAAA,CAAClC,OAAe,EAAEU,KAA8B;EAC/DnB,QAAQ,CAACwB,aAAa,CAACf,OAAO,EAAEU,KAAK,CAAC,CAAC;AACzC,CAAC;AAUD;AACA,OAAM,SAAUyB,QAAQA,CAACd,KAAW,EAAEpB,IAAU;EAC9C,IAAMmC,MAAM,GAAGzD,UAAU,CAACM,gBAAgB,CAAC;EAC3C,IAAMoD,cAAc,GAAG1D,UAAU,CAACQ,kBAAkB,CAAC;EACrD,IAAIa,OAAO,GAAkB,IAAI;EACjC,IAAMsC,cAAc,GAAGjB,KAAK,IAAI,OAAOA,KAAK,KAAK,QAAQ;EACzD,IAAI,CAACA,KAAK,EAAE;IACVrB,OAAO,GAAGqC,cAAc;GACzB,MAAM;IACLrC,OAAO,GAAGoB,UAAU,CAACC,KAAK,CAAC;;EAG7B;EACA,IAAI,CAACrB,OAAO,EAAE,MAAM,IAAIR,KAAK,CAAC,0CAA0C,CAAC;EAEzE,IAAM+C,GAAG,GAAGvC,OAAiB;EAC7B;EACAvB,SAAS,CAAC;IACR,IAAI6D,cAAc,IAAI,CAAClD,cAAc,CAACmD,GAAG,CAAC,EAAE;MAC1ChB,QAAQ,CAACgB,GAAG,EAAElB,KAAiB,EAAEpB,IAAI,CAAC;;EAE1C,CAAC,EAAE,CAACqC,cAAc,EAAEC,GAAG,EAAElB,KAAK,EAAEpB,IAAI,CAAC,CAAC;EAEtC,IAAMuC,SAAS,GAAGJ,MAAM,CAACG,GAAG,CAAC;EAE7B,IAAME,YAAY,GAAG/D,WAAW,CAAC,UAACuB,IAA8B;IAAK,OAAAqB,IAAI,CAACiB,GAAG,EAAEtC,IAAI,CAAC;EAAf,CAAe,EAAE,CAACsC,GAAG,CAAC,CAAC;EAC5F,IAAMG,YAAY,GAAGhE,WAAW,CAAC;IAAM,OAAAoD,IAAI,CAACS,GAAG,CAAC;EAAT,CAAS,EAAE,CAACA,GAAG,CAAC,CAAC;EACxD,IAAMI,cAAc,GAAGjE,WAAW,CAAC;IAAM,OAAAuD,MAAM,CAACM,GAAG,CAAC;EAAX,CAAW,EAAE,CAACA,GAAG,CAAC,CAAC;EAC5D,IAAMK,eAAe,GAAGlE,WAAW,CACjC,UAACuB,IAAc;;IACb,CAAAE,EAAA,GAAAe,cAAc,CAACqB,GAAG,CAAC,cAAApC,EAAA,uBAAAA,EAAA,CAAEyB,OAAO,CAAC3B,IAAI,CAAC;IAClC,OAAOiB,cAAc,CAACqB,GAAG,CAAC;EAC5B,CAAC,EACD,CAACA,GAAG,CAAC,CACN;EACD,IAAMM,cAAc,GAAGnE,WAAW,CAChC,UAACuB,IAAc;;IACb,CAAAE,EAAA,GAAAe,cAAc,CAACqB,GAAG,CAAC,cAAApC,EAAA,uBAAAA,EAAA,CAAE0B,MAAM,CAAC5B,IAAI,CAAC;IACjC,OAAOiB,cAAc,CAACqB,GAAG,CAAC;EAC5B,CAAC,EACD,CAACA,GAAG,CAAC,CACN;EACD,IAAMO,WAAW,GAAGpE,WAAW,CAC7B,UAACuB,IAAc;;IACb,CAAAE,EAAA,GAAAgB,kBAAkB,CAACoB,GAAG,CAAC,cAAApC,EAAA,uBAAAA,EAAA,CAAEyB,OAAO,CAAC3B,IAAI,CAAC;IACtC,OAAOkB,kBAAkB,CAACoB,GAAG,CAAC;EAChC,CAAC,EACD,CAACA,GAAG,CAAC,CACN;EAED,OAAO1D,OAAO,CACZ;IAAM,OAAC;MACLuB,EAAE,EAAEmC,GAAG;MACPtC,IAAI,EAAEuC,SAAS,aAATA,SAAS,uBAATA,SAAS,CAAEvC,IAAI;MACrBI,OAAO,EAAE,CAAC,EAACmC,SAAS,aAATA,SAAS,uBAATA,SAAS,CAAEnC,OAAO;MAC7B0C,WAAW,EAAE,CAAC,EAACP,SAAS,aAATA,SAAS,uBAATA,SAAS,CAAEO,WAAW;MACrCzB,IAAI,EAAEmB,YAAY;MAClBX,IAAI,EAAEY,YAAY;MAClBT,MAAM,EAAEU,cAAc;MACtBf,OAAO,EAAEgB,eAAe;MACxBf,MAAM,EAAEgB,cAAc;MACtBC,WAAW,EAAAA;KACZ;EAXK,CAWJ,EACF,CACEP,GAAG,EACHC,SAAS,aAATA,SAAS,uBAATA,SAAS,CAAEvC,IAAI,EACfuC,SAAS,aAATA,SAAS,uBAATA,SAAS,CAAEnC,OAAO,EAClBmC,SAAS,aAATA,SAAS,uBAATA,SAAS,CAAEO,WAAW,EACtBN,YAAY,EACZC,YAAY,EACZC,cAAc,EACdC,eAAe,EACfC,cAAc,EACdC,WAAW,CACZ,CACF;AACH;AACA,OAAO,IAAME,MAAM,GAAG,SAAAA,CACpBC,IAA4B;EAE5B,OAAO,UAAC9C,EAA6C;;IAA3C,IAAA+C,cAAc,GAAA/C,EAAA,CAAA+C,cAAA;MAAEH,WAAW,GAAA5C,EAAA,CAAA4C,WAAA;MAAE3C,EAAE,GAAAD,EAAA,CAAAC,EAAA;MAAK+C,KAAK,GAAAC,MAAA,CAAAjD,EAAA,EAA3C,uCAA6C,CAAF;IAC3C,IAAAQ,EAAA,GAAiBwB,QAAQ,CAAC/B,EAAE,CAAC;MAA3BH,IAAI,GAAAU,EAAA,CAAAV,IAAA;MAAEqB,IAAI,GAAAX,EAAA,CAAAW,IAAiB;IAEnC;IACA,IAAMc,MAAM,GAAGzD,UAAU,CAACM,gBAAgB,CAAC;IAC3C,IAAMoE,WAAW,GAAG,CAAC,CAACjB,MAAM,CAAChC,EAAE,CAAC;IAEhC3B,SAAS,CAAC;MACR;MACA,IAAIyE,cAAc,EAAE;QAClB5B,IAAI,EAAE;;MAGRjC,eAAe,CAACe,EAAE,CAAC,GAAG,IAAI;MAE1B,OAAO;QACL,OAAOf,eAAe,CAACe,EAAE,CAAC;MAC5B,CAAC;IACH,CAAC,EAAE,CAACA,EAAE,EAAEkB,IAAI,EAAE4B,cAAc,CAAC,CAAC;IAE9BzE,SAAS,CAAC;MACR,IAAIsE,WAAW,EAAEb,QAAQ,CAAC9B,EAAE,EAAE;QAAE2C,WAAW,EAAE;MAAI,CAAE,CAAC;IACtD,CAAC,EAAE,CAAC3C,EAAE,EAAE2C,WAAW,CAAC,CAAC;IAErB,IAAMzC,YAAY,GAAG,CAAAC,EAAA,GAAA6B,MAAM,CAAChC,EAAE,CAAC,cAAAG,EAAA,uBAAAA,EAAA,CAAED,YAAY;IAC7C;IACA;IACA;IACA7B,SAAS,CAAC;MACR,IAAI6B,YAAY,EAAE;QAChB;QACAgB,IAAI,CAACrB,IAAI,CAAC;;IAEd,CAAC,EAAE,CAACK,YAAY,EAAEL,IAAI,EAAEqB,IAAI,CAAC,CAAC;IAE9B,IAAI,CAAC+B,WAAW,EAAE,OAAO,IAAI;IAC7B,OACE7E,KAAA,CAAA8E,aAAA,CAACnE,kBAAkB,CAACoE,QAAQ;MAACC,KAAK,EAAEpD;IAAE,GACpC5B,KAAA,CAAA8E,aAAA,CAACL,IAAI,EAAA/C,QAAA,KAAMiD,KAAW,EAAMlD,IAAI,EAAI,CACR;EAElC,CAAC;AACH,CAAC;AAED;AACA,OAAO,IAAMsB,QAAQ,GAAG,SAAAA,CACtBnB,EAAU,EACVS,IAAO,EACPsC,KAAiC;EAEjC,IAAI,CAAC/D,cAAc,CAACgB,EAAE,CAAC,EAAE;IACvBhB,cAAc,CAACgB,EAAE,CAAC,GAAG;MAAES,IAAI,EAAAA,IAAA;MAAEsC,KAAK,EAAAA;IAAA,CAAE;GACrC,MAAM;IACL/D,cAAc,CAACgB,EAAE,CAAC,CAAC+C,KAAK,GAAGA,KAAK;;AAEpC,CAAC;AAED;;;;AAIA,OAAO,IAAMM,UAAU,GAAG,SAAAA,CAACrD,EAAU;EACnC,OAAOhB,cAAc,CAACgB,EAAE,CAAC;AAC3B,CAAC;AAED;AACA;AACA,IAAMsD,oBAAoB,GAAa,SAAAA,CAAA;EACrC,IAAMtB,MAAM,GAAGzD,UAAU,CAACM,gBAAgB,CAAC;EAC3C,IAAM0E,eAAe,GAAGC,MAAM,CAACC,IAAI,CAACzB,MAAM,CAAC,CAAC0B,MAAM,CAAC,UAAC1D,EAAE;IAAK,QAAC,CAACgC,MAAM,CAAChC,EAAE,CAAC;EAAZ,CAAY,CAAC;EACxEuD,eAAe,CAACI,OAAO,CAAC,UAAC3D,EAAE;IACzB,IAAI,CAAChB,cAAc,CAACgB,EAAE,CAAC,IAAI,CAACf,eAAe,CAACe,EAAE,CAAC,EAAE;MAC/C4D,OAAO,CAACC,IAAI,CACV,4BAA0B7D,EAAE,sEAAmE,CAChG;MACD;;EAEJ,CAAC,CAAC;EAEF,IAAM8D,QAAQ,GAAGP,eAAe,CAC7BG,MAAM,CAAC,UAAC1D,EAAE;IAAK,OAAAhB,cAAc,CAACgB,EAAE,CAAC;EAAlB,CAAkB,CAAC,CAClC+D,GAAG,CAAC,UAAC/D,EAAE;IAAK,OAAAF,QAAA;MACXE,EAAE,EAAAA;IAAA,GACChB,cAAc,CAACgB,EAAE,CAAC;EAFV,CAGX,CAAC;EAEL,OACE5B,KAAA,CAAA8E,aAAA,CAAA9E,KAAA,CAAA4F,QAAA,QACGF,QAAQ,CAACC,GAAG,CAAC,UAACE,CAAC;IAAK,OACnB7F,KAAA,CAAA8E,aAAA,CAACe,CAAC,CAACxD,IAAI,EAAAX,QAAA;MAACoE,GAAG,EAAED,CAAC,CAACjE,EAAE;MAAEA,EAAE,EAAEiE,CAAC,CAACjE;IAAE,GAAMiE,CAAC,CAAClB,KAAK,EAAI;EADzB,CAEpB,CAAC,CACD;AAEP,CAAC;AAED,IAAMoB,oBAAoB,GAAa,SAAAA,CAACpE,EAAY;MAAVqE,QAAQ,GAAArE,EAAA,CAAAqE,QAAA;EAChD,IAAMC,GAAG,GAAG7F,UAAU,CAACc,OAAO,EAAEV,YAAY,CAAC;EAC7C,IAAMoD,MAAM,GAAGqC,GAAG,CAAC,CAAC,CAAC;EACrBlF,QAAQ,GAAGkF,GAAG,CAAC,CAAC,CAAC;EACjB,OACEjG,KAAA,CAAA8E,aAAA,CAACrE,gBAAgB,CAACsE,QAAQ;IAACC,KAAK,EAAEpB;EAAM,GACrCoC,QAAQ,EACThG,KAAA,CAAA8E,aAAA,CAACI,oBAAoB,OAAG,CACE;AAEhC,CAAC;AAED,OAAO,IAAMH,QAAQ,GAAsC,SAAAA,CAACpD,EAQ3D;MAPCqE,QAAQ,GAAArE,EAAA,CAAAqE,QAAA;IACEE,aAAa,GAAAvE,EAAA,CAAAZ,QAAA;IACfoF,WAAW,GAAAxE,EAAA,CAAAiC,MAAA;EAMnB,IAAI,CAACsC,aAAa,IAAI,CAACC,WAAW,EAAE;IAClC,OAAOnG,KAAA,CAAA8E,aAAA,CAACiB,oBAAoB,QAAEC,QAAQ,CAAwB;;EAEhEjF,QAAQ,GAAGmF,aAAa;EACxB,OACElG,KAAA,CAAA8E,aAAA,CAACrE,gBAAgB,CAACsE,QAAQ;IAACC,KAAK,EAAEmB;EAAW,GAC1CH,QAAQ,EACThG,KAAA,CAAA8E,aAAA,CAACI,oBAAoB,OAAG,CACE;AAEhC,CAAC;AAED;;;;;;AAMA,OAAO,IAAMkB,QAAQ,GAAsC,SAAAA,CAACzE,EAM3D;MALCC,EAAE,GAAAD,EAAA,CAAAC,EAAA;IACFyE,SAAS,GAAA1E,EAAA,CAAA0E,SAAA;EAKTpG,SAAS,CAAC;IACR8C,QAAQ,CAACnB,EAAE,EAAEyE,SAAS,CAAC;IACvB,OAAO;MACLpB,UAAU,CAACrD,EAAE,CAAC;IAChB,CAAC;EACH,CAAC,EAAE,CAACA,EAAE,EAAEyE,SAAS,CAAC,CAAC;EACnB,OAAO,IAAI;AACb,CAAC;AAED;;;;;;;;;;;AAWA,OAAO,IAAMC,WAAW,GAAsC,SAAAA,CAAC3E,EAQ9D;;EAPC,IAAAkB,KAAK,GAAAlB,EAAA,CAAAkB,KAAA;IACLV,EAAA,GAAAR,EAAA,CAAA4E,OAAY;IAAZA,OAAO,GAAApE,EAAA,cAAG,EAAE,GAAAA,EAAA;IACTqE,SAAS,GAAA5B,MAAA,CAAAjD,EAAA,EAHiD,oBAI9D,CADa;EAMZ,IAAMoC,GAAG,GAAG1D,OAAO,CAAC;IAAM,OAAAY,MAAM,EAAE;EAAR,CAAQ,EAAE,EAAE,CAAC;EACvC,IAAMwF,SAAS,GAAG,OAAO5D,KAAK,KAAK,QAAQ,GAAG,CAAAd,EAAA,GAAAnB,cAAc,CAACiC,KAAK,CAAC,cAAAd,EAAA,uBAAAA,EAAA,CAAEM,IAAI,GAAGQ,KAAK;EAEjF,IAAI,CAAC0D,OAAO,EAAE;IACZ,MAAM,IAAIvF,KAAK,CAAC,4CAA4C,CAAC;;EAE/D,IAAI,CAACyF,SAAS,EAAE;IACd,MAAM,IAAIzF,KAAK,CAAC,4BAA0B6B,KAAK,+BAA4B,CAAC;;EAE9E0D,OAAO,CAACzD,IAAI,GAAG5C,WAAW,CAAC,UAACuB,IAAS;IAAK,OAAAqB,IAAI,CAACiB,GAAG,EAAEtC,IAAI,CAAC;EAAf,CAAe,EAAE,CAACsC,GAAG,CAAC,CAAC;EACjEwC,OAAO,CAACjD,IAAI,GAAGpD,WAAW,CAAC;IAAM,OAAAoD,IAAI,CAACS,GAAG,CAAC;EAAT,CAAS,EAAE,CAACA,GAAG,CAAC,CAAC;EAElD,OAAO/D,KAAA,CAAA8E,aAAA,CAAC2B,SAAS,EAAA/E,QAAA;IAACE,EAAE,EAAEmC;EAAG,GAAMyC,SAAS,EAAI;AAC9C,CAAC;AAED,OAAO,IAAME,SAAS,GAAG,SAAAA,CACvB7D,KAAuB;EAEvB,OAAO;IACLhB,OAAO,EAAEgB,KAAK,CAAChB,OAAO;IACtB8E,IAAI,EAAE,SAAAA,CAAA;MAAM,OAAA9D,KAAK,CAACS,IAAI,EAAE;IAAZ,CAAY;IACxBsD,QAAQ,EAAE,SAAAA,CAAA;MAAM,OAAA/D,KAAK,CAACS,IAAI,EAAE;IAAZ,CAAY;IAC5BuD,UAAU,EAAE,SAAAA,CAAA;MACV;MACAhE,KAAK,CAACyB,WAAW,EAAE;MACnB,IAAI,CAACzB,KAAK,CAAC0B,WAAW,EAAE1B,KAAK,CAACY,MAAM,EAAE;IACxC;GACD;AACH,CAAC;AACD,OAAO,IAAMqD,WAAW,GAAG,SAAAA,CACzBjE,KAAuB;EAEjB,IAAAlB,EAAA,GAAiC+E,SAAS,CAAC7D,KAAK,CAAC;IAA/C8D,IAAI,GAAAhF,EAAA,CAAAgF,IAAA;IAAEC,QAAQ,GAAAjF,EAAA,CAAAiF,QAAA;IAAEC,UAAU,GAAAlF,EAAA,CAAAkF,UAAqB;EACvD,OAAO;IACLE,IAAI,EAAElE,KAAK,CAAChB,OAAO;IACnB8E,IAAI,EAAAA,IAAA;IACJC,QAAQ,EAAAA,QAAA;IACRC,UAAU,EAAAA;GACX;AACH,CAAC;AACD,OAAO,IAAMG,UAAU,GAAG,SAAAA,CACxBnE,KAAuB;EAEvB,OAAO;IACLhB,OAAO,EAAEgB,KAAK,CAAChB,OAAO;IACtBoF,OAAO,EAAE,SAAAA,CAAA;MAAM,OAAApE,KAAK,CAACS,IAAI,EAAE;IAAZ,CAAY;IAC3B4D,kBAAkB,EAAE,SAAAA,CAACC,CAAU;MAC7B,IAAI,CAACA,CAAC,EAAE;QACNtE,KAAK,CAACyB,WAAW,EAAE;;MAErB,CAAC6C,CAAC,IAAI,CAACtE,KAAK,CAAC0B,WAAW,IAAI1B,KAAK,CAACY,MAAM,EAAE;IAC5C;GACD;AACH,CAAC;AACD,OAAO,IAAM2D,YAAY,GAAG,SAAAA,CAC1BvE,KAAuB;EAEjB,IAAAlB,EAAA,GAAmDqF,UAAU,CAACnE,KAAK,CAAC;IAAlEoE,OAAO,GAAAtF,EAAA,CAAAsF,OAAA;IAAsBI,eAAe,GAAA1F,EAAA,CAAAuF,kBAAsB;EAC1E,OAAO;IACLH,IAAI,EAAElE,KAAK,CAAChB,OAAO;IACnBoF,OAAO,EAAAA,OAAA;IACPI,eAAe,EAAAA;GAChB;AACH,CAAC;AACD,OAAO,IAAMC,SAAS,GAAG,SAAAA,CACvBzE,KAAuB;EAEvB,OAAO;IACLkE,IAAI,EAAElE,KAAK,CAAChB,OAAO;IACnBoF,OAAO,EAAE,SAAAA,CAAA;MAAM,OAAApE,KAAK,CAACS,IAAI,EAAE;IAAZ,CAAY;IAC3BiE,QAAQ,EAAE,SAAAA,CAAA;MACR1E,KAAK,CAACyB,WAAW,EAAE;MACnB,CAACzB,KAAK,CAAC0B,WAAW,IAAI1B,KAAK,CAACY,MAAM,EAAE;IACtC;GACD;AACH,CAAC;AAED,OAAO,IAAM+D,WAAW,GAAG,SAAAA,CACzB3E,KAAuB;EAEvB,OAAO;IACLkE,IAAI,EAAElE,KAAK,CAAChB,OAAO;IACnBoF,OAAO,EAAE,SAAAA,CAAA;MAAM,OAAApE,KAAK,CAACS,IAAI,EAAE;IAAZ,CAAY;IAC3BmE,eAAe,EAAE;MACfF,QAAQ,EAAE,SAAAA,CAAA;QACR1E,KAAK,CAACyB,WAAW,EAAE;QACnB,CAACzB,KAAK,CAAC0B,WAAW,IAAI1B,KAAK,CAACY,MAAM,EAAE;MACtC;;GAEH;AACH,CAAC;AACD,OAAO,IAAMiE,eAAe,GAAG,SAAAA,CAC7B7E,KAAuB;EAEvB,OAAO;IACLC,IAAI,EAAED,KAAK,CAAChB,OAAO;IACnB8F,MAAM,EAAE,SAAAA,CAAA;MAAM,OAAA9E,KAAK,CAACS,IAAI,EAAE;IAAZ,CAAY;IAC1BiE,QAAQ,EAAE,SAAAA,CAAA;MACR1E,KAAK,CAACyB,WAAW,EAAE;MACnB,CAACzB,KAAK,CAAC0B,WAAW,IAAI1B,KAAK,CAACY,MAAM,EAAE;IACtC;GACD;AACH,CAAC;AAED,IAAMmE,SAAS,GAAG;EAChB7C,QAAQ,EAAAA,QAAA;EACRqB,QAAQ,EAAAA,QAAA;EACRE,WAAW,EAAAA,WAAA;EACX7F,gBAAgB,EAAAA,gBAAA;EAChB+D,MAAM,EAAAA,MAAA;EACNzB,QAAQ,EAAAA,QAAA;EACRX,QAAQ,EAAAA,QAAA;EACRU,IAAI,EAAAA,IAAA;EACJQ,IAAI,EAAAA,IAAA;EACJG,MAAM,EAAAA,MAAA;EACNE,QAAQ,EAAAA,QAAA;EACRzC,OAAO,EAAAA,OAAA;EACPwF,SAAS,EAAAA,SAAA;EACTM,UAAU,EAAAA,UAAA;EACVM,SAAS,EAAAA,SAAA;EACTI,eAAe,EAAAA;CAChB;AAED,eAAeE,SAAS", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}