{"ast": null, "code": "var _jsxFileName = \"D:\\\\Documents_2\\\\New Coding\\\\CoderBuddy\\\\CoderBuddy\\\\frontend\\\\src\\\\index.js\";\nimport React from 'react';\nimport { createRoot } from 'react-dom/client';\nimport App from './App';\nimport NiceModal from '@ebay/nice-modal-react';\nimport './monacoLoaderConfig';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst container = document.getElementById('root');\nconst root = createRoot(container);\nroot.render(/*#__PURE__*/_jsxDEV(React.StrictMode, {\n  children: /*#__PURE__*/_jsxDEV(NiceModal.Provider, {\n    children: /*#__PURE__*/_jsxDEV(App, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 12,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 11,\n    columnNumber: 5\n  }, this)\n}, void 0, false, {\n  fileName: _jsxFileName,\n  lineNumber: 10,\n  columnNumber: 3\n}, this));", "map": {"version": 3, "names": ["React", "createRoot", "App", "NiceModal", "jsxDEV", "_jsxDEV", "container", "document", "getElementById", "root", "render", "StrictMode", "children", "Provider", "fileName", "_jsxFileName", "lineNumber", "columnNumber"], "sources": ["D:/Documents_2/New Coding/CoderBuddy/CoderBuddy/frontend/src/index.js"], "sourcesContent": ["import React from 'react';\r\nimport { createRoot } from 'react-dom/client';\r\nimport App from './App';\r\nimport NiceModal from '@ebay/nice-modal-react';\r\nimport './monacoLoaderConfig';\r\n\r\nconst container = document.getElementById('root');\r\nconst root = createRoot(container);\r\nroot.render(\r\n  <React.StrictMode>\r\n    <NiceModal.Provider>\r\n      <App />\r\n    </NiceModal.Provider>\r\n  </React.StrictMode>\r\n); "], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,UAAU,QAAQ,kBAAkB;AAC7C,OAAOC,GAAG,MAAM,OAAO;AACvB,OAAOC,SAAS,MAAM,wBAAwB;AAC9C,OAAO,sBAAsB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE9B,MAAMC,SAAS,GAAGC,QAAQ,CAACC,cAAc,CAAC,MAAM,CAAC;AACjD,MAAMC,IAAI,GAAGR,UAAU,CAACK,SAAS,CAAC;AAClCG,IAAI,CAACC,MAAM,cACTL,OAAA,CAACL,KAAK,CAACW,UAAU;EAAAC,QAAA,eACfP,OAAA,CAACF,SAAS,CAACU,QAAQ;IAAAD,QAAA,eACjBP,OAAA,CAACH,GAAG;MAAAY,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACW;AAAC;EAAAH,QAAA,EAAAC,YAAA;EAAAC,UAAA;EAAAC,YAAA;AAAA,OACL,CACpB,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}