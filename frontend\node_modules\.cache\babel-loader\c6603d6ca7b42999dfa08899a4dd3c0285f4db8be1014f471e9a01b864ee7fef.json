{"ast": null, "code": "import createCache from '@emotion/cache';\nimport { serializeStyles } from '@emotion/serialize';\nimport { getRegisteredStyles, insertStyles } from '@emotion/utils';\nfunction insertWithoutScoping(cache, serialized) {\n  if (cache.inserted[serialized.name] === undefined) {\n    return cache.insert('', serialized, cache.sheet, true);\n  }\n}\nfunction merge(registered, css, className) {\n  var registeredStyles = [];\n  var rawClassName = getRegisteredStyles(registered, registeredStyles, className);\n  if (registeredStyles.length < 2) {\n    return className;\n  }\n  return rawClassName + css(registeredStyles);\n}\nvar createEmotion = function createEmotion(options) {\n  var cache = createCache(options);\n  cache.sheet.speedy = function (value) {\n    if (this.ctr !== 0) {\n      throw new Error('speedy must be changed before any rules are inserted');\n    }\n    this.isSpeedy = value;\n  };\n  cache.compat = true;\n  var css = function css() {\n    for (var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++) {\n      args[_key] = arguments[_key];\n    }\n    var serialized = serializeStyles(args, cache.registered, undefined);\n    insertStyles(cache, serialized, false);\n    return cache.key + \"-\" + serialized.name;\n  };\n  var keyframes = function keyframes() {\n    for (var _len2 = arguments.length, args = new Array(_len2), _key2 = 0; _key2 < _len2; _key2++) {\n      args[_key2] = arguments[_key2];\n    }\n    var serialized = serializeStyles(args, cache.registered);\n    var animation = \"animation-\" + serialized.name;\n    insertWithoutScoping(cache, {\n      name: serialized.name,\n      styles: \"@keyframes \" + animation + \"{\" + serialized.styles + \"}\"\n    });\n    return animation;\n  };\n  var injectGlobal = function injectGlobal() {\n    for (var _len3 = arguments.length, args = new Array(_len3), _key3 = 0; _key3 < _len3; _key3++) {\n      args[_key3] = arguments[_key3];\n    }\n    var serialized = serializeStyles(args, cache.registered);\n    insertWithoutScoping(cache, serialized);\n  };\n  var cx = function cx() {\n    for (var _len4 = arguments.length, args = new Array(_len4), _key4 = 0; _key4 < _len4; _key4++) {\n      args[_key4] = arguments[_key4];\n    }\n    return merge(cache.registered, css, classnames(args));\n  };\n  return {\n    css: css,\n    cx: cx,\n    injectGlobal: injectGlobal,\n    keyframes: keyframes,\n    hydrate: function hydrate(ids) {\n      ids.forEach(function (key) {\n        cache.inserted[key] = true;\n      });\n    },\n    flush: function flush() {\n      cache.registered = {};\n      cache.inserted = {};\n      cache.sheet.flush();\n    },\n    sheet: cache.sheet,\n    cache: cache,\n    getRegisteredStyles: getRegisteredStyles.bind(null, cache.registered),\n    merge: merge.bind(null, cache.registered, css)\n  };\n};\nvar classnames = function classnames(args) {\n  var cls = '';\n  for (var i = 0; i < args.length; i++) {\n    var arg = args[i];\n    if (arg == null) continue;\n    var toAdd = void 0;\n    switch (typeof arg) {\n      case 'boolean':\n        break;\n      case 'object':\n        {\n          if (Array.isArray(arg)) {\n            toAdd = classnames(arg);\n          } else {\n            toAdd = '';\n            for (var k in arg) {\n              if (arg[k] && k) {\n                toAdd && (toAdd += ' ');\n                toAdd += k;\n              }\n            }\n          }\n          break;\n        }\n      default:\n        {\n          toAdd = arg;\n        }\n    }\n    if (toAdd) {\n      cls && (cls += ' ');\n      cls += toAdd;\n    }\n  }\n  return cls;\n};\nexport { createEmotion as default };", "map": {"version": 3, "names": ["createCache", "serializeStyles", "getRegisteredStyles", "insertStyles", "insertWithoutScoping", "cache", "serialized", "inserted", "name", "undefined", "insert", "sheet", "merge", "registered", "css", "className", "registeredStyles", "rawClassName", "length", "createEmotion", "options", "speedy", "value", "ctr", "Error", "isSpeedy", "compat", "_len", "arguments", "args", "Array", "_key", "key", "keyframes", "_len2", "_key2", "animation", "styles", "injectGlobal", "_len3", "_key3", "cx", "_len4", "_key4", "classnames", "hydrate", "ids", "for<PERSON>ach", "flush", "bind", "cls", "i", "arg", "toAdd", "isArray", "k", "default"], "sources": ["D:/Documents_2/New Coding/CoderBuddy/CoderBuddy/frontend/node_modules/@emotion/css/create-instance/dist/emotion-css-create-instance.development.esm.js"], "sourcesContent": ["import createCache from '@emotion/cache';\nimport { serializeStyles } from '@emotion/serialize';\nimport { getRegisteredStyles, insertStyles } from '@emotion/utils';\n\nfunction insertWithoutScoping(cache, serialized) {\n  if (cache.inserted[serialized.name] === undefined) {\n    return cache.insert('', serialized, cache.sheet, true);\n  }\n}\n\nfunction merge(registered, css, className) {\n  var registeredStyles = [];\n  var rawClassName = getRegisteredStyles(registered, registeredStyles, className);\n\n  if (registeredStyles.length < 2) {\n    return className;\n  }\n\n  return rawClassName + css(registeredStyles);\n}\n\nvar createEmotion = function createEmotion(options) {\n  var cache = createCache(options);\n\n  cache.sheet.speedy = function (value) {\n    if (this.ctr !== 0) {\n      throw new Error('speedy must be changed before any rules are inserted');\n    }\n\n    this.isSpeedy = value;\n  };\n\n  cache.compat = true;\n\n  var css = function css() {\n    for (var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++) {\n      args[_key] = arguments[_key];\n    }\n\n    var serialized = serializeStyles(args, cache.registered, undefined);\n    insertStyles(cache, serialized, false);\n    return cache.key + \"-\" + serialized.name;\n  };\n\n  var keyframes = function keyframes() {\n    for (var _len2 = arguments.length, args = new Array(_len2), _key2 = 0; _key2 < _len2; _key2++) {\n      args[_key2] = arguments[_key2];\n    }\n\n    var serialized = serializeStyles(args, cache.registered);\n    var animation = \"animation-\" + serialized.name;\n    insertWithoutScoping(cache, {\n      name: serialized.name,\n      styles: \"@keyframes \" + animation + \"{\" + serialized.styles + \"}\"\n    });\n    return animation;\n  };\n\n  var injectGlobal = function injectGlobal() {\n    for (var _len3 = arguments.length, args = new Array(_len3), _key3 = 0; _key3 < _len3; _key3++) {\n      args[_key3] = arguments[_key3];\n    }\n\n    var serialized = serializeStyles(args, cache.registered);\n    insertWithoutScoping(cache, serialized);\n  };\n\n  var cx = function cx() {\n    for (var _len4 = arguments.length, args = new Array(_len4), _key4 = 0; _key4 < _len4; _key4++) {\n      args[_key4] = arguments[_key4];\n    }\n\n    return merge(cache.registered, css, classnames(args));\n  };\n\n  return {\n    css: css,\n    cx: cx,\n    injectGlobal: injectGlobal,\n    keyframes: keyframes,\n    hydrate: function hydrate(ids) {\n      ids.forEach(function (key) {\n        cache.inserted[key] = true;\n      });\n    },\n    flush: function flush() {\n      cache.registered = {};\n      cache.inserted = {};\n      cache.sheet.flush();\n    },\n    sheet: cache.sheet,\n    cache: cache,\n    getRegisteredStyles: getRegisteredStyles.bind(null, cache.registered),\n    merge: merge.bind(null, cache.registered, css)\n  };\n};\n\nvar classnames = function classnames(args) {\n  var cls = '';\n\n  for (var i = 0; i < args.length; i++) {\n    var arg = args[i];\n    if (arg == null) continue;\n    var toAdd = void 0;\n\n    switch (typeof arg) {\n      case 'boolean':\n        break;\n\n      case 'object':\n        {\n          if (Array.isArray(arg)) {\n            toAdd = classnames(arg);\n          } else {\n            toAdd = '';\n\n            for (var k in arg) {\n              if (arg[k] && k) {\n                toAdd && (toAdd += ' ');\n                toAdd += k;\n              }\n            }\n          }\n\n          break;\n        }\n\n      default:\n        {\n          toAdd = arg;\n        }\n    }\n\n    if (toAdd) {\n      cls && (cls += ' ');\n      cls += toAdd;\n    }\n  }\n\n  return cls;\n};\n\nexport { createEmotion as default };\n"], "mappings": "AAAA,OAAOA,WAAW,MAAM,gBAAgB;AACxC,SAASC,eAAe,QAAQ,oBAAoB;AACpD,SAASC,mBAAmB,EAAEC,YAAY,QAAQ,gBAAgB;AAElE,SAASC,oBAAoBA,CAACC,KAAK,EAAEC,UAAU,EAAE;EAC/C,IAAID,KAAK,CAACE,QAAQ,CAACD,UAAU,CAACE,IAAI,CAAC,KAAKC,SAAS,EAAE;IACjD,OAAOJ,KAAK,CAACK,MAAM,CAAC,EAAE,EAAEJ,UAAU,EAAED,KAAK,CAACM,KAAK,EAAE,IAAI,CAAC;EACxD;AACF;AAEA,SAASC,KAAKA,CAACC,UAAU,EAAEC,GAAG,EAAEC,SAAS,EAAE;EACzC,IAAIC,gBAAgB,GAAG,EAAE;EACzB,IAAIC,YAAY,GAAGf,mBAAmB,CAACW,UAAU,EAAEG,gBAAgB,EAAED,SAAS,CAAC;EAE/E,IAAIC,gBAAgB,CAACE,MAAM,GAAG,CAAC,EAAE;IAC/B,OAAOH,SAAS;EAClB;EAEA,OAAOE,YAAY,GAAGH,GAAG,CAACE,gBAAgB,CAAC;AAC7C;AAEA,IAAIG,aAAa,GAAG,SAASA,aAAaA,CAACC,OAAO,EAAE;EAClD,IAAIf,KAAK,GAAGL,WAAW,CAACoB,OAAO,CAAC;EAEhCf,KAAK,CAACM,KAAK,CAACU,MAAM,GAAG,UAAUC,KAAK,EAAE;IACpC,IAAI,IAAI,CAACC,GAAG,KAAK,CAAC,EAAE;MAClB,MAAM,IAAIC,KAAK,CAAC,sDAAsD,CAAC;IACzE;IAEA,IAAI,CAACC,QAAQ,GAAGH,KAAK;EACvB,CAAC;EAEDjB,KAAK,CAACqB,MAAM,GAAG,IAAI;EAEnB,IAAIZ,GAAG,GAAG,SAASA,GAAGA,CAAA,EAAG;IACvB,KAAK,IAAIa,IAAI,GAAGC,SAAS,CAACV,MAAM,EAAEW,IAAI,GAAG,IAAIC,KAAK,CAACH,IAAI,CAAC,EAAEI,IAAI,GAAG,CAAC,EAAEA,IAAI,GAAGJ,IAAI,EAAEI,IAAI,EAAE,EAAE;MACvFF,IAAI,CAACE,IAAI,CAAC,GAAGH,SAAS,CAACG,IAAI,CAAC;IAC9B;IAEA,IAAIzB,UAAU,GAAGL,eAAe,CAAC4B,IAAI,EAAExB,KAAK,CAACQ,UAAU,EAAEJ,SAAS,CAAC;IACnEN,YAAY,CAACE,KAAK,EAAEC,UAAU,EAAE,KAAK,CAAC;IACtC,OAAOD,KAAK,CAAC2B,GAAG,GAAG,GAAG,GAAG1B,UAAU,CAACE,IAAI;EAC1C,CAAC;EAED,IAAIyB,SAAS,GAAG,SAASA,SAASA,CAAA,EAAG;IACnC,KAAK,IAAIC,KAAK,GAAGN,SAAS,CAACV,MAAM,EAAEW,IAAI,GAAG,IAAIC,KAAK,CAACI,KAAK,CAAC,EAAEC,KAAK,GAAG,CAAC,EAAEA,KAAK,GAAGD,KAAK,EAAEC,KAAK,EAAE,EAAE;MAC7FN,IAAI,CAACM,KAAK,CAAC,GAAGP,SAAS,CAACO,KAAK,CAAC;IAChC;IAEA,IAAI7B,UAAU,GAAGL,eAAe,CAAC4B,IAAI,EAAExB,KAAK,CAACQ,UAAU,CAAC;IACxD,IAAIuB,SAAS,GAAG,YAAY,GAAG9B,UAAU,CAACE,IAAI;IAC9CJ,oBAAoB,CAACC,KAAK,EAAE;MAC1BG,IAAI,EAAEF,UAAU,CAACE,IAAI;MACrB6B,MAAM,EAAE,aAAa,GAAGD,SAAS,GAAG,GAAG,GAAG9B,UAAU,CAAC+B,MAAM,GAAG;IAChE,CAAC,CAAC;IACF,OAAOD,SAAS;EAClB,CAAC;EAED,IAAIE,YAAY,GAAG,SAASA,YAAYA,CAAA,EAAG;IACzC,KAAK,IAAIC,KAAK,GAAGX,SAAS,CAACV,MAAM,EAAEW,IAAI,GAAG,IAAIC,KAAK,CAACS,KAAK,CAAC,EAAEC,KAAK,GAAG,CAAC,EAAEA,KAAK,GAAGD,KAAK,EAAEC,KAAK,EAAE,EAAE;MAC7FX,IAAI,CAACW,KAAK,CAAC,GAAGZ,SAAS,CAACY,KAAK,CAAC;IAChC;IAEA,IAAIlC,UAAU,GAAGL,eAAe,CAAC4B,IAAI,EAAExB,KAAK,CAACQ,UAAU,CAAC;IACxDT,oBAAoB,CAACC,KAAK,EAAEC,UAAU,CAAC;EACzC,CAAC;EAED,IAAImC,EAAE,GAAG,SAASA,EAAEA,CAAA,EAAG;IACrB,KAAK,IAAIC,KAAK,GAAGd,SAAS,CAACV,MAAM,EAAEW,IAAI,GAAG,IAAIC,KAAK,CAACY,KAAK,CAAC,EAAEC,KAAK,GAAG,CAAC,EAAEA,KAAK,GAAGD,KAAK,EAAEC,KAAK,EAAE,EAAE;MAC7Fd,IAAI,CAACc,KAAK,CAAC,GAAGf,SAAS,CAACe,KAAK,CAAC;IAChC;IAEA,OAAO/B,KAAK,CAACP,KAAK,CAACQ,UAAU,EAAEC,GAAG,EAAE8B,UAAU,CAACf,IAAI,CAAC,CAAC;EACvD,CAAC;EAED,OAAO;IACLf,GAAG,EAAEA,GAAG;IACR2B,EAAE,EAAEA,EAAE;IACNH,YAAY,EAAEA,YAAY;IAC1BL,SAAS,EAAEA,SAAS;IACpBY,OAAO,EAAE,SAASA,OAAOA,CAACC,GAAG,EAAE;MAC7BA,GAAG,CAACC,OAAO,CAAC,UAAUf,GAAG,EAAE;QACzB3B,KAAK,CAACE,QAAQ,CAACyB,GAAG,CAAC,GAAG,IAAI;MAC5B,CAAC,CAAC;IACJ,CAAC;IACDgB,KAAK,EAAE,SAASA,KAAKA,CAAA,EAAG;MACtB3C,KAAK,CAACQ,UAAU,GAAG,CAAC,CAAC;MACrBR,KAAK,CAACE,QAAQ,GAAG,CAAC,CAAC;MACnBF,KAAK,CAACM,KAAK,CAACqC,KAAK,CAAC,CAAC;IACrB,CAAC;IACDrC,KAAK,EAAEN,KAAK,CAACM,KAAK;IAClBN,KAAK,EAAEA,KAAK;IACZH,mBAAmB,EAAEA,mBAAmB,CAAC+C,IAAI,CAAC,IAAI,EAAE5C,KAAK,CAACQ,UAAU,CAAC;IACrED,KAAK,EAAEA,KAAK,CAACqC,IAAI,CAAC,IAAI,EAAE5C,KAAK,CAACQ,UAAU,EAAEC,GAAG;EAC/C,CAAC;AACH,CAAC;AAED,IAAI8B,UAAU,GAAG,SAASA,UAAUA,CAACf,IAAI,EAAE;EACzC,IAAIqB,GAAG,GAAG,EAAE;EAEZ,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGtB,IAAI,CAACX,MAAM,EAAEiC,CAAC,EAAE,EAAE;IACpC,IAAIC,GAAG,GAAGvB,IAAI,CAACsB,CAAC,CAAC;IACjB,IAAIC,GAAG,IAAI,IAAI,EAAE;IACjB,IAAIC,KAAK,GAAG,KAAK,CAAC;IAElB,QAAQ,OAAOD,GAAG;MAChB,KAAK,SAAS;QACZ;MAEF,KAAK,QAAQ;QACX;UACE,IAAItB,KAAK,CAACwB,OAAO,CAACF,GAAG,CAAC,EAAE;YACtBC,KAAK,GAAGT,UAAU,CAACQ,GAAG,CAAC;UACzB,CAAC,MAAM;YACLC,KAAK,GAAG,EAAE;YAEV,KAAK,IAAIE,CAAC,IAAIH,GAAG,EAAE;cACjB,IAAIA,GAAG,CAACG,CAAC,CAAC,IAAIA,CAAC,EAAE;gBACfF,KAAK,KAAKA,KAAK,IAAI,GAAG,CAAC;gBACvBA,KAAK,IAAIE,CAAC;cACZ;YACF;UACF;UAEA;QACF;MAEF;QACE;UACEF,KAAK,GAAGD,GAAG;QACb;IACJ;IAEA,IAAIC,KAAK,EAAE;MACTH,GAAG,KAAKA,GAAG,IAAI,GAAG,CAAC;MACnBA,GAAG,IAAIG,KAAK;IACd;EACF;EAEA,OAAOH,GAAG;AACZ,CAAC;AAED,SAAS/B,aAAa,IAAIqC,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}