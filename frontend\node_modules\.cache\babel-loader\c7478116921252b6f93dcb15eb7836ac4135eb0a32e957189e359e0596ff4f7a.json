{"ast": null, "code": "var _jsxFileName = \"D:\\\\Documents_2\\\\New Coding\\\\CoderBuddy\\\\CoderBuddy\\\\frontend\\\\src\\\\components\\\\SaveDiffModal.js\",\n  _s = $RefreshSig$();\nimport React from 'react';\nimport NiceModal, { useModal } from '@ebay/nice-modal-react';\nimport DiffViewer from 'react-diff-viewer-continued';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nfunction SaveDiffModal({\n  oldText,\n  newText,\n  filePath\n}) {\n  _s();\n  const modal = useModal();\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    style: {\n      position: 'fixed',\n      inset: 0,\n      background: 'rgba(0,0,0,0.4)',\n      display: 'flex',\n      alignItems: 'center',\n      justifyContent: 'center',\n      zIndex: 1000\n    },\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        padding: 16,\n        background: '#fff',\n        width: '80vw',\n        maxHeight: '80vh',\n        overflow: 'auto',\n        borderRadius: 4\n      },\n      children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n        style: {\n          marginTop: 0\n        },\n        children: [\"Confirm Save \\u2013 \", filePath]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 29,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(DiffViewer, {\n        oldValue: oldText,\n        newValue: newText,\n        splitView: true,\n        showDiffOnly: false,\n        styles: {\n          variables: {\n            light: {\n              diffViewerBackground: '#f7f7f7'\n            }\n          }\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 30,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          marginTop: 16,\n          textAlign: 'right'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: () => modal.resolve(true),\n          style: {\n            marginRight: 8\n          },\n          children: \"Save\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 44,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: () => modal.remove(),\n          children: \"Cancel\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 47,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 43,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 19,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 8,\n    columnNumber: 5\n  }, this);\n}\n_s(SaveDiffModal, \"hDyDP9waVoSwitriRQZZW/zZCNw=\", false, function () {\n  return [useModal];\n});\n_c = SaveDiffModal;\nexport default _c2 = NiceModal.create(SaveDiffModal);\nvar _c, _c2;\n$RefreshReg$(_c, \"SaveDiffModal\");\n$RefreshReg$(_c2, \"%default%\");", "map": {"version": 3, "names": ["React", "NiceModal", "useModal", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "jsxDEV", "_jsxDEV", "SaveDiffModal", "oldText", "newText", "filePath", "_s", "modal", "style", "position", "inset", "background", "display", "alignItems", "justifyContent", "zIndex", "children", "padding", "width", "maxHeight", "overflow", "borderRadius", "marginTop", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "oldValue", "newValue", "splitView", "showDiffOnly", "styles", "variables", "light", "diffViewerBackground", "textAlign", "onClick", "resolve", "marginRight", "remove", "_c", "_c2", "create", "$RefreshReg$"], "sources": ["D:/Documents_2/New Coding/CoderBuddy/CoderBuddy/frontend/src/components/SaveDiffModal.js"], "sourcesContent": ["import React from 'react';\r\nimport NiceModal, { useModal } from '@ebay/nice-modal-react';\r\nimport Diff<PERSON>ie<PERSON> from 'react-diff-viewer-continued';\r\n\r\nfunction SaveDiffModal({ oldText, newText, filePath }) {\r\n  const modal = useModal();\r\n  return (\r\n    <div\r\n      style={{\r\n        position: 'fixed',\r\n        inset: 0,\r\n        background: 'rgba(0,0,0,0.4)',\r\n        display: 'flex',\r\n        alignItems: 'center',\r\n        justifyContent: 'center',\r\n        zIndex: 1000,\r\n      }}\r\n    >\r\n      <div\r\n        style={{\r\n          padding: 16,\r\n          background: '#fff',\r\n          width: '80vw',\r\n          maxHeight: '80vh',\r\n          overflow: 'auto',\r\n          borderRadius: 4,\r\n        }}\r\n      >\r\n        <h3 style={{ marginTop: 0 }}>Confirm Save – {filePath}</h3>\r\n        <DiffViewer\r\n          oldValue={oldText}\r\n          newValue={newText}\r\n          splitView={true}\r\n          showDiffOnly={false}\r\n          styles={{\r\n            variables: {\r\n              light: {\r\n                diffViewerBackground: '#f7f7f7',\r\n              },\r\n            },\r\n          }}\r\n        />\r\n        <div style={{ marginTop: 16, textAlign: 'right' }}>\r\n          <button onClick={() => modal.resolve(true)} style={{ marginRight: 8 }}>\r\n            Save\r\n          </button>\r\n          <button onClick={() => modal.remove()}>Cancel</button>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  );\r\n}\r\n\r\nexport default NiceModal.create(SaveDiffModal); "], "mappings": ";;AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,OAAOC,SAAS,IAAIC,QAAQ,QAAQ,wBAAwB;AAC5D,OAAOC,UAAU,MAAM,6BAA6B;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAErD,SAASC,aAAaA,CAAC;EAAEC,OAAO;EAAEC,OAAO;EAAEC;AAAS,CAAC,EAAE;EAAAC,EAAA;EACrD,MAAMC,KAAK,GAAGT,QAAQ,CAAC,CAAC;EACxB,oBACEG,OAAA;IACEO,KAAK,EAAE;MACLC,QAAQ,EAAE,OAAO;MACjBC,KAAK,EAAE,CAAC;MACRC,UAAU,EAAE,iBAAiB;MAC7BC,OAAO,EAAE,MAAM;MACfC,UAAU,EAAE,QAAQ;MACpBC,cAAc,EAAE,QAAQ;MACxBC,MAAM,EAAE;IACV,CAAE;IAAAC,QAAA,eAEFf,OAAA;MACEO,KAAK,EAAE;QACLS,OAAO,EAAE,EAAE;QACXN,UAAU,EAAE,MAAM;QAClBO,KAAK,EAAE,MAAM;QACbC,SAAS,EAAE,MAAM;QACjBC,QAAQ,EAAE,MAAM;QAChBC,YAAY,EAAE;MAChB,CAAE;MAAAL,QAAA,gBAEFf,OAAA;QAAIO,KAAK,EAAE;UAAEc,SAAS,EAAE;QAAE,CAAE;QAAAN,QAAA,GAAC,sBAAe,EAACX,QAAQ;MAAA;QAAAkB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK,CAAC,eAC3DzB,OAAA,CAACF,UAAU;QACT4B,QAAQ,EAAExB,OAAQ;QAClByB,QAAQ,EAAExB,OAAQ;QAClByB,SAAS,EAAE,IAAK;QAChBC,YAAY,EAAE,KAAM;QACpBC,MAAM,EAAE;UACNC,SAAS,EAAE;YACTC,KAAK,EAAE;cACLC,oBAAoB,EAAE;YACxB;UACF;QACF;MAAE;QAAAX,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eACFzB,OAAA;QAAKO,KAAK,EAAE;UAAEc,SAAS,EAAE,EAAE;UAAEa,SAAS,EAAE;QAAQ,CAAE;QAAAnB,QAAA,gBAChDf,OAAA;UAAQmC,OAAO,EAAEA,CAAA,KAAM7B,KAAK,CAAC8B,OAAO,CAAC,IAAI,CAAE;UAAC7B,KAAK,EAAE;YAAE8B,WAAW,EAAE;UAAE,CAAE;UAAAtB,QAAA,EAAC;QAEvE;UAAAO,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eACTzB,OAAA;UAAQmC,OAAO,EAAEA,CAAA,KAAM7B,KAAK,CAACgC,MAAM,CAAC,CAAE;UAAAvB,QAAA,EAAC;QAAM;UAAAO,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACnD,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV;AAACpB,EAAA,CA/CQJ,aAAa;EAAA,QACNJ,QAAQ;AAAA;AAAA0C,EAAA,GADftC,aAAa;AAiDtB,eAAAuC,GAAA,GAAe5C,SAAS,CAAC6C,MAAM,CAACxC,aAAa,CAAC;AAAC,IAAAsC,EAAA,EAAAC,GAAA;AAAAE,YAAA,CAAAH,EAAA;AAAAG,YAAA,CAAAF,GAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}