{"ast": null, "code": "const BASE_URL = process.env.REACT_APP_BACKEND_URL || 'http://localhost:8000';\nconst PROJECT_ID = 'demo'; // temporary default\n\nasync function request(url, options = {}) {\n  const res = await fetch(url, options);\n  if (!res.ok) {\n    const data = await res.json().catch(() => ({}));\n    throw new Error(data.detail || `Request failed: ${res.status}`);\n  }\n  return res.json();\n}\nexport async function listDir(path = '') {\n  const url = `${BASE_URL}/api/projects/${PROJECT_ID}/list/${encodeURIComponent(path)}`;\n  return request(url);\n}\nexport async function readFile(path) {\n  const url = `${BASE_URL}/api/projects/${PROJECT_ID}/files/${encodeURIComponent(path)}`;\n  return request(url);\n}\nexport async function writeFile(path, content, approved = true) {\n  const url = `${BASE_URL}/api/projects/${PROJECT_ID}/files/${encodeURIComponent(path)}`;\n  return request(url, {\n    method: 'PUT',\n    headers: {\n      'Content-Type': 'application/json'\n    },\n    body: JSON.stringify({\n      content,\n      approved\n    })\n  });\n}", "map": {"version": 3, "names": ["BASE_URL", "process", "env", "REACT_APP_BACKEND_URL", "PROJECT_ID", "request", "url", "options", "res", "fetch", "ok", "data", "json", "catch", "Error", "detail", "status", "listDir", "path", "encodeURIComponent", "readFile", "writeFile", "content", "approved", "method", "headers", "body", "JSON", "stringify"], "sources": ["D:/Documents_2/New Coding/CoderBuddy/CoderBuddy/frontend/src/services/api.js"], "sourcesContent": ["const BASE_URL = process.env.REACT_APP_BACKEND_URL || 'http://localhost:8000';\r\nconst PROJECT_ID = 'demo'; // temporary default\r\n\r\nasync function request(url, options = {}) {\r\n  const res = await fetch(url, options);\r\n  if (!res.ok) {\r\n    const data = await res.json().catch(() => ({}));\r\n    throw new Error(data.detail || `Request failed: ${res.status}`);\r\n  }\r\n  return res.json();\r\n}\r\n\r\nexport async function listDir(path = '') {\r\n  const url = `${BASE_URL}/api/projects/${PROJECT_ID}/list/${encodeURIComponent(path)}`;\r\n  return request(url);\r\n}\r\n\r\nexport async function readFile(path) {\r\n  const url = `${BASE_URL}/api/projects/${PROJECT_ID}/files/${encodeURIComponent(path)}`;\r\n  return request(url);\r\n}\r\n\r\nexport async function writeFile(path, content, approved = true) {\r\n  const url = `${BASE_URL}/api/projects/${PROJECT_ID}/files/${encodeURIComponent(path)}`;\r\n  return request(url, {\r\n    method: 'PUT',\r\n    headers: {\r\n      'Content-Type': 'application/json',\r\n    },\r\n    body: JSON.stringify({ content, approved }),\r\n  });\r\n} "], "mappings": "AAAA,MAAMA,QAAQ,GAAGC,OAAO,CAACC,GAAG,CAACC,qBAAqB,IAAI,uBAAuB;AAC7E,MAAMC,UAAU,GAAG,MAAM,CAAC,CAAC;;AAE3B,eAAeC,OAAOA,CAACC,GAAG,EAAEC,OAAO,GAAG,CAAC,CAAC,EAAE;EACxC,MAAMC,GAAG,GAAG,MAAMC,KAAK,CAACH,GAAG,EAAEC,OAAO,CAAC;EACrC,IAAI,CAACC,GAAG,CAACE,EAAE,EAAE;IACX,MAAMC,IAAI,GAAG,MAAMH,GAAG,CAACI,IAAI,CAAC,CAAC,CAACC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;IAC/C,MAAM,IAAIC,KAAK,CAACH,IAAI,CAACI,MAAM,IAAI,mBAAmBP,GAAG,CAACQ,MAAM,EAAE,CAAC;EACjE;EACA,OAAOR,GAAG,CAACI,IAAI,CAAC,CAAC;AACnB;AAEA,OAAO,eAAeK,OAAOA,CAACC,IAAI,GAAG,EAAE,EAAE;EACvC,MAAMZ,GAAG,GAAG,GAAGN,QAAQ,iBAAiBI,UAAU,SAASe,kBAAkB,CAACD,IAAI,CAAC,EAAE;EACrF,OAAOb,OAAO,CAACC,GAAG,CAAC;AACrB;AAEA,OAAO,eAAec,QAAQA,CAACF,IAAI,EAAE;EACnC,MAAMZ,GAAG,GAAG,GAAGN,QAAQ,iBAAiBI,UAAU,UAAUe,kBAAkB,CAACD,IAAI,CAAC,EAAE;EACtF,OAAOb,OAAO,CAACC,GAAG,CAAC;AACrB;AAEA,OAAO,eAAee,SAASA,CAACH,IAAI,EAAEI,OAAO,EAAEC,QAAQ,GAAG,IAAI,EAAE;EAC9D,MAAMjB,GAAG,GAAG,GAAGN,QAAQ,iBAAiBI,UAAU,UAAUe,kBAAkB,CAACD,IAAI,CAAC,EAAE;EACtF,OAAOb,OAAO,CAACC,GAAG,EAAE;IAClBkB,MAAM,EAAE,KAAK;IACbC,OAAO,EAAE;MACP,cAAc,EAAE;IAClB,CAAC;IACDC,IAAI,EAAEC,IAAI,CAACC,SAAS,CAAC;MAAEN,OAAO;MAAEC;IAAS,CAAC;EAC5C,CAAC,CAAC;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}