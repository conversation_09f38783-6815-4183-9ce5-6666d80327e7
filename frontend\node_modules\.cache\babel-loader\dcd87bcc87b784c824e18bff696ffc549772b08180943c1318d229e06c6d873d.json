{"ast": null, "code": "const BASE_URL = process.env.REACT_APP_BACKEND_URL || 'http://localhost:8000';\nconst PROJECT_ID = 'demo'; // temporary default\n\nasync function request(url, options = {}) {\n  const res = await fetch(url, options);\n  if (!res.ok) {\n    const data = await res.json().catch(() => ({}));\n    throw new Error(data.detail || `Request failed: ${res.status}`);\n  }\n  return res.json();\n}\nexport async function listDir(path = '', projectId = 'demo') {\n  const url = `${BASE_URL}/api/projects/${projectId}/list/${encodeURIComponent(path)}`;\n  return request(url);\n}\nexport async function readFile(path) {\n  const url = `${BASE_URL}/api/projects/${PROJECT_ID}/files/${encodeURIComponent(path)}`;\n  return request(url);\n}\nexport async function writeFile(path, content, approved = true) {\n  const url = `${BASE_URL}/api/projects/${PROJECT_ID}/files/${encodeURIComponent(path)}`;\n  return request(url, {\n    method: 'PUT',\n    headers: {\n      'Content-Type': 'application/json'\n    },\n    body: JSON.stringify({\n      content,\n      approved\n    })\n  });\n}\nexport async function runCommand(command) {\n  const url = `${BASE_URL}/api/terminal/run`;\n  return request(url, {\n    method: 'POST',\n    headers: {\n      'Content-Type': 'application/json'\n    },\n    body: JSON.stringify({\n      command\n    })\n  });\n}\nexport async function listProjects() {\n  const url = `${BASE_URL}/api/projects/list`;\n  return request(url);\n}\nexport async function createProject(project_id) {\n  const url = `${BASE_URL}/api/projects/create`;\n  return request(url, {\n    method: 'POST',\n    headers: {\n      'Content-Type': 'application/json'\n    },\n    body: JSON.stringify({\n      project_id\n    })\n  });\n}", "map": {"version": 3, "names": ["BASE_URL", "process", "env", "REACT_APP_BACKEND_URL", "PROJECT_ID", "request", "url", "options", "res", "fetch", "ok", "data", "json", "catch", "Error", "detail", "status", "listDir", "path", "projectId", "encodeURIComponent", "readFile", "writeFile", "content", "approved", "method", "headers", "body", "JSON", "stringify", "runCommand", "command", "listProjects", "createProject", "project_id"], "sources": ["D:/Documents_2/New Coding/CoderBuddy/CoderBuddy/frontend/src/services/api.js"], "sourcesContent": ["const BASE_URL = process.env.REACT_APP_BACKEND_URL || 'http://localhost:8000';\r\nconst PROJECT_ID = 'demo'; // temporary default\r\n\r\nasync function request(url, options = {}) {\r\n  const res = await fetch(url, options);\r\n  if (!res.ok) {\r\n    const data = await res.json().catch(() => ({}));\r\n    throw new Error(data.detail || `Request failed: ${res.status}`);\r\n  }\r\n  return res.json();\r\n}\r\n\r\nexport async function listDir(path = '', projectId = 'demo') {\r\n  const url = `${BASE_URL}/api/projects/${projectId}/list/${encodeURIComponent(path)}`;\r\n  return request(url);\r\n}\r\n\r\nexport async function readFile(path) {\r\n  const url = `${BASE_URL}/api/projects/${PROJECT_ID}/files/${encodeURIComponent(path)}`;\r\n  return request(url);\r\n}\r\n\r\nexport async function writeFile(path, content, approved = true) {\r\n  const url = `${BASE_URL}/api/projects/${PROJECT_ID}/files/${encodeURIComponent(path)}`;\r\n  return request(url, {\r\n    method: 'PUT',\r\n    headers: {\r\n      'Content-Type': 'application/json',\r\n    },\r\n    body: JSON.stringify({ content, approved }),\r\n  });\r\n}\r\n\r\nexport async function runCommand(command) {\r\n  const url = `${BASE_URL}/api/terminal/run`;\r\n  return request(url, {\r\n    method: 'POST',\r\n    headers: {\r\n      'Content-Type': 'application/json',\r\n    },\r\n    body: JSON.stringify({ command }),\r\n  });\r\n}\r\n\r\nexport async function listProjects() {\r\n  const url = `${BASE_URL}/api/projects/list`;\r\n  return request(url);\r\n}\r\n\r\nexport async function createProject(project_id) {\r\n  const url = `${BASE_URL}/api/projects/create`;\r\n  return request(url, {\r\n    method: 'POST',\r\n    headers: { 'Content-Type': 'application/json' },\r\n    body: JSON.stringify({ project_id }),\r\n  });\r\n} "], "mappings": "AAAA,MAAMA,QAAQ,GAAGC,OAAO,CAACC,GAAG,CAACC,qBAAqB,IAAI,uBAAuB;AAC7E,MAAMC,UAAU,GAAG,MAAM,CAAC,CAAC;;AAE3B,eAAeC,OAAOA,CAACC,GAAG,EAAEC,OAAO,GAAG,CAAC,CAAC,EAAE;EACxC,MAAMC,GAAG,GAAG,MAAMC,KAAK,CAACH,GAAG,EAAEC,OAAO,CAAC;EACrC,IAAI,CAACC,GAAG,CAACE,EAAE,EAAE;IACX,MAAMC,IAAI,GAAG,MAAMH,GAAG,CAACI,IAAI,CAAC,CAAC,CAACC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;IAC/C,MAAM,IAAIC,KAAK,CAACH,IAAI,CAACI,MAAM,IAAI,mBAAmBP,GAAG,CAACQ,MAAM,EAAE,CAAC;EACjE;EACA,OAAOR,GAAG,CAACI,IAAI,CAAC,CAAC;AACnB;AAEA,OAAO,eAAeK,OAAOA,CAACC,IAAI,GAAG,EAAE,EAAEC,SAAS,GAAG,MAAM,EAAE;EAC3D,MAAMb,GAAG,GAAG,GAAGN,QAAQ,iBAAiBmB,SAAS,SAASC,kBAAkB,CAACF,IAAI,CAAC,EAAE;EACpF,OAAOb,OAAO,CAACC,GAAG,CAAC;AACrB;AAEA,OAAO,eAAee,QAAQA,CAACH,IAAI,EAAE;EACnC,MAAMZ,GAAG,GAAG,GAAGN,QAAQ,iBAAiBI,UAAU,UAAUgB,kBAAkB,CAACF,IAAI,CAAC,EAAE;EACtF,OAAOb,OAAO,CAACC,GAAG,CAAC;AACrB;AAEA,OAAO,eAAegB,SAASA,CAACJ,IAAI,EAAEK,OAAO,EAAEC,QAAQ,GAAG,IAAI,EAAE;EAC9D,MAAMlB,GAAG,GAAG,GAAGN,QAAQ,iBAAiBI,UAAU,UAAUgB,kBAAkB,CAACF,IAAI,CAAC,EAAE;EACtF,OAAOb,OAAO,CAACC,GAAG,EAAE;IAClBmB,MAAM,EAAE,KAAK;IACbC,OAAO,EAAE;MACP,cAAc,EAAE;IAClB,CAAC;IACDC,IAAI,EAAEC,IAAI,CAACC,SAAS,CAAC;MAAEN,OAAO;MAAEC;IAAS,CAAC;EAC5C,CAAC,CAAC;AACJ;AAEA,OAAO,eAAeM,UAAUA,CAACC,OAAO,EAAE;EACxC,MAAMzB,GAAG,GAAG,GAAGN,QAAQ,mBAAmB;EAC1C,OAAOK,OAAO,CAACC,GAAG,EAAE;IAClBmB,MAAM,EAAE,MAAM;IACdC,OAAO,EAAE;MACP,cAAc,EAAE;IAClB,CAAC;IACDC,IAAI,EAAEC,IAAI,CAACC,SAAS,CAAC;MAAEE;IAAQ,CAAC;EAClC,CAAC,CAAC;AACJ;AAEA,OAAO,eAAeC,YAAYA,CAAA,EAAG;EACnC,MAAM1B,GAAG,GAAG,GAAGN,QAAQ,oBAAoB;EAC3C,OAAOK,OAAO,CAACC,GAAG,CAAC;AACrB;AAEA,OAAO,eAAe2B,aAAaA,CAACC,UAAU,EAAE;EAC9C,MAAM5B,GAAG,GAAG,GAAGN,QAAQ,sBAAsB;EAC7C,OAAOK,OAAO,CAACC,GAAG,EAAE;IAClBmB,MAAM,EAAE,MAAM;IACdC,OAAO,EAAE;MAAE,cAAc,EAAE;IAAmB,CAAC;IAC/CC,IAAI,EAAEC,IAAI,CAACC,SAAS,CAAC;MAAEK;IAAW,CAAC;EACrC,CAAC,CAAC;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}