{"ast": null, "code": "import { jsx as _jsx, jsxs as _jsxs, Fragment as _Fragment } from \"react/jsx-runtime\";\nimport cn from \"classnames\";\nimport * as React from \"react\";\nimport memoize from \"memoize-one\";\nimport { computeHiddenBlocks } from \"./compute-hidden-blocks.js\";\nimport { DiffMethod, DiffType, computeLineInformation } from \"./compute-lines.js\";\nimport { Expand } from \"./expand.js\";\nimport computeStyles from \"./styles.js\";\nimport { Fold } from \"./fold.js\";\nexport var LineNumberPrefix;\n(function (LineNumberPrefix) {\n  LineNumberPrefix[\"LEFT\"] = \"L\";\n  LineNumberPrefix[\"RIGHT\"] = \"R\";\n})(LineNumberPrefix || (LineNumberPrefix = {}));\nclass DiffViewer extends React.Component {\n  styles;\n  static defaultProps = {\n    oldValue: \"\",\n    newValue: \"\",\n    splitView: true,\n    highlightLines: [],\n    disableWordDiff: false,\n    compareMethod: DiffMethod.CHARS,\n    styles: {},\n    hideLineNumbers: false,\n    extraLinesSurroundingDiff: 3,\n    showDiffOnly: true,\n    useDarkTheme: false,\n    linesOffset: 0,\n    nonce: \"\"\n  };\n  constructor(props) {\n    super(props);\n    this.state = {\n      expandedBlocks: [],\n      noSelect: undefined\n    };\n  }\n  /**\n   * Resets code block expand to the initial stage. Will be exposed to the parent component via\n   * refs.\n   */\n  resetCodeBlocks = () => {\n    if (this.state.expandedBlocks.length > 0) {\n      this.setState({\n        expandedBlocks: []\n      });\n      return true;\n    }\n    return false;\n  };\n  /**\n   * Pushes the target expanded code block to the state. During the re-render,\n   * this value is used to expand/fold unmodified code.\n   */\n  onBlockExpand = id => {\n    const prevState = this.state.expandedBlocks.slice();\n    prevState.push(id);\n    this.setState({\n      expandedBlocks: prevState\n    });\n  };\n  /**\n   * Computes final styles for the diff viewer. It combines the default styles with the user\n   * supplied overrides. The computed styles are cached with performance in mind.\n   *\n   * @param styles User supplied style overrides.\n   */\n  computeStyles = memoize(computeStyles);\n  /**\n   * Returns a function with clicked line number in the closure. Returns an no-op function when no\n   * onLineNumberClick handler is supplied.\n   *\n   * @param id Line id of a line.\n   */\n  onLineNumberClickProxy = id => {\n    if (this.props.onLineNumberClick) {\n      return e => this.props.onLineNumberClick(id, e);\n    }\n    return () => {};\n  };\n  /**\n   * Maps over the word diff and constructs the required React elements to show word diff.\n   *\n   * @param diffArray Word diff information derived from line information.\n   * @param renderer Optional renderer to format diff words. Useful for syntax highlighting.\n   */\n  renderWordDiff = (diffArray, renderer) => {\n    return diffArray.map((wordDiff, i) => {\n      const content = renderer ? renderer(wordDiff.value) : typeof wordDiff.value === 'string' ? wordDiff.value\n      // If wordDiff.value is DiffInformation, we don't handle it, unclear why. See c0c99f5712.\n      : undefined;\n      return wordDiff.type === DiffType.ADDED ? _jsx(\"ins\", {\n        className: cn(this.styles.wordDiff, {\n          [this.styles.wordAdded]: wordDiff.type === DiffType.ADDED\n        }),\n        children: content\n      }, i) : wordDiff.type === DiffType.REMOVED ? _jsx(\"del\", {\n        className: cn(this.styles.wordDiff, {\n          [this.styles.wordRemoved]: wordDiff.type === DiffType.REMOVED\n        }),\n        children: content\n      }, i) : _jsx(\"span\", {\n        className: cn(this.styles.wordDiff),\n        children: content\n      }, i);\n    });\n  };\n  /**\n   * Maps over the line diff and constructs the required react elements to show line diff. It calls\n   * renderWordDiff when encountering word diff. This takes care of both inline and split view line\n   * renders.\n   *\n   * @param lineNumber Line number of the current line.\n   * @param type Type of diff of the current line.\n   * @param prefix Unique id to prefix with the line numbers.\n   * @param value Content of the line. It can be a string or a word diff array.\n   * @param additionalLineNumber Additional line number to be shown. Useful for rendering inline\n   *  diff view. Right line number will be passed as additionalLineNumber.\n   * @param additionalPrefix Similar to prefix but for additional line number.\n   */\n  renderLine = (lineNumber, type, prefix, value, additionalLineNumber, additionalPrefix) => {\n    const lineNumberTemplate = `${prefix}-${lineNumber}`;\n    const additionalLineNumberTemplate = `${additionalPrefix}-${additionalLineNumber}`;\n    const highlightLine = this.props.highlightLines.includes(lineNumberTemplate) || this.props.highlightLines.includes(additionalLineNumberTemplate);\n    const added = type === DiffType.ADDED;\n    const removed = type === DiffType.REMOVED;\n    const changed = type === DiffType.CHANGED;\n    let content;\n    const hasWordDiff = Array.isArray(value);\n    if (hasWordDiff) {\n      content = this.renderWordDiff(value, this.props.renderContent);\n    } else if (this.props.renderContent) {\n      content = this.props.renderContent(value);\n    } else {\n      content = value;\n    }\n    let ElementType = \"div\";\n    if (added && !hasWordDiff) {\n      ElementType = \"ins\";\n    } else if (removed && !hasWordDiff) {\n      ElementType = \"del\";\n    }\n    return _jsxs(_Fragment, {\n      children: [!this.props.hideLineNumbers && _jsx(\"td\", {\n        onClick: lineNumber && this.onLineNumberClickProxy(lineNumberTemplate),\n        className: cn(this.styles.gutter, {\n          [this.styles.emptyGutter]: !lineNumber,\n          [this.styles.diffAdded]: added,\n          [this.styles.diffRemoved]: removed,\n          [this.styles.diffChanged]: changed,\n          [this.styles.highlightedGutter]: highlightLine\n        }),\n        children: _jsx(\"pre\", {\n          className: this.styles.lineNumber,\n          children: lineNumber\n        })\n      }), !this.props.splitView && !this.props.hideLineNumbers && _jsx(\"td\", {\n        onClick: additionalLineNumber && this.onLineNumberClickProxy(additionalLineNumberTemplate),\n        className: cn(this.styles.gutter, {\n          [this.styles.emptyGutter]: !additionalLineNumber,\n          [this.styles.diffAdded]: added,\n          [this.styles.diffRemoved]: removed,\n          [this.styles.diffChanged]: changed,\n          [this.styles.highlightedGutter]: highlightLine\n        }),\n        children: _jsx(\"pre\", {\n          className: this.styles.lineNumber,\n          children: additionalLineNumber\n        })\n      }), this.props.renderGutter ? this.props.renderGutter({\n        lineNumber,\n        type,\n        prefix,\n        value,\n        additionalLineNumber,\n        additionalPrefix,\n        styles: this.styles\n      }) : null, _jsx(\"td\", {\n        className: cn(this.styles.marker, {\n          [this.styles.emptyLine]: !content,\n          [this.styles.diffAdded]: added,\n          [this.styles.diffRemoved]: removed,\n          [this.styles.diffChanged]: changed,\n          [this.styles.highlightedLine]: highlightLine\n        }),\n        children: _jsxs(\"pre\", {\n          children: [added && \"+\", removed && \"-\"]\n        })\n      }), _jsx(\"td\", {\n        className: cn(this.styles.content, {\n          [this.styles.emptyLine]: !content,\n          [this.styles.diffAdded]: added,\n          [this.styles.diffRemoved]: removed,\n          [this.styles.diffChanged]: changed,\n          [this.styles.highlightedLine]: highlightLine,\n          left: prefix === LineNumberPrefix.LEFT,\n          right: prefix === LineNumberPrefix.RIGHT\n        }),\n        onMouseDown: () => {\n          const elements = document.getElementsByClassName(prefix === LineNumberPrefix.LEFT ? \"right\" : \"left\");\n          for (let i = 0; i < elements.length; i++) {\n            const element = elements.item(i);\n            element.classList.add(this.styles.noSelect);\n          }\n        },\n        title: added && !hasWordDiff ? \"Added line\" : removed && !hasWordDiff ? \"Removed line\" : undefined,\n        children: _jsx(ElementType, {\n          className: this.styles.contentText,\n          children: content\n        })\n      })]\n    });\n  };\n  /**\n   * Generates lines for split view.\n   *\n   * @param obj Line diff information.\n   * @param obj.left Life diff information for the left pane of the split view.\n   * @param obj.right Life diff information for the right pane of the split view.\n   * @param index React key for the lines.\n   */\n  renderSplitView = ({\n    left,\n    right\n  }, index) => {\n    return _jsxs(\"tr\", {\n      className: this.styles.line,\n      children: [this.renderLine(left.lineNumber, left.type, LineNumberPrefix.LEFT, left.value), this.renderLine(right.lineNumber, right.type, LineNumberPrefix.RIGHT, right.value)]\n    }, index);\n  };\n  /**\n   * Generates lines for inline view.\n   *\n   * @param obj Line diff information.\n   * @param obj.left Life diff information for the added section of the inline view.\n   * @param obj.right Life diff information for the removed section of the inline view.\n   * @param index React key for the lines.\n   */\n  renderInlineView = ({\n    left,\n    right\n  }, index) => {\n    let content;\n    if (left.type === DiffType.REMOVED && right.type === DiffType.ADDED) {\n      return _jsxs(React.Fragment, {\n        children: [_jsx(\"tr\", {\n          className: this.styles.line,\n          children: this.renderLine(left.lineNumber, left.type, LineNumberPrefix.LEFT, left.value, null)\n        }), _jsx(\"tr\", {\n          className: this.styles.line,\n          children: this.renderLine(null, right.type, LineNumberPrefix.RIGHT, right.value, right.lineNumber, LineNumberPrefix.RIGHT)\n        })]\n      }, index);\n    }\n    if (left.type === DiffType.REMOVED) {\n      content = this.renderLine(left.lineNumber, left.type, LineNumberPrefix.LEFT, left.value, null);\n    }\n    if (left.type === DiffType.DEFAULT) {\n      content = this.renderLine(left.lineNumber, left.type, LineNumberPrefix.LEFT, left.value, right.lineNumber, LineNumberPrefix.RIGHT);\n    }\n    if (right.type === DiffType.ADDED) {\n      content = this.renderLine(null, right.type, LineNumberPrefix.RIGHT, right.value, right.lineNumber);\n    }\n    return _jsx(\"tr\", {\n      className: this.styles.line,\n      children: content\n    }, index);\n  };\n  /**\n   * Returns a function with clicked block number in the closure.\n   *\n   * @param id Cold fold block id.\n   */\n  onBlockClickProxy = id => () => this.onBlockExpand(id);\n  /**\n   * Generates cold fold block. It also uses the custom message renderer when available to show\n   * cold fold messages.\n   *\n   * @param num Number of skipped lines between two blocks.\n   * @param blockNumber Code fold block id.\n   * @param leftBlockLineNumber First left line number after the current code fold block.\n   * @param rightBlockLineNumber First right line number after the current code fold block.\n   */\n  renderSkippedLineIndicator = (num, blockNumber, leftBlockLineNumber, rightBlockLineNumber) => {\n    const {\n      hideLineNumbers,\n      splitView\n    } = this.props;\n    const message = this.props.codeFoldMessageRenderer ? this.props.codeFoldMessageRenderer(num, leftBlockLineNumber, rightBlockLineNumber) : _jsxs(\"span\", {\n      className: this.styles.codeFoldContent,\n      children: [\"Expand \", num, \" lines ...\"]\n    });\n    const content = _jsx(\"td\", {\n      className: this.styles.codeFoldContentContainer,\n      children: _jsx(\"button\", {\n        type: \"button\",\n        className: this.styles.codeFoldExpandButton,\n        onClick: this.onBlockClickProxy(blockNumber),\n        tabIndex: 0,\n        children: message\n      })\n    });\n    const isUnifiedViewWithoutLineNumbers = !splitView && !hideLineNumbers;\n    return _jsxs(\"tr\", {\n      className: this.styles.codeFold,\n      children: [!hideLineNumbers && _jsx(\"td\", {\n        className: this.styles.codeFoldGutter\n      }), this.props.renderGutter ? _jsx(\"td\", {\n        className: this.styles.codeFoldGutter\n      }) : null, _jsx(\"td\", {\n        className: cn({\n          [this.styles.codeFoldGutter]: isUnifiedViewWithoutLineNumbers\n        })\n      }), isUnifiedViewWithoutLineNumbers ? _jsxs(React.Fragment, {\n        children: [_jsx(\"td\", {}), content]\n      }) : _jsxs(React.Fragment, {\n        children: [content, this.props.renderGutter ? _jsx(\"td\", {}) : null, _jsx(\"td\", {}), _jsx(\"td\", {}), !hideLineNumbers ? _jsx(\"td\", {}) : null]\n      })]\n    }, `${leftBlockLineNumber}-${rightBlockLineNumber}`);\n  };\n  /**\n   * Generates the entire diff view.\n   */\n  renderDiff = () => {\n    const {\n      oldValue,\n      newValue,\n      splitView,\n      disableWordDiff,\n      compareMethod,\n      linesOffset\n    } = this.props;\n    const {\n      lineInformation,\n      diffLines\n    } = computeLineInformation(oldValue, newValue, disableWordDiff, compareMethod, linesOffset, this.props.alwaysShowLines);\n    const extraLines = this.props.extraLinesSurroundingDiff < 0 ? 0 : Math.round(this.props.extraLinesSurroundingDiff);\n    const {\n      lineBlocks,\n      blocks\n    } = computeHiddenBlocks(lineInformation, diffLines, extraLines);\n    const diffNodes = lineInformation.map((line, lineIndex) => {\n      if (this.props.showDiffOnly) {\n        const blockIndex = lineBlocks[lineIndex];\n        if (blockIndex !== undefined) {\n          const lastLineOfBlock = blocks[blockIndex].endLine === lineIndex;\n          if (!this.state.expandedBlocks.includes(blockIndex) && lastLineOfBlock) {\n            return _jsx(React.Fragment, {\n              children: this.renderSkippedLineIndicator(blocks[blockIndex].lines, blockIndex, line.left.lineNumber, line.right.lineNumber)\n            }, lineIndex);\n          }\n          if (!this.state.expandedBlocks.includes(blockIndex)) {\n            return null;\n          }\n        }\n      }\n      return splitView ? this.renderSplitView(line, lineIndex) : this.renderInlineView(line, lineIndex);\n    });\n    return {\n      diffNodes,\n      blocks,\n      lineInformation\n    };\n  };\n  render = () => {\n    const {\n      oldValue,\n      newValue,\n      useDarkTheme,\n      leftTitle,\n      rightTitle,\n      splitView,\n      compareMethod,\n      hideLineNumbers,\n      nonce\n    } = this.props;\n    if (typeof compareMethod === \"string\" && compareMethod !== DiffMethod.JSON) {\n      if (typeof oldValue !== \"string\" || typeof newValue !== \"string\") {\n        throw Error('\"oldValue\" and \"newValue\" should be strings');\n      }\n    }\n    this.styles = this.computeStyles(this.props.styles, useDarkTheme, nonce);\n    const nodes = this.renderDiff();\n    let colSpanOnSplitView = 3;\n    let colSpanOnInlineView = 4;\n    if (hideLineNumbers) {\n      colSpanOnSplitView -= 1;\n      colSpanOnInlineView -= 1;\n    }\n    if (this.props.renderGutter) {\n      colSpanOnSplitView += 1;\n      colSpanOnInlineView += 1;\n    }\n    let deletions = 0;\n    let additions = 0;\n    for (const l of nodes.lineInformation) {\n      if (l.left.type === DiffType.ADDED) {\n        additions++;\n      }\n      if (l.right.type === DiffType.ADDED) {\n        additions++;\n      }\n      if (l.left.type === DiffType.REMOVED) {\n        deletions++;\n      }\n      if (l.right.type === DiffType.REMOVED) {\n        deletions++;\n      }\n    }\n    const totalChanges = deletions + additions;\n    const percentageAddition = Math.round(additions / totalChanges * 100);\n    const blocks = [];\n    for (let i = 0; i < 5; i++) {\n      if (percentageAddition > i * 20) {\n        blocks.push(_jsx(\"span\", {\n          className: cn(this.styles.block, this.styles.blockAddition)\n        }, i));\n      } else {\n        blocks.push(_jsx(\"span\", {\n          className: cn(this.styles.block, this.styles.blockDeletion)\n        }, i));\n      }\n    }\n    const allExpanded = this.state.expandedBlocks.length === nodes.blocks.length;\n    return _jsxs(\"div\", {\n      children: [_jsxs(\"div\", {\n        className: this.styles.summary,\n        role: \"banner\",\n        children: [_jsx(\"button\", {\n          type: \"button\",\n          className: this.styles.allExpandButton,\n          onClick: () => {\n            this.setState({\n              expandedBlocks: allExpanded ? [] : nodes.blocks.map(b => b.index)\n            });\n          },\n          children: allExpanded ? _jsx(Fold, {}) : _jsx(Expand, {})\n        }), \" \", totalChanges, _jsx(\"div\", {\n          style: {\n            display: \"flex\",\n            gap: \"1px\"\n          },\n          children: blocks\n        }), this.props.summary ? _jsx(\"span\", {\n          children: this.props.summary\n        }) : null]\n      }), _jsx(\"table\", {\n        className: cn(this.styles.diffContainer, {\n          [this.styles.splitView]: splitView\n        }),\n        onMouseUp: () => {\n          const elements = document.getElementsByClassName(\"right\");\n          for (let i = 0; i < elements.length; i++) {\n            const element = elements.item(i);\n            element.classList.remove(this.styles.noSelect);\n          }\n          const elementsLeft = document.getElementsByClassName(\"left\");\n          for (let i = 0; i < elementsLeft.length; i++) {\n            const element = elementsLeft.item(i);\n            element.classList.remove(this.styles.noSelect);\n          }\n        },\n        children: _jsxs(\"tbody\", {\n          children: [_jsxs(\"tr\", {\n            children: [!this.props.hideLineNumbers ? _jsx(\"td\", {\n              width: \"50px\"\n            }) : null, !splitView && !this.props.hideLineNumbers ? _jsx(\"td\", {\n              width: \"50px\"\n            }) : null, this.props.renderGutter ? _jsx(\"td\", {\n              width: \"50px\"\n            }) : null, _jsx(\"td\", {\n              width: \"28px\"\n            }), _jsx(\"td\", {\n              width: \"100%\"\n            }), splitView ? _jsxs(_Fragment, {\n              children: [!this.props.hideLineNumbers ? _jsx(\"td\", {\n                width: \"50px\"\n              }) : null, this.props.renderGutter ? _jsx(\"td\", {\n                width: \"50px\"\n              }) : null, _jsx(\"td\", {\n                width: \"28px\"\n              }), _jsx(\"td\", {\n                width: \"100%\"\n              })]\n            }) : null]\n          }), leftTitle || rightTitle ? _jsxs(\"tr\", {\n            children: [_jsx(\"th\", {\n              colSpan: splitView ? colSpanOnSplitView : colSpanOnInlineView,\n              className: cn(this.styles.titleBlock, this.styles.column),\n              children: leftTitle ? _jsx(\"pre\", {\n                className: this.styles.contentText,\n                children: leftTitle\n              }) : null\n            }), splitView ? _jsx(\"th\", {\n              colSpan: colSpanOnSplitView,\n              className: cn(this.styles.titleBlock, this.styles.column),\n              children: rightTitle ? _jsx(\"pre\", {\n                className: this.styles.contentText,\n                children: rightTitle\n              }) : null\n            }) : null]\n          }) : null, nodes.diffNodes]\n        })\n      })]\n    });\n  };\n}\nexport default DiffViewer;\nexport { DiffMethod };", "map": {"version": 3, "names": ["jsx", "_jsx", "jsxs", "_jsxs", "Fragment", "_Fragment", "cn", "React", "memoize", "computeHiddenBlocks", "DiffMethod", "DiffType", "computeLineInformation", "Expand", "computeStyles", "Fold", "LineNumberPrefix", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Component", "styles", "defaultProps", "oldValue", "newValue", "splitView", "highlightLines", "disable<PERSON><PERSON><PERSON><PERSON>", "compareMethod", "CHARS", "hideLineNumbers", "extraLinesSurroundingDiff", "showDiffOnly", "useDarkTheme", "linesOffset", "nonce", "constructor", "props", "state", "expandedBlocks", "noSelect", "undefined", "resetCodeBlocks", "length", "setState", "onBlockExpand", "id", "prevState", "slice", "push", "onLineNumberClickProxy", "onLineNumberClick", "e", "renderWordDiff", "diffArray", "renderer", "map", "wordDiff", "i", "content", "value", "type", "ADDED", "className", "wordAdded", "children", "REMOVED", "wordRemoved", "renderLine", "lineNumber", "prefix", "additionalLineNumber", "additionalPrefix", "lineNumberTemplate", "additionalLineNumberTemplate", "highlightLine", "includes", "added", "removed", "changed", "CHANGED", "hasWordDiff", "Array", "isArray", "renderContent", "ElementType", "onClick", "gutter", "emptyGutter", "diffAdded", "diffRemoved", "diffChanged", "<PERSON><PERSON><PERSON>", "renderGutter", "marker", "emptyLine", "highlightedLine", "left", "LEFT", "right", "RIGHT", "onMouseDown", "elements", "document", "getElementsByClassName", "element", "item", "classList", "add", "title", "contentText", "renderSplitView", "index", "line", "renderInlineView", "DEFAULT", "onBlockClickProxy", "renderSkippedLineIndicator", "num", "blockNumber", "leftBlockLineNumber", "rightBlockLineNumber", "message", "codeFoldMessageRenderer", "codeFoldContent", "codeFoldContentContainer", "codeFoldExpandButton", "tabIndex", "isUnifiedViewWithoutLineNumbers", "codeFold", "codeFoldGutter", "renderDiff", "lineInformation", "diffLines", "alwaysShowLines", "extraLines", "Math", "round", "lineBlocks", "blocks", "diffNodes", "lineIndex", "blockIndex", "lastLineOfBlock", "endLine", "lines", "render", "leftTitle", "rightTitle", "JSON", "Error", "nodes", "colSpanOnSplitView", "colSpanOnInlineView", "deletions", "additions", "l", "totalChanges", "percentageAddition", "block", "blockAddition", "blockDeletion", "allExpanded", "summary", "role", "allExpandButton", "b", "style", "display", "gap", "<PERSON>ff<PERSON><PERSON><PERSON>", "onMouseUp", "remove", "elementsLeft", "width", "colSpan", "titleBlock", "column"], "sources": ["D:/Documents_2/New Coding/CoderBuddy/CoderBuddy/frontend/node_modules/react-diff-viewer-continued/lib/esm/src/index.js"], "sourcesContent": ["import { jsx as _jsx, jsxs as _jsxs, Fragment as _Fragment } from \"react/jsx-runtime\";\nimport cn from \"classnames\";\nimport * as React from \"react\";\nimport memoize from \"memoize-one\";\nimport { computeHiddenBlocks } from \"./compute-hidden-blocks.js\";\nimport { DiffMethod, DiffType, computeLineInformation, } from \"./compute-lines.js\";\nimport { Expand } from \"./expand.js\";\nimport computeStyles from \"./styles.js\";\nimport { Fold } from \"./fold.js\";\nexport var LineNumberPrefix;\n(function (LineNumberPrefix) {\n    LineNumberPrefix[\"LEFT\"] = \"L\";\n    LineNumberPrefix[\"RIGHT\"] = \"R\";\n})(LineNumberPrefix || (LineNumberPrefix = {}));\nclass DiffViewer extends React.Component {\n    styles;\n    static defaultProps = {\n        oldValue: \"\",\n        newValue: \"\",\n        splitView: true,\n        highlightLines: [],\n        disableWordDiff: false,\n        compareMethod: DiffMethod.CHARS,\n        styles: {},\n        hideLineNumbers: false,\n        extraLinesSurroundingDiff: 3,\n        showDiffOnly: true,\n        useDarkTheme: false,\n        linesOffset: 0,\n        nonce: \"\",\n    };\n    constructor(props) {\n        super(props);\n        this.state = {\n            expandedBlocks: [],\n            noSelect: undefined,\n        };\n    }\n    /**\n     * Resets code block expand to the initial stage. Will be exposed to the parent component via\n     * refs.\n     */\n    resetCodeBlocks = () => {\n        if (this.state.expandedBlocks.length > 0) {\n            this.setState({\n                expandedBlocks: [],\n            });\n            return true;\n        }\n        return false;\n    };\n    /**\n     * Pushes the target expanded code block to the state. During the re-render,\n     * this value is used to expand/fold unmodified code.\n     */\n    onBlockExpand = (id) => {\n        const prevState = this.state.expandedBlocks.slice();\n        prevState.push(id);\n        this.setState({\n            expandedBlocks: prevState,\n        });\n    };\n    /**\n     * Computes final styles for the diff viewer. It combines the default styles with the user\n     * supplied overrides. The computed styles are cached with performance in mind.\n     *\n     * @param styles User supplied style overrides.\n     */\n    computeStyles = memoize(computeStyles);\n    /**\n     * Returns a function with clicked line number in the closure. Returns an no-op function when no\n     * onLineNumberClick handler is supplied.\n     *\n     * @param id Line id of a line.\n     */\n    onLineNumberClickProxy = (id) => {\n        if (this.props.onLineNumberClick) {\n            return (e) => this.props.onLineNumberClick(id, e);\n        }\n        return () => { };\n    };\n    /**\n     * Maps over the word diff and constructs the required React elements to show word diff.\n     *\n     * @param diffArray Word diff information derived from line information.\n     * @param renderer Optional renderer to format diff words. Useful for syntax highlighting.\n     */\n    renderWordDiff = (diffArray, renderer) => {\n        return diffArray.map((wordDiff, i) => {\n            const content = renderer\n                ? renderer(wordDiff.value)\n                : (typeof wordDiff.value === 'string'\n                    ? wordDiff.value\n                    // If wordDiff.value is DiffInformation, we don't handle it, unclear why. See c0c99f5712.\n                    : undefined);\n            return wordDiff.type === DiffType.ADDED ? (_jsx(\"ins\", { className: cn(this.styles.wordDiff, {\n                    [this.styles.wordAdded]: wordDiff.type === DiffType.ADDED,\n                }), children: content }, i)) : wordDiff.type === DiffType.REMOVED ? (_jsx(\"del\", { className: cn(this.styles.wordDiff, {\n                    [this.styles.wordRemoved]: wordDiff.type === DiffType.REMOVED,\n                }), children: content }, i)) : (_jsx(\"span\", { className: cn(this.styles.wordDiff), children: content }, i));\n        });\n    };\n    /**\n     * Maps over the line diff and constructs the required react elements to show line diff. It calls\n     * renderWordDiff when encountering word diff. This takes care of both inline and split view line\n     * renders.\n     *\n     * @param lineNumber Line number of the current line.\n     * @param type Type of diff of the current line.\n     * @param prefix Unique id to prefix with the line numbers.\n     * @param value Content of the line. It can be a string or a word diff array.\n     * @param additionalLineNumber Additional line number to be shown. Useful for rendering inline\n     *  diff view. Right line number will be passed as additionalLineNumber.\n     * @param additionalPrefix Similar to prefix but for additional line number.\n     */\n    renderLine = (lineNumber, type, prefix, value, additionalLineNumber, additionalPrefix) => {\n        const lineNumberTemplate = `${prefix}-${lineNumber}`;\n        const additionalLineNumberTemplate = `${additionalPrefix}-${additionalLineNumber}`;\n        const highlightLine = this.props.highlightLines.includes(lineNumberTemplate) ||\n            this.props.highlightLines.includes(additionalLineNumberTemplate);\n        const added = type === DiffType.ADDED;\n        const removed = type === DiffType.REMOVED;\n        const changed = type === DiffType.CHANGED;\n        let content;\n        const hasWordDiff = Array.isArray(value);\n        if (hasWordDiff) {\n            content = this.renderWordDiff(value, this.props.renderContent);\n        }\n        else if (this.props.renderContent) {\n            content = this.props.renderContent(value);\n        }\n        else {\n            content = value;\n        }\n        let ElementType = \"div\";\n        if (added && !hasWordDiff) {\n            ElementType = \"ins\";\n        }\n        else if (removed && !hasWordDiff) {\n            ElementType = \"del\";\n        }\n        return (_jsxs(_Fragment, { children: [!this.props.hideLineNumbers && (_jsx(\"td\", { onClick: lineNumber && this.onLineNumberClickProxy(lineNumberTemplate), className: cn(this.styles.gutter, {\n                        [this.styles.emptyGutter]: !lineNumber,\n                        [this.styles.diffAdded]: added,\n                        [this.styles.diffRemoved]: removed,\n                        [this.styles.diffChanged]: changed,\n                        [this.styles.highlightedGutter]: highlightLine,\n                    }), children: _jsx(\"pre\", { className: this.styles.lineNumber, children: lineNumber }) })), !this.props.splitView && !this.props.hideLineNumbers && (_jsx(\"td\", { onClick: additionalLineNumber &&\n                        this.onLineNumberClickProxy(additionalLineNumberTemplate), className: cn(this.styles.gutter, {\n                        [this.styles.emptyGutter]: !additionalLineNumber,\n                        [this.styles.diffAdded]: added,\n                        [this.styles.diffRemoved]: removed,\n                        [this.styles.diffChanged]: changed,\n                        [this.styles.highlightedGutter]: highlightLine,\n                    }), children: _jsx(\"pre\", { className: this.styles.lineNumber, children: additionalLineNumber }) })), this.props.renderGutter\n                    ? this.props.renderGutter({\n                        lineNumber,\n                        type,\n                        prefix,\n                        value,\n                        additionalLineNumber,\n                        additionalPrefix,\n                        styles: this.styles,\n                    })\n                    : null, _jsx(\"td\", { className: cn(this.styles.marker, {\n                        [this.styles.emptyLine]: !content,\n                        [this.styles.diffAdded]: added,\n                        [this.styles.diffRemoved]: removed,\n                        [this.styles.diffChanged]: changed,\n                        [this.styles.highlightedLine]: highlightLine,\n                    }), children: _jsxs(\"pre\", { children: [added && \"+\", removed && \"-\"] }) }), _jsx(\"td\", { className: cn(this.styles.content, {\n                        [this.styles.emptyLine]: !content,\n                        [this.styles.diffAdded]: added,\n                        [this.styles.diffRemoved]: removed,\n                        [this.styles.diffChanged]: changed,\n                        [this.styles.highlightedLine]: highlightLine,\n                        left: prefix === LineNumberPrefix.LEFT,\n                        right: prefix === LineNumberPrefix.RIGHT,\n                    }), onMouseDown: () => {\n                        const elements = document.getElementsByClassName(prefix === LineNumberPrefix.LEFT ? \"right\" : \"left\");\n                        for (let i = 0; i < elements.length; i++) {\n                            const element = elements.item(i);\n                            element.classList.add(this.styles.noSelect);\n                        }\n                    }, title: added && !hasWordDiff\n                        ? \"Added line\"\n                        : removed && !hasWordDiff\n                            ? \"Removed line\"\n                            : undefined, children: _jsx(ElementType, { className: this.styles.contentText, children: content }) })] }));\n    };\n    /**\n     * Generates lines for split view.\n     *\n     * @param obj Line diff information.\n     * @param obj.left Life diff information for the left pane of the split view.\n     * @param obj.right Life diff information for the right pane of the split view.\n     * @param index React key for the lines.\n     */\n    renderSplitView = ({ left, right }, index) => {\n        return (_jsxs(\"tr\", { className: this.styles.line, children: [this.renderLine(left.lineNumber, left.type, LineNumberPrefix.LEFT, left.value), this.renderLine(right.lineNumber, right.type, LineNumberPrefix.RIGHT, right.value)] }, index));\n    };\n    /**\n     * Generates lines for inline view.\n     *\n     * @param obj Line diff information.\n     * @param obj.left Life diff information for the added section of the inline view.\n     * @param obj.right Life diff information for the removed section of the inline view.\n     * @param index React key for the lines.\n     */\n    renderInlineView = ({ left, right }, index) => {\n        let content;\n        if (left.type === DiffType.REMOVED && right.type === DiffType.ADDED) {\n            return (_jsxs(React.Fragment, { children: [_jsx(\"tr\", { className: this.styles.line, children: this.renderLine(left.lineNumber, left.type, LineNumberPrefix.LEFT, left.value, null) }), _jsx(\"tr\", { className: this.styles.line, children: this.renderLine(null, right.type, LineNumberPrefix.RIGHT, right.value, right.lineNumber, LineNumberPrefix.RIGHT) })] }, index));\n        }\n        if (left.type === DiffType.REMOVED) {\n            content = this.renderLine(left.lineNumber, left.type, LineNumberPrefix.LEFT, left.value, null);\n        }\n        if (left.type === DiffType.DEFAULT) {\n            content = this.renderLine(left.lineNumber, left.type, LineNumberPrefix.LEFT, left.value, right.lineNumber, LineNumberPrefix.RIGHT);\n        }\n        if (right.type === DiffType.ADDED) {\n            content = this.renderLine(null, right.type, LineNumberPrefix.RIGHT, right.value, right.lineNumber);\n        }\n        return (_jsx(\"tr\", { className: this.styles.line, children: content }, index));\n    };\n    /**\n     * Returns a function with clicked block number in the closure.\n     *\n     * @param id Cold fold block id.\n     */\n    onBlockClickProxy = (id) => () => this.onBlockExpand(id);\n    /**\n     * Generates cold fold block. It also uses the custom message renderer when available to show\n     * cold fold messages.\n     *\n     * @param num Number of skipped lines between two blocks.\n     * @param blockNumber Code fold block id.\n     * @param leftBlockLineNumber First left line number after the current code fold block.\n     * @param rightBlockLineNumber First right line number after the current code fold block.\n     */\n    renderSkippedLineIndicator = (num, blockNumber, leftBlockLineNumber, rightBlockLineNumber) => {\n        const { hideLineNumbers, splitView } = this.props;\n        const message = this.props.codeFoldMessageRenderer ? (this.props.codeFoldMessageRenderer(num, leftBlockLineNumber, rightBlockLineNumber)) : (_jsxs(\"span\", { className: this.styles.codeFoldContent, children: [\"Expand \", num, \" lines ...\"] }));\n        const content = (_jsx(\"td\", { className: this.styles.codeFoldContentContainer, children: _jsx(\"button\", { type: \"button\", className: this.styles.codeFoldExpandButton, onClick: this.onBlockClickProxy(blockNumber), tabIndex: 0, children: message }) }));\n        const isUnifiedViewWithoutLineNumbers = !splitView && !hideLineNumbers;\n        return (_jsxs(\"tr\", { className: this.styles.codeFold, children: [!hideLineNumbers && _jsx(\"td\", { className: this.styles.codeFoldGutter }), this.props.renderGutter ? (_jsx(\"td\", { className: this.styles.codeFoldGutter })) : null, _jsx(\"td\", { className: cn({\n                        [this.styles.codeFoldGutter]: isUnifiedViewWithoutLineNumbers,\n                    }) }), isUnifiedViewWithoutLineNumbers ? (_jsxs(React.Fragment, { children: [_jsx(\"td\", {}), content] })) : (_jsxs(React.Fragment, { children: [content, this.props.renderGutter ? _jsx(\"td\", {}) : null, _jsx(\"td\", {}), _jsx(\"td\", {}), !hideLineNumbers ? _jsx(\"td\", {}) : null] }))] }, `${leftBlockLineNumber}-${rightBlockLineNumber}`));\n    };\n    /**\n     * Generates the entire diff view.\n     */\n    renderDiff = () => {\n        const { oldValue, newValue, splitView, disableWordDiff, compareMethod, linesOffset, } = this.props;\n        const { lineInformation, diffLines } = computeLineInformation(oldValue, newValue, disableWordDiff, compareMethod, linesOffset, this.props.alwaysShowLines);\n        const extraLines = this.props.extraLinesSurroundingDiff < 0\n            ? 0\n            : Math.round(this.props.extraLinesSurroundingDiff);\n        const { lineBlocks, blocks } = computeHiddenBlocks(lineInformation, diffLines, extraLines);\n        const diffNodes = lineInformation.map((line, lineIndex) => {\n            if (this.props.showDiffOnly) {\n                const blockIndex = lineBlocks[lineIndex];\n                if (blockIndex !== undefined) {\n                    const lastLineOfBlock = blocks[blockIndex].endLine === lineIndex;\n                    if (!this.state.expandedBlocks.includes(blockIndex) &&\n                        lastLineOfBlock) {\n                        return (_jsx(React.Fragment, { children: this.renderSkippedLineIndicator(blocks[blockIndex].lines, blockIndex, line.left.lineNumber, line.right.lineNumber) }, lineIndex));\n                    }\n                    if (!this.state.expandedBlocks.includes(blockIndex)) {\n                        return null;\n                    }\n                }\n            }\n            return splitView\n                ? this.renderSplitView(line, lineIndex)\n                : this.renderInlineView(line, lineIndex);\n        });\n        return {\n            diffNodes,\n            blocks,\n            lineInformation,\n        };\n    };\n    render = () => {\n        const { oldValue, newValue, useDarkTheme, leftTitle, rightTitle, splitView, compareMethod, hideLineNumbers, nonce, } = this.props;\n        if (typeof compareMethod === \"string\" &&\n            compareMethod !== DiffMethod.JSON) {\n            if (typeof oldValue !== \"string\" || typeof newValue !== \"string\") {\n                throw Error('\"oldValue\" and \"newValue\" should be strings');\n            }\n        }\n        this.styles = this.computeStyles(this.props.styles, useDarkTheme, nonce);\n        const nodes = this.renderDiff();\n        let colSpanOnSplitView = 3;\n        let colSpanOnInlineView = 4;\n        if (hideLineNumbers) {\n            colSpanOnSplitView -= 1;\n            colSpanOnInlineView -= 1;\n        }\n        if (this.props.renderGutter) {\n            colSpanOnSplitView += 1;\n            colSpanOnInlineView += 1;\n        }\n        let deletions = 0;\n        let additions = 0;\n        for (const l of nodes.lineInformation) {\n            if (l.left.type === DiffType.ADDED) {\n                additions++;\n            }\n            if (l.right.type === DiffType.ADDED) {\n                additions++;\n            }\n            if (l.left.type === DiffType.REMOVED) {\n                deletions++;\n            }\n            if (l.right.type === DiffType.REMOVED) {\n                deletions++;\n            }\n        }\n        const totalChanges = deletions + additions;\n        const percentageAddition = Math.round((additions / totalChanges) * 100);\n        const blocks = [];\n        for (let i = 0; i < 5; i++) {\n            if (percentageAddition > i * 20) {\n                blocks.push(_jsx(\"span\", { className: cn(this.styles.block, this.styles.blockAddition) }, i));\n            }\n            else {\n                blocks.push(_jsx(\"span\", { className: cn(this.styles.block, this.styles.blockDeletion) }, i));\n            }\n        }\n        const allExpanded = this.state.expandedBlocks.length === nodes.blocks.length;\n        return (_jsxs(\"div\", { children: [_jsxs(\"div\", { className: this.styles.summary, role: \"banner\", children: [_jsx(\"button\", { type: \"button\", className: this.styles.allExpandButton, onClick: () => {\n                                this.setState({\n                                    expandedBlocks: allExpanded\n                                        ? []\n                                        : nodes.blocks.map((b) => b.index),\n                                });\n                            }, children: allExpanded ? _jsx(Fold, {}) : _jsx(Expand, {}) }), \" \", totalChanges, _jsx(\"div\", { style: { display: \"flex\", gap: \"1px\" }, children: blocks }), this.props.summary ? _jsx(\"span\", { children: this.props.summary }) : null] }), _jsx(\"table\", { className: cn(this.styles.diffContainer, {\n                        [this.styles.splitView]: splitView,\n                    }), onMouseUp: () => {\n                        const elements = document.getElementsByClassName(\"right\");\n                        for (let i = 0; i < elements.length; i++) {\n                            const element = elements.item(i);\n                            element.classList.remove(this.styles.noSelect);\n                        }\n                        const elementsLeft = document.getElementsByClassName(\"left\");\n                        for (let i = 0; i < elementsLeft.length; i++) {\n                            const element = elementsLeft.item(i);\n                            element.classList.remove(this.styles.noSelect);\n                        }\n                    }, children: _jsxs(\"tbody\", { children: [_jsxs(\"tr\", { children: [!this.props.hideLineNumbers ? _jsx(\"td\", { width: \"50px\" }) : null, !splitView && !this.props.hideLineNumbers ? (_jsx(\"td\", { width: \"50px\" })) : null, this.props.renderGutter ? _jsx(\"td\", { width: \"50px\" }) : null, _jsx(\"td\", { width: \"28px\" }), _jsx(\"td\", { width: \"100%\" }), splitView ? (_jsxs(_Fragment, { children: [!this.props.hideLineNumbers ? _jsx(\"td\", { width: \"50px\" }) : null, this.props.renderGutter ? _jsx(\"td\", { width: \"50px\" }) : null, _jsx(\"td\", { width: \"28px\" }), _jsx(\"td\", { width: \"100%\" })] })) : null] }), leftTitle || rightTitle ? (_jsxs(\"tr\", { children: [_jsx(\"th\", { colSpan: splitView ? colSpanOnSplitView : colSpanOnInlineView, className: cn(this.styles.titleBlock, this.styles.column), children: leftTitle ? (_jsx(\"pre\", { className: this.styles.contentText, children: leftTitle })) : null }), splitView ? (_jsx(\"th\", { colSpan: colSpanOnSplitView, className: cn(this.styles.titleBlock, this.styles.column), children: rightTitle ? (_jsx(\"pre\", { className: this.styles.contentText, children: rightTitle })) : null })) : null] })) : null, nodes.diffNodes] }) })] }));\n    };\n}\nexport default DiffViewer;\nexport { DiffMethod };\n"], "mappings": "AAAA,SAASA,GAAG,IAAIC,IAAI,EAAEC,IAAI,IAAIC,KAAK,EAAEC,QAAQ,IAAIC,SAAS,QAAQ,mBAAmB;AACrF,OAAOC,EAAE,MAAM,YAAY;AAC3B,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,OAAOC,OAAO,MAAM,aAAa;AACjC,SAASC,mBAAmB,QAAQ,4BAA4B;AAChE,SAASC,UAAU,EAAEC,QAAQ,EAAEC,sBAAsB,QAAS,oBAAoB;AAClF,SAASC,MAAM,QAAQ,aAAa;AACpC,OAAOC,aAAa,MAAM,aAAa;AACvC,SAASC,IAAI,QAAQ,WAAW;AAChC,OAAO,IAAIC,gBAAgB;AAC3B,CAAC,UAAUA,gBAAgB,EAAE;EACzBA,gBAAgB,CAAC,MAAM,CAAC,GAAG,GAAG;EAC9BA,gBAAgB,CAAC,OAAO,CAAC,GAAG,GAAG;AACnC,CAAC,EAAEA,gBAAgB,KAAKA,gBAAgB,GAAG,CAAC,CAAC,CAAC,CAAC;AAC/C,MAAMC,UAAU,SAASV,KAAK,CAACW,SAAS,CAAC;EACrCC,MAAM;EACN,OAAOC,YAAY,GAAG;IAClBC,QAAQ,EAAE,EAAE;IACZC,QAAQ,EAAE,EAAE;IACZC,SAAS,EAAE,IAAI;IACfC,cAAc,EAAE,EAAE;IAClBC,eAAe,EAAE,KAAK;IACtBC,aAAa,EAAEhB,UAAU,CAACiB,KAAK;IAC/BR,MAAM,EAAE,CAAC,CAAC;IACVS,eAAe,EAAE,KAAK;IACtBC,yBAAyB,EAAE,CAAC;IAC5BC,YAAY,EAAE,IAAI;IAClBC,YAAY,EAAE,KAAK;IACnBC,WAAW,EAAE,CAAC;IACdC,KAAK,EAAE;EACX,CAAC;EACDC,WAAWA,CAACC,KAAK,EAAE;IACf,KAAK,CAACA,KAAK,CAAC;IACZ,IAAI,CAACC,KAAK,GAAG;MACTC,cAAc,EAAE,EAAE;MAClBC,QAAQ,EAAEC;IACd,CAAC;EACL;EACA;AACJ;AACA;AACA;EACIC,eAAe,GAAGA,CAAA,KAAM;IACpB,IAAI,IAAI,CAACJ,KAAK,CAACC,cAAc,CAACI,MAAM,GAAG,CAAC,EAAE;MACtC,IAAI,CAACC,QAAQ,CAAC;QACVL,cAAc,EAAE;MACpB,CAAC,CAAC;MACF,OAAO,IAAI;IACf;IACA,OAAO,KAAK;EAChB,CAAC;EACD;AACJ;AACA;AACA;EACIM,aAAa,GAAIC,EAAE,IAAK;IACpB,MAAMC,SAAS,GAAG,IAAI,CAACT,KAAK,CAACC,cAAc,CAACS,KAAK,CAAC,CAAC;IACnDD,SAAS,CAACE,IAAI,CAACH,EAAE,CAAC;IAClB,IAAI,CAACF,QAAQ,CAAC;MACVL,cAAc,EAAEQ;IACpB,CAAC,CAAC;EACN,CAAC;EACD;AACJ;AACA;AACA;AACA;AACA;EACI/B,aAAa,GAAGN,OAAO,CAACM,aAAa,CAAC;EACtC;AACJ;AACA;AACA;AACA;AACA;EACIkC,sBAAsB,GAAIJ,EAAE,IAAK;IAC7B,IAAI,IAAI,CAACT,KAAK,CAACc,iBAAiB,EAAE;MAC9B,OAAQC,CAAC,IAAK,IAAI,CAACf,KAAK,CAACc,iBAAiB,CAACL,EAAE,EAAEM,CAAC,CAAC;IACrD;IACA,OAAO,MAAM,CAAE,CAAC;EACpB,CAAC;EACD;AACJ;AACA;AACA;AACA;AACA;EACIC,cAAc,GAAGA,CAACC,SAAS,EAAEC,QAAQ,KAAK;IACtC,OAAOD,SAAS,CAACE,GAAG,CAAC,CAACC,QAAQ,EAAEC,CAAC,KAAK;MAClC,MAAMC,OAAO,GAAGJ,QAAQ,GAClBA,QAAQ,CAACE,QAAQ,CAACG,KAAK,CAAC,GACvB,OAAOH,QAAQ,CAACG,KAAK,KAAK,QAAQ,GAC/BH,QAAQ,CAACG;MACX;MAAA,EACEnB,SAAU;MACpB,OAAOgB,QAAQ,CAACI,IAAI,KAAKhD,QAAQ,CAACiD,KAAK,GAAI3D,IAAI,CAAC,KAAK,EAAE;QAAE4D,SAAS,EAAEvD,EAAE,CAAC,IAAI,CAACa,MAAM,CAACoC,QAAQ,EAAE;UACrF,CAAC,IAAI,CAACpC,MAAM,CAAC2C,SAAS,GAAGP,QAAQ,CAACI,IAAI,KAAKhD,QAAQ,CAACiD;QACxD,CAAC,CAAC;QAAEG,QAAQ,EAAEN;MAAQ,CAAC,EAAED,CAAC,CAAC,GAAID,QAAQ,CAACI,IAAI,KAAKhD,QAAQ,CAACqD,OAAO,GAAI/D,IAAI,CAAC,KAAK,EAAE;QAAE4D,SAAS,EAAEvD,EAAE,CAAC,IAAI,CAACa,MAAM,CAACoC,QAAQ,EAAE;UACnH,CAAC,IAAI,CAACpC,MAAM,CAAC8C,WAAW,GAAGV,QAAQ,CAACI,IAAI,KAAKhD,QAAQ,CAACqD;QAC1D,CAAC,CAAC;QAAED,QAAQ,EAAEN;MAAQ,CAAC,EAAED,CAAC,CAAC,GAAKvD,IAAI,CAAC,MAAM,EAAE;QAAE4D,SAAS,EAAEvD,EAAE,CAAC,IAAI,CAACa,MAAM,CAACoC,QAAQ,CAAC;QAAEQ,QAAQ,EAAEN;MAAQ,CAAC,EAAED,CAAC,CAAE;IACpH,CAAC,CAAC;EACN,CAAC;EACD;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACIU,UAAU,GAAGA,CAACC,UAAU,EAAER,IAAI,EAAES,MAAM,EAAEV,KAAK,EAAEW,oBAAoB,EAAEC,gBAAgB,KAAK;IACtF,MAAMC,kBAAkB,GAAG,GAAGH,MAAM,IAAID,UAAU,EAAE;IACpD,MAAMK,4BAA4B,GAAG,GAAGF,gBAAgB,IAAID,oBAAoB,EAAE;IAClF,MAAMI,aAAa,GAAG,IAAI,CAACtC,KAAK,CAACX,cAAc,CAACkD,QAAQ,CAACH,kBAAkB,CAAC,IACxE,IAAI,CAACpC,KAAK,CAACX,cAAc,CAACkD,QAAQ,CAACF,4BAA4B,CAAC;IACpE,MAAMG,KAAK,GAAGhB,IAAI,KAAKhD,QAAQ,CAACiD,KAAK;IACrC,MAAMgB,OAAO,GAAGjB,IAAI,KAAKhD,QAAQ,CAACqD,OAAO;IACzC,MAAMa,OAAO,GAAGlB,IAAI,KAAKhD,QAAQ,CAACmE,OAAO;IACzC,IAAIrB,OAAO;IACX,MAAMsB,WAAW,GAAGC,KAAK,CAACC,OAAO,CAACvB,KAAK,CAAC;IACxC,IAAIqB,WAAW,EAAE;MACbtB,OAAO,GAAG,IAAI,CAACN,cAAc,CAACO,KAAK,EAAE,IAAI,CAACvB,KAAK,CAAC+C,aAAa,CAAC;IAClE,CAAC,MACI,IAAI,IAAI,CAAC/C,KAAK,CAAC+C,aAAa,EAAE;MAC/BzB,OAAO,GAAG,IAAI,CAACtB,KAAK,CAAC+C,aAAa,CAACxB,KAAK,CAAC;IAC7C,CAAC,MACI;MACDD,OAAO,GAAGC,KAAK;IACnB;IACA,IAAIyB,WAAW,GAAG,KAAK;IACvB,IAAIR,KAAK,IAAI,CAACI,WAAW,EAAE;MACvBI,WAAW,GAAG,KAAK;IACvB,CAAC,MACI,IAAIP,OAAO,IAAI,CAACG,WAAW,EAAE;MAC9BI,WAAW,GAAG,KAAK;IACvB;IACA,OAAQhF,KAAK,CAACE,SAAS,EAAE;MAAE0D,QAAQ,EAAE,CAAC,CAAC,IAAI,CAAC5B,KAAK,CAACP,eAAe,IAAK3B,IAAI,CAAC,IAAI,EAAE;QAAEmF,OAAO,EAAEjB,UAAU,IAAI,IAAI,CAACnB,sBAAsB,CAACuB,kBAAkB,CAAC;QAAEV,SAAS,EAAEvD,EAAE,CAAC,IAAI,CAACa,MAAM,CAACkE,MAAM,EAAE;UAC7K,CAAC,IAAI,CAAClE,MAAM,CAACmE,WAAW,GAAG,CAACnB,UAAU;UACtC,CAAC,IAAI,CAAChD,MAAM,CAACoE,SAAS,GAAGZ,KAAK;UAC9B,CAAC,IAAI,CAACxD,MAAM,CAACqE,WAAW,GAAGZ,OAAO;UAClC,CAAC,IAAI,CAACzD,MAAM,CAACsE,WAAW,GAAGZ,OAAO;UAClC,CAAC,IAAI,CAAC1D,MAAM,CAACuE,iBAAiB,GAAGjB;QACrC,CAAC,CAAC;QAAEV,QAAQ,EAAE9D,IAAI,CAAC,KAAK,EAAE;UAAE4D,SAAS,EAAE,IAAI,CAAC1C,MAAM,CAACgD,UAAU;UAAEJ,QAAQ,EAAEI;QAAW,CAAC;MAAE,CAAC,CAAE,EAAE,CAAC,IAAI,CAAChC,KAAK,CAACZ,SAAS,IAAI,CAAC,IAAI,CAACY,KAAK,CAACP,eAAe,IAAK3B,IAAI,CAAC,IAAI,EAAE;QAAEmF,OAAO,EAAEf,oBAAoB,IAC3L,IAAI,CAACrB,sBAAsB,CAACwB,4BAA4B,CAAC;QAAEX,SAAS,EAAEvD,EAAE,CAAC,IAAI,CAACa,MAAM,CAACkE,MAAM,EAAE;UAC7F,CAAC,IAAI,CAAClE,MAAM,CAACmE,WAAW,GAAG,CAACjB,oBAAoB;UAChD,CAAC,IAAI,CAAClD,MAAM,CAACoE,SAAS,GAAGZ,KAAK;UAC9B,CAAC,IAAI,CAACxD,MAAM,CAACqE,WAAW,GAAGZ,OAAO;UAClC,CAAC,IAAI,CAACzD,MAAM,CAACsE,WAAW,GAAGZ,OAAO;UAClC,CAAC,IAAI,CAAC1D,MAAM,CAACuE,iBAAiB,GAAGjB;QACrC,CAAC,CAAC;QAAEV,QAAQ,EAAE9D,IAAI,CAAC,KAAK,EAAE;UAAE4D,SAAS,EAAE,IAAI,CAAC1C,MAAM,CAACgD,UAAU;UAAEJ,QAAQ,EAAEM;QAAqB,CAAC;MAAE,CAAC,CAAE,EAAE,IAAI,CAAClC,KAAK,CAACwD,YAAY,GAC3H,IAAI,CAACxD,KAAK,CAACwD,YAAY,CAAC;QACtBxB,UAAU;QACVR,IAAI;QACJS,MAAM;QACNV,KAAK;QACLW,oBAAoB;QACpBC,gBAAgB;QAChBnD,MAAM,EAAE,IAAI,CAACA;MACjB,CAAC,CAAC,GACA,IAAI,EAAElB,IAAI,CAAC,IAAI,EAAE;QAAE4D,SAAS,EAAEvD,EAAE,CAAC,IAAI,CAACa,MAAM,CAACyE,MAAM,EAAE;UACnD,CAAC,IAAI,CAACzE,MAAM,CAAC0E,SAAS,GAAG,CAACpC,OAAO;UACjC,CAAC,IAAI,CAACtC,MAAM,CAACoE,SAAS,GAAGZ,KAAK;UAC9B,CAAC,IAAI,CAACxD,MAAM,CAACqE,WAAW,GAAGZ,OAAO;UAClC,CAAC,IAAI,CAACzD,MAAM,CAACsE,WAAW,GAAGZ,OAAO;UAClC,CAAC,IAAI,CAAC1D,MAAM,CAAC2E,eAAe,GAAGrB;QACnC,CAAC,CAAC;QAAEV,QAAQ,EAAE5D,KAAK,CAAC,KAAK,EAAE;UAAE4D,QAAQ,EAAE,CAACY,KAAK,IAAI,GAAG,EAAEC,OAAO,IAAI,GAAG;QAAE,CAAC;MAAE,CAAC,CAAC,EAAE3E,IAAI,CAAC,IAAI,EAAE;QAAE4D,SAAS,EAAEvD,EAAE,CAAC,IAAI,CAACa,MAAM,CAACsC,OAAO,EAAE;UACzH,CAAC,IAAI,CAACtC,MAAM,CAAC0E,SAAS,GAAG,CAACpC,OAAO;UACjC,CAAC,IAAI,CAACtC,MAAM,CAACoE,SAAS,GAAGZ,KAAK;UAC9B,CAAC,IAAI,CAACxD,MAAM,CAACqE,WAAW,GAAGZ,OAAO;UAClC,CAAC,IAAI,CAACzD,MAAM,CAACsE,WAAW,GAAGZ,OAAO;UAClC,CAAC,IAAI,CAAC1D,MAAM,CAAC2E,eAAe,GAAGrB,aAAa;UAC5CsB,IAAI,EAAE3B,MAAM,KAAKpD,gBAAgB,CAACgF,IAAI;UACtCC,KAAK,EAAE7B,MAAM,KAAKpD,gBAAgB,CAACkF;QACvC,CAAC,CAAC;QAAEC,WAAW,EAAEA,CAAA,KAAM;UACnB,MAAMC,QAAQ,GAAGC,QAAQ,CAACC,sBAAsB,CAAClC,MAAM,KAAKpD,gBAAgB,CAACgF,IAAI,GAAG,OAAO,GAAG,MAAM,CAAC;UACrG,KAAK,IAAIxC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG4C,QAAQ,CAAC3D,MAAM,EAAEe,CAAC,EAAE,EAAE;YACtC,MAAM+C,OAAO,GAAGH,QAAQ,CAACI,IAAI,CAAChD,CAAC,CAAC;YAChC+C,OAAO,CAACE,SAAS,CAACC,GAAG,CAAC,IAAI,CAACvF,MAAM,CAACmB,QAAQ,CAAC;UAC/C;QACJ,CAAC;QAAEqE,KAAK,EAAEhC,KAAK,IAAI,CAACI,WAAW,GACzB,YAAY,GACZH,OAAO,IAAI,CAACG,WAAW,GACnB,cAAc,GACdxC,SAAS;QAAEwB,QAAQ,EAAE9D,IAAI,CAACkF,WAAW,EAAE;UAAEtB,SAAS,EAAE,IAAI,CAAC1C,MAAM,CAACyF,WAAW;UAAE7C,QAAQ,EAAEN;QAAQ,CAAC;MAAE,CAAC,CAAC;IAAE,CAAC,CAAC;EAClI,CAAC;EACD;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;EACIoD,eAAe,GAAGA,CAAC;IAAEd,IAAI;IAAEE;EAAM,CAAC,EAAEa,KAAK,KAAK;IAC1C,OAAQ3G,KAAK,CAAC,IAAI,EAAE;MAAE0D,SAAS,EAAE,IAAI,CAAC1C,MAAM,CAAC4F,IAAI;MAAEhD,QAAQ,EAAE,CAAC,IAAI,CAACG,UAAU,CAAC6B,IAAI,CAAC5B,UAAU,EAAE4B,IAAI,CAACpC,IAAI,EAAE3C,gBAAgB,CAACgF,IAAI,EAAED,IAAI,CAACrC,KAAK,CAAC,EAAE,IAAI,CAACQ,UAAU,CAAC+B,KAAK,CAAC9B,UAAU,EAAE8B,KAAK,CAACtC,IAAI,EAAE3C,gBAAgB,CAACkF,KAAK,EAAED,KAAK,CAACvC,KAAK,CAAC;IAAE,CAAC,EAAEoD,KAAK,CAAC;EAC/O,CAAC;EACD;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;EACIE,gBAAgB,GAAGA,CAAC;IAAEjB,IAAI;IAAEE;EAAM,CAAC,EAAEa,KAAK,KAAK;IAC3C,IAAIrD,OAAO;IACX,IAAIsC,IAAI,CAACpC,IAAI,KAAKhD,QAAQ,CAACqD,OAAO,IAAIiC,KAAK,CAACtC,IAAI,KAAKhD,QAAQ,CAACiD,KAAK,EAAE;MACjE,OAAQzD,KAAK,CAACI,KAAK,CAACH,QAAQ,EAAE;QAAE2D,QAAQ,EAAE,CAAC9D,IAAI,CAAC,IAAI,EAAE;UAAE4D,SAAS,EAAE,IAAI,CAAC1C,MAAM,CAAC4F,IAAI;UAAEhD,QAAQ,EAAE,IAAI,CAACG,UAAU,CAAC6B,IAAI,CAAC5B,UAAU,EAAE4B,IAAI,CAACpC,IAAI,EAAE3C,gBAAgB,CAACgF,IAAI,EAAED,IAAI,CAACrC,KAAK,EAAE,IAAI;QAAE,CAAC,CAAC,EAAEzD,IAAI,CAAC,IAAI,EAAE;UAAE4D,SAAS,EAAE,IAAI,CAAC1C,MAAM,CAAC4F,IAAI;UAAEhD,QAAQ,EAAE,IAAI,CAACG,UAAU,CAAC,IAAI,EAAE+B,KAAK,CAACtC,IAAI,EAAE3C,gBAAgB,CAACkF,KAAK,EAAED,KAAK,CAACvC,KAAK,EAAEuC,KAAK,CAAC9B,UAAU,EAAEnD,gBAAgB,CAACkF,KAAK;QAAE,CAAC,CAAC;MAAE,CAAC,EAAEY,KAAK,CAAC;IAC9W;IACA,IAAIf,IAAI,CAACpC,IAAI,KAAKhD,QAAQ,CAACqD,OAAO,EAAE;MAChCP,OAAO,GAAG,IAAI,CAACS,UAAU,CAAC6B,IAAI,CAAC5B,UAAU,EAAE4B,IAAI,CAACpC,IAAI,EAAE3C,gBAAgB,CAACgF,IAAI,EAAED,IAAI,CAACrC,KAAK,EAAE,IAAI,CAAC;IAClG;IACA,IAAIqC,IAAI,CAACpC,IAAI,KAAKhD,QAAQ,CAACsG,OAAO,EAAE;MAChCxD,OAAO,GAAG,IAAI,CAACS,UAAU,CAAC6B,IAAI,CAAC5B,UAAU,EAAE4B,IAAI,CAACpC,IAAI,EAAE3C,gBAAgB,CAACgF,IAAI,EAAED,IAAI,CAACrC,KAAK,EAAEuC,KAAK,CAAC9B,UAAU,EAAEnD,gBAAgB,CAACkF,KAAK,CAAC;IACtI;IACA,IAAID,KAAK,CAACtC,IAAI,KAAKhD,QAAQ,CAACiD,KAAK,EAAE;MAC/BH,OAAO,GAAG,IAAI,CAACS,UAAU,CAAC,IAAI,EAAE+B,KAAK,CAACtC,IAAI,EAAE3C,gBAAgB,CAACkF,KAAK,EAAED,KAAK,CAACvC,KAAK,EAAEuC,KAAK,CAAC9B,UAAU,CAAC;IACtG;IACA,OAAQlE,IAAI,CAAC,IAAI,EAAE;MAAE4D,SAAS,EAAE,IAAI,CAAC1C,MAAM,CAAC4F,IAAI;MAAEhD,QAAQ,EAAEN;IAAQ,CAAC,EAAEqD,KAAK,CAAC;EACjF,CAAC;EACD;AACJ;AACA;AACA;AACA;EACII,iBAAiB,GAAItE,EAAE,IAAK,MAAM,IAAI,CAACD,aAAa,CAACC,EAAE,CAAC;EACxD;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACIuE,0BAA0B,GAAGA,CAACC,GAAG,EAAEC,WAAW,EAAEC,mBAAmB,EAAEC,oBAAoB,KAAK;IAC1F,MAAM;MAAE3F,eAAe;MAAEL;IAAU,CAAC,GAAG,IAAI,CAACY,KAAK;IACjD,MAAMqF,OAAO,GAAG,IAAI,CAACrF,KAAK,CAACsF,uBAAuB,GAAI,IAAI,CAACtF,KAAK,CAACsF,uBAAuB,CAACL,GAAG,EAAEE,mBAAmB,EAAEC,oBAAoB,CAAC,GAAKpH,KAAK,CAAC,MAAM,EAAE;MAAE0D,SAAS,EAAE,IAAI,CAAC1C,MAAM,CAACuG,eAAe;MAAE3D,QAAQ,EAAE,CAAC,SAAS,EAAEqD,GAAG,EAAE,YAAY;IAAE,CAAC,CAAE;IACjP,MAAM3D,OAAO,GAAIxD,IAAI,CAAC,IAAI,EAAE;MAAE4D,SAAS,EAAE,IAAI,CAAC1C,MAAM,CAACwG,wBAAwB;MAAE5D,QAAQ,EAAE9D,IAAI,CAAC,QAAQ,EAAE;QAAE0D,IAAI,EAAE,QAAQ;QAAEE,SAAS,EAAE,IAAI,CAAC1C,MAAM,CAACyG,oBAAoB;QAAExC,OAAO,EAAE,IAAI,CAAC8B,iBAAiB,CAACG,WAAW,CAAC;QAAEQ,QAAQ,EAAE,CAAC;QAAE9D,QAAQ,EAAEyD;MAAQ,CAAC;IAAE,CAAC,CAAE;IAC1P,MAAMM,+BAA+B,GAAG,CAACvG,SAAS,IAAI,CAACK,eAAe;IACtE,OAAQzB,KAAK,CAAC,IAAI,EAAE;MAAE0D,SAAS,EAAE,IAAI,CAAC1C,MAAM,CAAC4G,QAAQ;MAAEhE,QAAQ,EAAE,CAAC,CAACnC,eAAe,IAAI3B,IAAI,CAAC,IAAI,EAAE;QAAE4D,SAAS,EAAE,IAAI,CAAC1C,MAAM,CAAC6G;MAAe,CAAC,CAAC,EAAE,IAAI,CAAC7F,KAAK,CAACwD,YAAY,GAAI1F,IAAI,CAAC,IAAI,EAAE;QAAE4D,SAAS,EAAE,IAAI,CAAC1C,MAAM,CAAC6G;MAAe,CAAC,CAAC,GAAI,IAAI,EAAE/H,IAAI,CAAC,IAAI,EAAE;QAAE4D,SAAS,EAAEvD,EAAE,CAAC;UAClP,CAAC,IAAI,CAACa,MAAM,CAAC6G,cAAc,GAAGF;QAClC,CAAC;MAAE,CAAC,CAAC,EAAEA,+BAA+B,GAAI3H,KAAK,CAACI,KAAK,CAACH,QAAQ,EAAE;QAAE2D,QAAQ,EAAE,CAAC9D,IAAI,CAAC,IAAI,EAAE,CAAC,CAAC,CAAC,EAAEwD,OAAO;MAAE,CAAC,CAAC,GAAKtD,KAAK,CAACI,KAAK,CAACH,QAAQ,EAAE;QAAE2D,QAAQ,EAAE,CAACN,OAAO,EAAE,IAAI,CAACtB,KAAK,CAACwD,YAAY,GAAG1F,IAAI,CAAC,IAAI,EAAE,CAAC,CAAC,CAAC,GAAG,IAAI,EAAEA,IAAI,CAAC,IAAI,EAAE,CAAC,CAAC,CAAC,EAAEA,IAAI,CAAC,IAAI,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC2B,eAAe,GAAG3B,IAAI,CAAC,IAAI,EAAE,CAAC,CAAC,CAAC,GAAG,IAAI;MAAE,CAAC,CAAE;IAAE,CAAC,EAAE,GAAGqH,mBAAmB,IAAIC,oBAAoB,EAAE,CAAC;EAC7V,CAAC;EACD;AACJ;AACA;EACIU,UAAU,GAAGA,CAAA,KAAM;IACf,MAAM;MAAE5G,QAAQ;MAAEC,QAAQ;MAAEC,SAAS;MAAEE,eAAe;MAAEC,aAAa;MAAEM;IAAa,CAAC,GAAG,IAAI,CAACG,KAAK;IAClG,MAAM;MAAE+F,eAAe;MAAEC;IAAU,CAAC,GAAGvH,sBAAsB,CAACS,QAAQ,EAAEC,QAAQ,EAAEG,eAAe,EAAEC,aAAa,EAAEM,WAAW,EAAE,IAAI,CAACG,KAAK,CAACiG,eAAe,CAAC;IAC1J,MAAMC,UAAU,GAAG,IAAI,CAAClG,KAAK,CAACN,yBAAyB,GAAG,CAAC,GACrD,CAAC,GACDyG,IAAI,CAACC,KAAK,CAAC,IAAI,CAACpG,KAAK,CAACN,yBAAyB,CAAC;IACtD,MAAM;MAAE2G,UAAU;MAAEC;IAAO,CAAC,GAAGhI,mBAAmB,CAACyH,eAAe,EAAEC,SAAS,EAAEE,UAAU,CAAC;IAC1F,MAAMK,SAAS,GAAGR,eAAe,CAAC5E,GAAG,CAAC,CAACyD,IAAI,EAAE4B,SAAS,KAAK;MACvD,IAAI,IAAI,CAACxG,KAAK,CAACL,YAAY,EAAE;QACzB,MAAM8G,UAAU,GAAGJ,UAAU,CAACG,SAAS,CAAC;QACxC,IAAIC,UAAU,KAAKrG,SAAS,EAAE;UAC1B,MAAMsG,eAAe,GAAGJ,MAAM,CAACG,UAAU,CAAC,CAACE,OAAO,KAAKH,SAAS;UAChE,IAAI,CAAC,IAAI,CAACvG,KAAK,CAACC,cAAc,CAACqC,QAAQ,CAACkE,UAAU,CAAC,IAC/CC,eAAe,EAAE;YACjB,OAAQ5I,IAAI,CAACM,KAAK,CAACH,QAAQ,EAAE;cAAE2D,QAAQ,EAAE,IAAI,CAACoD,0BAA0B,CAACsB,MAAM,CAACG,UAAU,CAAC,CAACG,KAAK,EAAEH,UAAU,EAAE7B,IAAI,CAAChB,IAAI,CAAC5B,UAAU,EAAE4C,IAAI,CAACd,KAAK,CAAC9B,UAAU;YAAE,CAAC,EAAEwE,SAAS,CAAC;UAC7K;UACA,IAAI,CAAC,IAAI,CAACvG,KAAK,CAACC,cAAc,CAACqC,QAAQ,CAACkE,UAAU,CAAC,EAAE;YACjD,OAAO,IAAI;UACf;QACJ;MACJ;MACA,OAAOrH,SAAS,GACV,IAAI,CAACsF,eAAe,CAACE,IAAI,EAAE4B,SAAS,CAAC,GACrC,IAAI,CAAC3B,gBAAgB,CAACD,IAAI,EAAE4B,SAAS,CAAC;IAChD,CAAC,CAAC;IACF,OAAO;MACHD,SAAS;MACTD,MAAM;MACNP;IACJ,CAAC;EACL,CAAC;EACDc,MAAM,GAAGA,CAAA,KAAM;IACX,MAAM;MAAE3H,QAAQ;MAAEC,QAAQ;MAAES,YAAY;MAAEkH,SAAS;MAAEC,UAAU;MAAE3H,SAAS;MAAEG,aAAa;MAAEE,eAAe;MAAEK;IAAO,CAAC,GAAG,IAAI,CAACE,KAAK;IACjI,IAAI,OAAOT,aAAa,KAAK,QAAQ,IACjCA,aAAa,KAAKhB,UAAU,CAACyI,IAAI,EAAE;MACnC,IAAI,OAAO9H,QAAQ,KAAK,QAAQ,IAAI,OAAOC,QAAQ,KAAK,QAAQ,EAAE;QAC9D,MAAM8H,KAAK,CAAC,6CAA6C,CAAC;MAC9D;IACJ;IACA,IAAI,CAACjI,MAAM,GAAG,IAAI,CAACL,aAAa,CAAC,IAAI,CAACqB,KAAK,CAAChB,MAAM,EAAEY,YAAY,EAAEE,KAAK,CAAC;IACxE,MAAMoH,KAAK,GAAG,IAAI,CAACpB,UAAU,CAAC,CAAC;IAC/B,IAAIqB,kBAAkB,GAAG,CAAC;IAC1B,IAAIC,mBAAmB,GAAG,CAAC;IAC3B,IAAI3H,eAAe,EAAE;MACjB0H,kBAAkB,IAAI,CAAC;MACvBC,mBAAmB,IAAI,CAAC;IAC5B;IACA,IAAI,IAAI,CAACpH,KAAK,CAACwD,YAAY,EAAE;MACzB2D,kBAAkB,IAAI,CAAC;MACvBC,mBAAmB,IAAI,CAAC;IAC5B;IACA,IAAIC,SAAS,GAAG,CAAC;IACjB,IAAIC,SAAS,GAAG,CAAC;IACjB,KAAK,MAAMC,CAAC,IAAIL,KAAK,CAACnB,eAAe,EAAE;MACnC,IAAIwB,CAAC,CAAC3D,IAAI,CAACpC,IAAI,KAAKhD,QAAQ,CAACiD,KAAK,EAAE;QAChC6F,SAAS,EAAE;MACf;MACA,IAAIC,CAAC,CAACzD,KAAK,CAACtC,IAAI,KAAKhD,QAAQ,CAACiD,KAAK,EAAE;QACjC6F,SAAS,EAAE;MACf;MACA,IAAIC,CAAC,CAAC3D,IAAI,CAACpC,IAAI,KAAKhD,QAAQ,CAACqD,OAAO,EAAE;QAClCwF,SAAS,EAAE;MACf;MACA,IAAIE,CAAC,CAACzD,KAAK,CAACtC,IAAI,KAAKhD,QAAQ,CAACqD,OAAO,EAAE;QACnCwF,SAAS,EAAE;MACf;IACJ;IACA,MAAMG,YAAY,GAAGH,SAAS,GAAGC,SAAS;IAC1C,MAAMG,kBAAkB,GAAGtB,IAAI,CAACC,KAAK,CAAEkB,SAAS,GAAGE,YAAY,GAAI,GAAG,CAAC;IACvE,MAAMlB,MAAM,GAAG,EAAE;IACjB,KAAK,IAAIjF,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,CAAC,EAAEA,CAAC,EAAE,EAAE;MACxB,IAAIoG,kBAAkB,GAAGpG,CAAC,GAAG,EAAE,EAAE;QAC7BiF,MAAM,CAAC1F,IAAI,CAAC9C,IAAI,CAAC,MAAM,EAAE;UAAE4D,SAAS,EAAEvD,EAAE,CAAC,IAAI,CAACa,MAAM,CAAC0I,KAAK,EAAE,IAAI,CAAC1I,MAAM,CAAC2I,aAAa;QAAE,CAAC,EAAEtG,CAAC,CAAC,CAAC;MACjG,CAAC,MACI;QACDiF,MAAM,CAAC1F,IAAI,CAAC9C,IAAI,CAAC,MAAM,EAAE;UAAE4D,SAAS,EAAEvD,EAAE,CAAC,IAAI,CAACa,MAAM,CAAC0I,KAAK,EAAE,IAAI,CAAC1I,MAAM,CAAC4I,aAAa;QAAE,CAAC,EAAEvG,CAAC,CAAC,CAAC;MACjG;IACJ;IACA,MAAMwG,WAAW,GAAG,IAAI,CAAC5H,KAAK,CAACC,cAAc,CAACI,MAAM,KAAK4G,KAAK,CAACZ,MAAM,CAAChG,MAAM;IAC5E,OAAQtC,KAAK,CAAC,KAAK,EAAE;MAAE4D,QAAQ,EAAE,CAAC5D,KAAK,CAAC,KAAK,EAAE;QAAE0D,SAAS,EAAE,IAAI,CAAC1C,MAAM,CAAC8I,OAAO;QAAEC,IAAI,EAAE,QAAQ;QAAEnG,QAAQ,EAAE,CAAC9D,IAAI,CAAC,QAAQ,EAAE;UAAE0D,IAAI,EAAE,QAAQ;UAAEE,SAAS,EAAE,IAAI,CAAC1C,MAAM,CAACgJ,eAAe;UAAE/E,OAAO,EAAEA,CAAA,KAAM;YAC5K,IAAI,CAAC1C,QAAQ,CAAC;cACVL,cAAc,EAAE2H,WAAW,GACrB,EAAE,GACFX,KAAK,CAACZ,MAAM,CAACnF,GAAG,CAAE8G,CAAC,IAAKA,CAAC,CAACtD,KAAK;YACzC,CAAC,CAAC;UACN,CAAC;UAAE/C,QAAQ,EAAEiG,WAAW,GAAG/J,IAAI,CAACc,IAAI,EAAE,CAAC,CAAC,CAAC,GAAGd,IAAI,CAACY,MAAM,EAAE,CAAC,CAAC;QAAE,CAAC,CAAC,EAAE,GAAG,EAAE8I,YAAY,EAAE1J,IAAI,CAAC,KAAK,EAAE;UAAEoK,KAAK,EAAE;YAAEC,OAAO,EAAE,MAAM;YAAEC,GAAG,EAAE;UAAM,CAAC;UAAExG,QAAQ,EAAE0E;QAAO,CAAC,CAAC,EAAE,IAAI,CAACtG,KAAK,CAAC8H,OAAO,GAAGhK,IAAI,CAAC,MAAM,EAAE;UAAE8D,QAAQ,EAAE,IAAI,CAAC5B,KAAK,CAAC8H;QAAQ,CAAC,CAAC,GAAG,IAAI;MAAE,CAAC,CAAC,EAAEhK,IAAI,CAAC,OAAO,EAAE;QAAE4D,SAAS,EAAEvD,EAAE,CAAC,IAAI,CAACa,MAAM,CAACqJ,aAAa,EAAE;UAC5S,CAAC,IAAI,CAACrJ,MAAM,CAACI,SAAS,GAAGA;QAC7B,CAAC,CAAC;QAAEkJ,SAAS,EAAEA,CAAA,KAAM;UACjB,MAAMrE,QAAQ,GAAGC,QAAQ,CAACC,sBAAsB,CAAC,OAAO,CAAC;UACzD,KAAK,IAAI9C,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG4C,QAAQ,CAAC3D,MAAM,EAAEe,CAAC,EAAE,EAAE;YACtC,MAAM+C,OAAO,GAAGH,QAAQ,CAACI,IAAI,CAAChD,CAAC,CAAC;YAChC+C,OAAO,CAACE,SAAS,CAACiE,MAAM,CAAC,IAAI,CAACvJ,MAAM,CAACmB,QAAQ,CAAC;UAClD;UACA,MAAMqI,YAAY,GAAGtE,QAAQ,CAACC,sBAAsB,CAAC,MAAM,CAAC;UAC5D,KAAK,IAAI9C,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGmH,YAAY,CAAClI,MAAM,EAAEe,CAAC,EAAE,EAAE;YAC1C,MAAM+C,OAAO,GAAGoE,YAAY,CAACnE,IAAI,CAAChD,CAAC,CAAC;YACpC+C,OAAO,CAACE,SAAS,CAACiE,MAAM,CAAC,IAAI,CAACvJ,MAAM,CAACmB,QAAQ,CAAC;UAClD;QACJ,CAAC;QAAEyB,QAAQ,EAAE5D,KAAK,CAAC,OAAO,EAAE;UAAE4D,QAAQ,EAAE,CAAC5D,KAAK,CAAC,IAAI,EAAE;YAAE4D,QAAQ,EAAE,CAAC,CAAC,IAAI,CAAC5B,KAAK,CAACP,eAAe,GAAG3B,IAAI,CAAC,IAAI,EAAE;cAAE2K,KAAK,EAAE;YAAO,CAAC,CAAC,GAAG,IAAI,EAAE,CAACrJ,SAAS,IAAI,CAAC,IAAI,CAACY,KAAK,CAACP,eAAe,GAAI3B,IAAI,CAAC,IAAI,EAAE;cAAE2K,KAAK,EAAE;YAAO,CAAC,CAAC,GAAI,IAAI,EAAE,IAAI,CAACzI,KAAK,CAACwD,YAAY,GAAG1F,IAAI,CAAC,IAAI,EAAE;cAAE2K,KAAK,EAAE;YAAO,CAAC,CAAC,GAAG,IAAI,EAAE3K,IAAI,CAAC,IAAI,EAAE;cAAE2K,KAAK,EAAE;YAAO,CAAC,CAAC,EAAE3K,IAAI,CAAC,IAAI,EAAE;cAAE2K,KAAK,EAAE;YAAO,CAAC,CAAC,EAAErJ,SAAS,GAAIpB,KAAK,CAACE,SAAS,EAAE;cAAE0D,QAAQ,EAAE,CAAC,CAAC,IAAI,CAAC5B,KAAK,CAACP,eAAe,GAAG3B,IAAI,CAAC,IAAI,EAAE;gBAAE2K,KAAK,EAAE;cAAO,CAAC,CAAC,GAAG,IAAI,EAAE,IAAI,CAACzI,KAAK,CAACwD,YAAY,GAAG1F,IAAI,CAAC,IAAI,EAAE;gBAAE2K,KAAK,EAAE;cAAO,CAAC,CAAC,GAAG,IAAI,EAAE3K,IAAI,CAAC,IAAI,EAAE;gBAAE2K,KAAK,EAAE;cAAO,CAAC,CAAC,EAAE3K,IAAI,CAAC,IAAI,EAAE;gBAAE2K,KAAK,EAAE;cAAO,CAAC,CAAC;YAAE,CAAC,CAAC,GAAI,IAAI;UAAE,CAAC,CAAC,EAAE3B,SAAS,IAAIC,UAAU,GAAI/I,KAAK,CAAC,IAAI,EAAE;YAAE4D,QAAQ,EAAE,CAAC9D,IAAI,CAAC,IAAI,EAAE;cAAE4K,OAAO,EAAEtJ,SAAS,GAAG+H,kBAAkB,GAAGC,mBAAmB;cAAE1F,SAAS,EAAEvD,EAAE,CAAC,IAAI,CAACa,MAAM,CAAC2J,UAAU,EAAE,IAAI,CAAC3J,MAAM,CAAC4J,MAAM,CAAC;cAAEhH,QAAQ,EAAEkF,SAAS,GAAIhJ,IAAI,CAAC,KAAK,EAAE;gBAAE4D,SAAS,EAAE,IAAI,CAAC1C,MAAM,CAACyF,WAAW;gBAAE7C,QAAQ,EAAEkF;cAAU,CAAC,CAAC,GAAI;YAAK,CAAC,CAAC,EAAE1H,SAAS,GAAItB,IAAI,CAAC,IAAI,EAAE;cAAE4K,OAAO,EAAEvB,kBAAkB;cAAEzF,SAAS,EAAEvD,EAAE,CAAC,IAAI,CAACa,MAAM,CAAC2J,UAAU,EAAE,IAAI,CAAC3J,MAAM,CAAC4J,MAAM,CAAC;cAAEhH,QAAQ,EAAEmF,UAAU,GAAIjJ,IAAI,CAAC,KAAK,EAAE;gBAAE4D,SAAS,EAAE,IAAI,CAAC1C,MAAM,CAACyF,WAAW;gBAAE7C,QAAQ,EAAEmF;cAAW,CAAC,CAAC,GAAI;YAAK,CAAC,CAAC,GAAI,IAAI;UAAE,CAAC,CAAC,GAAI,IAAI,EAAEG,KAAK,CAACX,SAAS;QAAE,CAAC;MAAE,CAAC,CAAC;IAAE,CAAC,CAAC;EAC1pC,CAAC;AACL;AACA,eAAezH,UAAU;AACzB,SAASP,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}