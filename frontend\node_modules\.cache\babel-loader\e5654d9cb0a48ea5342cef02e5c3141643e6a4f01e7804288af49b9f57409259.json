{"ast": null, "code": "var _jsxFileName = \"D:\\\\Documents_2\\\\New Coding\\\\CoderBuddy\\\\CoderBuddy\\\\frontend\\\\src\\\\components\\\\EditorTabs.js\",\n  _s = $RefreshSig$();\nimport React, { useState } from 'react';\nimport { readFile, writeFile } from '../services/api';\nimport Editor, { DiffEditor } from '@monaco-editor/react';\nimport { useHotkeys } from 'react-hotkeys-hook';\nimport NiceModal from '@ebay/nice-modal-react';\nimport SaveDiffModal from './SaveDiffModal';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nexport default function EditorTabs({\n  selectedFile,\n  projectId\n}) {\n  _s();\n  const [tabs, setTabs] = useState([]);\n  const [activePath, setActivePath] = useState(null);\n  React.useEffect(() => {\n    if (selectedFile) {\n      openFile(selectedFile);\n    }\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n  }, [selectedFile, projectId]);\n  React.useEffect(() => {\n    setTabs([]);\n    setActivePath(null);\n  }, [projectId]);\n  async function openFile(path) {\n    // if already open, just activate\n    const existing = tabs.find(t => t.path === path);\n    if (existing) {\n      setActivePath(path);\n      return;\n    }\n    try {\n      const {\n        content\n      } = await readFile(path, projectId);\n      setTabs(prev => [...prev, {\n        path,\n        content,\n        savedContent: content,\n        diffMode: false\n      }]);\n      setActivePath(path);\n    } catch (e) {\n      alert(`Failed to open file: ${e.message}`);\n    }\n  }\n  function handleChange(value = '') {\n    setTabs(prev => prev.map(t => t.path === activePath ? {\n      ...t,\n      content: value\n    } : t));\n  }\n  const handleSave = async () => {\n    if (!activePath) return;\n    const tab = tabs.find(t => t.path === activePath);\n    if (!tab) return;\n\n    // If not in diff mode yet and file is dirty -> open diff view\n    if (!tab.diffMode) {\n      if (tab.content === tab.savedContent) return; // nothing to save\n      setTabs(prev => prev.map(t => t.path === tab.path ? {\n        ...t,\n        diffMode: true\n      } : t));\n      return;\n    }\n\n    // In diff mode -> actually persist\n    try {\n      await writeFile(tab.path, tab.content, true, projectId);\n      setTabs(prev => prev.map(t => t.path === tab.path ? {\n        ...t,\n        savedContent: t.content,\n        diffMode: false\n      } : t));\n    } catch (e) {\n      console.error(e);\n      alert('Save failed: ' + e.message);\n    }\n  };\n\n  // Cancel diff -> revert content\n  const handleCancel = () => {\n    if (!activePath) return;\n    setTabs(prev => prev.map(t => t.path === activePath ? {\n      ...t,\n      content: t.savedContent,\n      diffMode: false\n    } : t));\n  };\n\n  // Ctrl+S hotkey\n  useHotkeys('ctrl+s, command+s', e => {\n    e.preventDefault();\n    handleSave();\n  }, [tabs, activePath]);\n\n  // ESC to cancel when diffMode\n  useHotkeys('esc', () => {\n    const tab = tabs.find(t => t.path === activePath);\n    if (tab !== null && tab !== void 0 && tab.diffMode) handleCancel();\n  }, [tabs, activePath]);\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    style: {\n      flex: 1,\n      display: 'flex',\n      flexDirection: 'column'\n    },\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        display: 'flex',\n        borderBottom: '1px solid #ccc',\n        overflowX: 'auto',\n        alignItems: 'center'\n      },\n      children: [tabs.map(tab => /*#__PURE__*/_jsxDEV(\"div\", {\n        onClick: () => setActivePath(tab.path),\n        style: {\n          padding: '4px 8px',\n          cursor: 'pointer',\n          backgroundColor: tab.path === activePath ? '#eee' : 'transparent'\n        },\n        children: tab.path.split('/').pop()\n      }, tab.path, false, {\n        fileName: _jsxFileName,\n        lineNumber: 111,\n        columnNumber: 11\n      }, this)), activePath && /*#__PURE__*/_jsxDEV(\"button\", {\n        style: {\n          marginLeft: 'auto',\n          marginRight: 8\n        },\n        onClick: handleSave,\n        children: \"Save\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 124,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 109,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        flex: 1,\n        position: 'relative'\n      },\n      children: tabs.length > 0 && activePath && (() => {\n        const current = tabs.find(t => t.path === activePath);\n        if (!current) return null;\n        if (current.diffMode) {\n          return /*#__PURE__*/_jsxDEV(_Fragment, {\n            children: [/*#__PURE__*/_jsxDEV(DiffEditor, {\n              height: \"100%\",\n              original: current.savedContent,\n              modified: current.content,\n              language: \"markdown\",\n              options: {\n                renderSideBySide: true,\n                fontSize: 14\n              },\n              onChange: v => handleChange(v !== null && v !== void 0 ? v : '')\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 136,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                position: 'absolute',\n                top: 8,\n                right: 8\n              },\n              children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                style: {\n                  marginRight: 8\n                },\n                onClick: handleSave,\n                children: \"Save\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 145,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                onClick: handleCancel,\n                children: \"Cancel\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 146,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 144,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true);\n        }\n        return /*#__PURE__*/_jsxDEV(Editor, {\n          height: \"100%\",\n          defaultLanguage: \"markdown\",\n          value: current.content,\n          onChange: v => handleChange(v !== null && v !== void 0 ? v : ''),\n          options: {\n            fontSize: 14\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 152,\n          columnNumber: 13\n        }, this);\n      })()\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 129,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 108,\n    columnNumber: 5\n  }, this);\n}\n_s(EditorTabs, \"lDcmKek4Mp9tUhehy9Gm2CPszFE=\", false, function () {\n  return [useHotkeys, useHotkeys];\n});\n_c = EditorTabs;\nvar _c;\n$RefreshReg$(_c, \"EditorTabs\");", "map": {"version": 3, "names": ["React", "useState", "readFile", "writeFile", "Editor", "DiffE<PERSON>or", "useHotkeys", "NiceModal", "SaveDiffModal", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "EditorTabs", "selectedFile", "projectId", "_s", "tabs", "setTabs", "activePath", "setActivePath", "useEffect", "openFile", "path", "existing", "find", "t", "content", "prev", "saved<PERSON><PERSON>nt", "diffMode", "e", "alert", "message", "handleChange", "value", "map", "handleSave", "tab", "console", "error", "handleCancel", "preventDefault", "style", "flex", "display", "flexDirection", "children", "borderBottom", "overflowX", "alignItems", "onClick", "padding", "cursor", "backgroundColor", "split", "pop", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "marginLeft", "marginRight", "position", "length", "current", "height", "original", "modified", "language", "options", "renderSideBySide", "fontSize", "onChange", "v", "top", "right", "defaultLanguage", "_c", "$RefreshReg$"], "sources": ["D:/Documents_2/New Coding/CoderBuddy/CoderBuddy/frontend/src/components/EditorTabs.js"], "sourcesContent": ["import React, { useState } from 'react';\r\nimport { readFile, writeFile } from '../services/api';\r\nimport Editor, { DiffEditor } from '@monaco-editor/react';\r\nimport { useHotkeys } from 'react-hotkeys-hook';\r\nimport NiceModal from '@ebay/nice-modal-react';\r\nimport SaveDiffModal from './SaveDiffModal';\r\n\r\nexport default function EditorTabs({ selectedFile, projectId }) {\r\n  const [tabs, setTabs] = useState([]);\r\n  const [activePath, setActivePath] = useState(null);\r\n\r\n  React.useEffect(() => {\r\n    if (selectedFile) {\r\n      openFile(selectedFile);\r\n    }\r\n    // eslint-disable-next-line react-hooks/exhaustive-deps\r\n  }, [selectedFile, projectId]);\r\n\r\n  React.useEffect(() => {\r\n    setTabs([]);\r\n    setActivePath(null);\r\n  }, [projectId]);\r\n\r\n  async function openFile(path) {\r\n    // if already open, just activate\r\n    const existing = tabs.find((t) => t.path === path);\r\n    if (existing) {\r\n      setActivePath(path);\r\n      return;\r\n    }\r\n    try {\r\n      const { content } = await readFile(path, projectId);\r\n      setTabs((prev) => [\r\n        ...prev,\r\n        { path, content, savedContent: content, diffMode: false },\r\n      ]);\r\n      setActivePath(path);\r\n    } catch (e) {\r\n      alert(`Failed to open file: ${e.message}`);\r\n    }\r\n  }\r\n\r\n  function handleChange(value = '') {\r\n    setTabs((prev) =>\r\n      prev.map((t) =>\r\n        t.path === activePath ? { ...t, content: value } : t\r\n      )\r\n    );\r\n  }\r\n\r\n  const handleSave = async () => {\r\n    if (!activePath) return;\r\n    const tab = tabs.find((t) => t.path === activePath);\r\n    if (!tab) return;\r\n\r\n    // If not in diff mode yet and file is dirty -> open diff view\r\n    if (!tab.diffMode) {\r\n      if (tab.content === tab.savedContent) return; // nothing to save\r\n      setTabs((prev) =>\r\n        prev.map((t) =>\r\n          t.path === tab.path ? { ...t, diffMode: true } : t\r\n        )\r\n      );\r\n      return;\r\n    }\r\n\r\n    // In diff mode -> actually persist\r\n    try {\r\n      await writeFile(tab.path, tab.content, true, projectId);\r\n      setTabs((prev) =>\r\n        prev.map((t) =>\r\n          t.path === tab.path\r\n            ? { ...t, savedContent: t.content, diffMode: false }\r\n            : t\r\n        )\r\n      );\r\n    } catch (e) {\r\n      console.error(e);\r\n      alert('Save failed: ' + e.message);\r\n    }\r\n  };\r\n\r\n  // Cancel diff -> revert content\r\n  const handleCancel = () => {\r\n    if (!activePath) return;\r\n    setTabs((prev) =>\r\n      prev.map((t) =>\r\n        t.path === activePath\r\n          ? { ...t, content: t.savedContent, diffMode: false }\r\n          : t\r\n      )\r\n    );\r\n  };\r\n\r\n  // Ctrl+S hotkey\r\n  useHotkeys('ctrl+s, command+s', (e) => {\r\n    e.preventDefault();\r\n    handleSave();\r\n  }, [tabs, activePath]);\r\n\r\n  // ESC to cancel when diffMode\r\n  useHotkeys('esc', () => {\r\n    const tab = tabs.find((t) => t.path === activePath);\r\n    if (tab?.diffMode) handleCancel();\r\n  }, [tabs, activePath]);\r\n\r\n  return (\r\n    <div style={{ flex: 1, display: 'flex', flexDirection: 'column' }}>\r\n      <div style={{ display: 'flex', borderBottom: '1px solid #ccc', overflowX: 'auto', alignItems: 'center' }}>\r\n        {tabs.map((tab) => (\r\n          <div\r\n            key={tab.path}\r\n            onClick={() => setActivePath(tab.path)}\r\n            style={{\r\n              padding: '4px 8px',\r\n              cursor: 'pointer',\r\n              backgroundColor: tab.path === activePath ? '#eee' : 'transparent',\r\n            }}\r\n          >\r\n            {tab.path.split('/').pop()}\r\n          </div>\r\n        ))}\r\n        {activePath && (\r\n          <button style={{ marginLeft: 'auto', marginRight: 8 }} onClick={handleSave}>\r\n            Save\r\n          </button>\r\n        )}\r\n      </div>\r\n      <div style={{ flex: 1, position: 'relative' }}>\r\n        {tabs.length > 0 && activePath && (() => {\r\n          const current = tabs.find((t) => t.path === activePath);\r\n          if (!current) return null;\r\n          if (current.diffMode) {\r\n            return (\r\n              <>\r\n                <DiffEditor\r\n                  height=\"100%\"\r\n                  original={current.savedContent}\r\n                  modified={current.content}\r\n                  language=\"markdown\"\r\n                  options={{ renderSideBySide: true, fontSize: 14 }}\r\n                  onChange={(v) => handleChange(v ?? '')}\r\n                />\r\n                <div style={{ position: 'absolute', top: 8, right: 8 }}>\r\n                  <button style={{ marginRight: 8 }} onClick={handleSave}>Save</button>\r\n                  <button onClick={handleCancel}>Cancel</button>\r\n                </div>\r\n              </>\r\n            );\r\n          }\r\n          return (\r\n            <Editor\r\n              height=\"100%\"\r\n              defaultLanguage=\"markdown\"\r\n              value={current.content}\r\n              onChange={(v) => handleChange(v ?? '')}\r\n              options={{ fontSize: 14 }}\r\n            />\r\n          );\r\n        })()}\r\n      </div>\r\n    </div>\r\n  );\r\n} "], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,SAASC,QAAQ,EAAEC,SAAS,QAAQ,iBAAiB;AACrD,OAAOC,MAAM,IAAIC,UAAU,QAAQ,sBAAsB;AACzD,SAASC,UAAU,QAAQ,oBAAoB;AAC/C,OAAOC,SAAS,MAAM,wBAAwB;AAC9C,OAAOC,aAAa,MAAM,iBAAiB;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAE5C,eAAe,SAASC,UAAUA,CAAC;EAAEC,YAAY;EAAEC;AAAU,CAAC,EAAE;EAAAC,EAAA;EAC9D,MAAM,CAACC,IAAI,EAAEC,OAAO,CAAC,GAAGjB,QAAQ,CAAC,EAAE,CAAC;EACpC,MAAM,CAACkB,UAAU,EAAEC,aAAa,CAAC,GAAGnB,QAAQ,CAAC,IAAI,CAAC;EAElDD,KAAK,CAACqB,SAAS,CAAC,MAAM;IACpB,IAAIP,YAAY,EAAE;MAChBQ,QAAQ,CAACR,YAAY,CAAC;IACxB;IACA;EACF,CAAC,EAAE,CAACA,YAAY,EAAEC,SAAS,CAAC,CAAC;EAE7Bf,KAAK,CAACqB,SAAS,CAAC,MAAM;IACpBH,OAAO,CAAC,EAAE,CAAC;IACXE,aAAa,CAAC,IAAI,CAAC;EACrB,CAAC,EAAE,CAACL,SAAS,CAAC,CAAC;EAEf,eAAeO,QAAQA,CAACC,IAAI,EAAE;IAC5B;IACA,MAAMC,QAAQ,GAAGP,IAAI,CAACQ,IAAI,CAAEC,CAAC,IAAKA,CAAC,CAACH,IAAI,KAAKA,IAAI,CAAC;IAClD,IAAIC,QAAQ,EAAE;MACZJ,aAAa,CAACG,IAAI,CAAC;MACnB;IACF;IACA,IAAI;MACF,MAAM;QAAEI;MAAQ,CAAC,GAAG,MAAMzB,QAAQ,CAACqB,IAAI,EAAER,SAAS,CAAC;MACnDG,OAAO,CAAEU,IAAI,IAAK,CAChB,GAAGA,IAAI,EACP;QAAEL,IAAI;QAAEI,OAAO;QAAEE,YAAY,EAAEF,OAAO;QAAEG,QAAQ,EAAE;MAAM,CAAC,CAC1D,CAAC;MACFV,aAAa,CAACG,IAAI,CAAC;IACrB,CAAC,CAAC,OAAOQ,CAAC,EAAE;MACVC,KAAK,CAAC,wBAAwBD,CAAC,CAACE,OAAO,EAAE,CAAC;IAC5C;EACF;EAEA,SAASC,YAAYA,CAACC,KAAK,GAAG,EAAE,EAAE;IAChCjB,OAAO,CAAEU,IAAI,IACXA,IAAI,CAACQ,GAAG,CAAEV,CAAC,IACTA,CAAC,CAACH,IAAI,KAAKJ,UAAU,GAAG;MAAE,GAAGO,CAAC;MAAEC,OAAO,EAAEQ;IAAM,CAAC,GAAGT,CACrD,CACF,CAAC;EACH;EAEA,MAAMW,UAAU,GAAG,MAAAA,CAAA,KAAY;IAC7B,IAAI,CAAClB,UAAU,EAAE;IACjB,MAAMmB,GAAG,GAAGrB,IAAI,CAACQ,IAAI,CAAEC,CAAC,IAAKA,CAAC,CAACH,IAAI,KAAKJ,UAAU,CAAC;IACnD,IAAI,CAACmB,GAAG,EAAE;;IAEV;IACA,IAAI,CAACA,GAAG,CAACR,QAAQ,EAAE;MACjB,IAAIQ,GAAG,CAACX,OAAO,KAAKW,GAAG,CAACT,YAAY,EAAE,OAAO,CAAC;MAC9CX,OAAO,CAAEU,IAAI,IACXA,IAAI,CAACQ,GAAG,CAAEV,CAAC,IACTA,CAAC,CAACH,IAAI,KAAKe,GAAG,CAACf,IAAI,GAAG;QAAE,GAAGG,CAAC;QAAEI,QAAQ,EAAE;MAAK,CAAC,GAAGJ,CACnD,CACF,CAAC;MACD;IACF;;IAEA;IACA,IAAI;MACF,MAAMvB,SAAS,CAACmC,GAAG,CAACf,IAAI,EAAEe,GAAG,CAACX,OAAO,EAAE,IAAI,EAAEZ,SAAS,CAAC;MACvDG,OAAO,CAAEU,IAAI,IACXA,IAAI,CAACQ,GAAG,CAAEV,CAAC,IACTA,CAAC,CAACH,IAAI,KAAKe,GAAG,CAACf,IAAI,GACf;QAAE,GAAGG,CAAC;QAAEG,YAAY,EAAEH,CAAC,CAACC,OAAO;QAAEG,QAAQ,EAAE;MAAM,CAAC,GAClDJ,CACN,CACF,CAAC;IACH,CAAC,CAAC,OAAOK,CAAC,EAAE;MACVQ,OAAO,CAACC,KAAK,CAACT,CAAC,CAAC;MAChBC,KAAK,CAAC,eAAe,GAAGD,CAAC,CAACE,OAAO,CAAC;IACpC;EACF,CAAC;;EAED;EACA,MAAMQ,YAAY,GAAGA,CAAA,KAAM;IACzB,IAAI,CAACtB,UAAU,EAAE;IACjBD,OAAO,CAAEU,IAAI,IACXA,IAAI,CAACQ,GAAG,CAAEV,CAAC,IACTA,CAAC,CAACH,IAAI,KAAKJ,UAAU,GACjB;MAAE,GAAGO,CAAC;MAAEC,OAAO,EAAED,CAAC,CAACG,YAAY;MAAEC,QAAQ,EAAE;IAAM,CAAC,GAClDJ,CACN,CACF,CAAC;EACH,CAAC;;EAED;EACApB,UAAU,CAAC,mBAAmB,EAAGyB,CAAC,IAAK;IACrCA,CAAC,CAACW,cAAc,CAAC,CAAC;IAClBL,UAAU,CAAC,CAAC;EACd,CAAC,EAAE,CAACpB,IAAI,EAAEE,UAAU,CAAC,CAAC;;EAEtB;EACAb,UAAU,CAAC,KAAK,EAAE,MAAM;IACtB,MAAMgC,GAAG,GAAGrB,IAAI,CAACQ,IAAI,CAAEC,CAAC,IAAKA,CAAC,CAACH,IAAI,KAAKJ,UAAU,CAAC;IACnD,IAAImB,GAAG,aAAHA,GAAG,eAAHA,GAAG,CAAER,QAAQ,EAAEW,YAAY,CAAC,CAAC;EACnC,CAAC,EAAE,CAACxB,IAAI,EAAEE,UAAU,CAAC,CAAC;EAEtB,oBACET,OAAA;IAAKiC,KAAK,EAAE;MAAEC,IAAI,EAAE,CAAC;MAAEC,OAAO,EAAE,MAAM;MAAEC,aAAa,EAAE;IAAS,CAAE;IAAAC,QAAA,gBAChErC,OAAA;MAAKiC,KAAK,EAAE;QAAEE,OAAO,EAAE,MAAM;QAAEG,YAAY,EAAE,gBAAgB;QAAEC,SAAS,EAAE,MAAM;QAAEC,UAAU,EAAE;MAAS,CAAE;MAAAH,QAAA,GACtG9B,IAAI,CAACmB,GAAG,CAAEE,GAAG,iBACZ5B,OAAA;QAEEyC,OAAO,EAAEA,CAAA,KAAM/B,aAAa,CAACkB,GAAG,CAACf,IAAI,CAAE;QACvCoB,KAAK,EAAE;UACLS,OAAO,EAAE,SAAS;UAClBC,MAAM,EAAE,SAAS;UACjBC,eAAe,EAAEhB,GAAG,CAACf,IAAI,KAAKJ,UAAU,GAAG,MAAM,GAAG;QACtD,CAAE;QAAA4B,QAAA,EAEDT,GAAG,CAACf,IAAI,CAACgC,KAAK,CAAC,GAAG,CAAC,CAACC,GAAG,CAAC;MAAC,GARrBlB,GAAG,CAACf,IAAI;QAAAkC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OASV,CACN,CAAC,EACDzC,UAAU,iBACTT,OAAA;QAAQiC,KAAK,EAAE;UAAEkB,UAAU,EAAE,MAAM;UAAEC,WAAW,EAAE;QAAE,CAAE;QAACX,OAAO,EAAEd,UAAW;QAAAU,QAAA,EAAC;MAE5E;QAAAU,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CACT;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC,eACNlD,OAAA;MAAKiC,KAAK,EAAE;QAAEC,IAAI,EAAE,CAAC;QAAEmB,QAAQ,EAAE;MAAW,CAAE;MAAAhB,QAAA,EAC3C9B,IAAI,CAAC+C,MAAM,GAAG,CAAC,IAAI7C,UAAU,IAAI,CAAC,MAAM;QACvC,MAAM8C,OAAO,GAAGhD,IAAI,CAACQ,IAAI,CAAEC,CAAC,IAAKA,CAAC,CAACH,IAAI,KAAKJ,UAAU,CAAC;QACvD,IAAI,CAAC8C,OAAO,EAAE,OAAO,IAAI;QACzB,IAAIA,OAAO,CAACnC,QAAQ,EAAE;UACpB,oBACEpB,OAAA,CAAAE,SAAA;YAAAmC,QAAA,gBACErC,OAAA,CAACL,UAAU;cACT6D,MAAM,EAAC,MAAM;cACbC,QAAQ,EAAEF,OAAO,CAACpC,YAAa;cAC/BuC,QAAQ,EAAEH,OAAO,CAACtC,OAAQ;cAC1B0C,QAAQ,EAAC,UAAU;cACnBC,OAAO,EAAE;gBAAEC,gBAAgB,EAAE,IAAI;gBAAEC,QAAQ,EAAE;cAAG,CAAE;cAClDC,QAAQ,EAAGC,CAAC,IAAKxC,YAAY,CAACwC,CAAC,aAADA,CAAC,cAADA,CAAC,GAAI,EAAE;YAAE;cAAAjB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACxC,CAAC,eACFlD,OAAA;cAAKiC,KAAK,EAAE;gBAAEoB,QAAQ,EAAE,UAAU;gBAAEY,GAAG,EAAE,CAAC;gBAAEC,KAAK,EAAE;cAAE,CAAE;cAAA7B,QAAA,gBACrDrC,OAAA;gBAAQiC,KAAK,EAAE;kBAAEmB,WAAW,EAAE;gBAAE,CAAE;gBAACX,OAAO,EAAEd,UAAW;gBAAAU,QAAA,EAAC;cAAI;gBAAAU,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eACrElD,OAAA;gBAAQyC,OAAO,EAAEV,YAAa;gBAAAM,QAAA,EAAC;cAAM;gBAAAU,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC3C,CAAC;UAAA,eACN,CAAC;QAEP;QACA,oBACElD,OAAA,CAACN,MAAM;UACL8D,MAAM,EAAC,MAAM;UACbW,eAAe,EAAC,UAAU;UAC1B1C,KAAK,EAAE8B,OAAO,CAACtC,OAAQ;UACvB8C,QAAQ,EAAGC,CAAC,IAAKxC,YAAY,CAACwC,CAAC,aAADA,CAAC,cAADA,CAAC,GAAI,EAAE,CAAE;UACvCJ,OAAO,EAAE;YAAEE,QAAQ,EAAE;UAAG;QAAE;UAAAf,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC3B,CAAC;MAEN,CAAC,EAAE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACD,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV;AAAC5C,EAAA,CA5JuBH,UAAU;EAAA,QAwFhCP,UAAU,EAMVA,UAAU;AAAA;AAAAwE,EAAA,GA9FYjE,UAAU;AAAA,IAAAiE,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}