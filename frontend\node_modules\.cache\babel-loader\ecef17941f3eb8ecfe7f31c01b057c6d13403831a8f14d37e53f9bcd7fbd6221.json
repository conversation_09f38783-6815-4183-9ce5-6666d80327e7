{"ast": null, "code": "var _jsxFileName = \"D:\\\\Documents_2\\\\New Coding\\\\CoderBuddy\\\\CoderBuddy\\\\frontend\\\\src\\\\components\\\\ChatPanel.js\",\n  _s = $RefreshSig$();\nimport React, { useState } from 'react';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nexport default function ChatPanel() {\n  _s();\n  const [messages, setMessages] = useState([]); // {role, content}\n  const [input, setInput] = useState('');\n  const [loading, setLoading] = useState(false);\n  async function sendMessage() {\n    if (!input.trim()) return;\n    const newMsgs = [...messages, {\n      role: 'user',\n      content: input\n    }];\n    setMessages(newMsgs);\n    setInput('');\n    setLoading(true);\n    try {\n      const res = await fetch('/api/chat/completion', {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json'\n        },\n        body: JSON.stringify({\n          messages: newMsgs\n        })\n      });\n      const data = await res.json();\n      if (!res.ok) throw new Error(data.detail);\n      setMessages([...newMsgs, {\n        role: 'assistant',\n        content: data.content\n      }]);\n    } catch (e) {\n      alert(e.message);\n    } finally {\n      setLoading(false);\n    }\n  }\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    style: {\n      display: 'flex',\n      flexDirection: 'column',\n      height: '100%'\n    },\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        flex: 1,\n        overflowY: 'auto',\n        padding: 8\n      },\n      children: [messages.map((m, i) => /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          marginBottom: 8\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"b\", {\n          children: [m.role === 'user' ? 'You' : 'AI', \": \"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 35,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n          children: m.content\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 36,\n          columnNumber: 13\n        }, this)]\n      }, i, true, {\n        fileName: _jsxFileName,\n        lineNumber: 34,\n        columnNumber: 11\n      }, this)), loading && /*#__PURE__*/_jsxDEV(\"div\", {\n        children: \"Thinking...\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 39,\n        columnNumber: 21\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 32,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        display: 'flex',\n        padding: 8,\n        borderTop: '1px solid #ccc'\n      },\n      children: [/*#__PURE__*/_jsxDEV(\"input\", {\n        style: {\n          flex: 1,\n          marginRight: 8\n        },\n        value: input,\n        onChange: e => setInput(e.target.value),\n        onKeyDown: e => {\n          if (e.key === 'Enter') sendMessage();\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 42,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n        onClick: sendMessage,\n        children: \"Send\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 50,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 41,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 31,\n    columnNumber: 5\n  }, this);\n}\n_s(ChatPanel, \"QcbopfskvOtpvx/BuTIYXPrHv4s=\");\n_c = ChatPanel;\nvar _c;\n$RefreshReg$(_c, \"ChatPanel\");", "map": {"version": 3, "names": ["React", "useState", "jsxDEV", "_jsxDEV", "ChatPanel", "_s", "messages", "setMessages", "input", "setInput", "loading", "setLoading", "sendMessage", "trim", "newMsgs", "role", "content", "res", "fetch", "method", "headers", "body", "JSON", "stringify", "data", "json", "ok", "Error", "detail", "e", "alert", "message", "style", "display", "flexDirection", "height", "children", "flex", "overflowY", "padding", "map", "m", "i", "marginBottom", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "borderTop", "marginRight", "value", "onChange", "target", "onKeyDown", "key", "onClick", "_c", "$RefreshReg$"], "sources": ["D:/Documents_2/New Coding/CoderBuddy/CoderBuddy/frontend/src/components/ChatPanel.js"], "sourcesContent": ["import React, { useState } from 'react';\r\n\r\nexport default function ChatPanel() {\r\n  const [messages, setMessages] = useState([]); // {role, content}\r\n  const [input, setInput] = useState('');\r\n  const [loading, setLoading] = useState(false);\r\n\r\n  async function sendMessage() {\r\n    if (!input.trim()) return;\r\n    const newMsgs = [...messages, { role: 'user', content: input }];\r\n    setMessages(newMsgs);\r\n    setInput('');\r\n    setLoading(true);\r\n    try {\r\n      const res = await fetch('/api/chat/completion', {\r\n        method: 'POST',\r\n        headers: { 'Content-Type': 'application/json' },\r\n        body: JSON.stringify({ messages: newMsgs }),\r\n      });\r\n      const data = await res.json();\r\n      if (!res.ok) throw new Error(data.detail);\r\n      setMessages([...newMsgs, { role: 'assistant', content: data.content }]);\r\n    } catch (e) {\r\n      alert(e.message);\r\n    } finally {\r\n      setLoading(false);\r\n    }\r\n  }\r\n\r\n  return (\r\n    <div style={{ display: 'flex', flexDirection: 'column', height: '100%' }}>\r\n      <div style={{ flex: 1, overflowY: 'auto', padding: 8 }}>\r\n        {messages.map((m, i) => (\r\n          <div key={i} style={{ marginBottom: 8 }}>\r\n            <b>{m.role === 'user' ? 'You' : 'AI'}: </b>\r\n            <span>{m.content}</span>\r\n          </div>\r\n        ))}\r\n        {loading && <div>Thinking...</div>}\r\n      </div>\r\n      <div style={{ display: 'flex', padding: 8, borderTop: '1px solid #ccc' }}>\r\n        <input\r\n          style={{ flex: 1, marginRight: 8 }}\r\n          value={input}\r\n          onChange={(e) => setInput(e.target.value)}\r\n          onKeyDown={(e) => {\r\n            if (e.key === 'Enter') sendMessage();\r\n          }}\r\n        />\r\n        <button onClick={sendMessage}>Send</button>\r\n      </div>\r\n    </div>\r\n  );\r\n} "], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAExC,eAAe,SAASC,SAASA,CAAA,EAAG;EAAAC,EAAA;EAClC,MAAM,CAACC,QAAQ,EAAEC,WAAW,CAAC,GAAGN,QAAQ,CAAC,EAAE,CAAC,CAAC,CAAC;EAC9C,MAAM,CAACO,KAAK,EAAEC,QAAQ,CAAC,GAAGR,QAAQ,CAAC,EAAE,CAAC;EACtC,MAAM,CAACS,OAAO,EAAEC,UAAU,CAAC,GAAGV,QAAQ,CAAC,KAAK,CAAC;EAE7C,eAAeW,WAAWA,CAAA,EAAG;IAC3B,IAAI,CAACJ,KAAK,CAACK,IAAI,CAAC,CAAC,EAAE;IACnB,MAAMC,OAAO,GAAG,CAAC,GAAGR,QAAQ,EAAE;MAAES,IAAI,EAAE,MAAM;MAAEC,OAAO,EAAER;IAAM,CAAC,CAAC;IAC/DD,WAAW,CAACO,OAAO,CAAC;IACpBL,QAAQ,CAAC,EAAE,CAAC;IACZE,UAAU,CAAC,IAAI,CAAC;IAChB,IAAI;MACF,MAAMM,GAAG,GAAG,MAAMC,KAAK,CAAC,sBAAsB,EAAE;QAC9CC,MAAM,EAAE,MAAM;QACdC,OAAO,EAAE;UAAE,cAAc,EAAE;QAAmB,CAAC;QAC/CC,IAAI,EAAEC,IAAI,CAACC,SAAS,CAAC;UAAEjB,QAAQ,EAAEQ;QAAQ,CAAC;MAC5C,CAAC,CAAC;MACF,MAAMU,IAAI,GAAG,MAAMP,GAAG,CAACQ,IAAI,CAAC,CAAC;MAC7B,IAAI,CAACR,GAAG,CAACS,EAAE,EAAE,MAAM,IAAIC,KAAK,CAACH,IAAI,CAACI,MAAM,CAAC;MACzCrB,WAAW,CAAC,CAAC,GAAGO,OAAO,EAAE;QAAEC,IAAI,EAAE,WAAW;QAAEC,OAAO,EAAEQ,IAAI,CAACR;MAAQ,CAAC,CAAC,CAAC;IACzE,CAAC,CAAC,OAAOa,CAAC,EAAE;MACVC,KAAK,CAACD,CAAC,CAACE,OAAO,CAAC;IAClB,CAAC,SAAS;MACRpB,UAAU,CAAC,KAAK,CAAC;IACnB;EACF;EAEA,oBACER,OAAA;IAAK6B,KAAK,EAAE;MAAEC,OAAO,EAAE,MAAM;MAAEC,aAAa,EAAE,QAAQ;MAAEC,MAAM,EAAE;IAAO,CAAE;IAAAC,QAAA,gBACvEjC,OAAA;MAAK6B,KAAK,EAAE;QAAEK,IAAI,EAAE,CAAC;QAAEC,SAAS,EAAE,MAAM;QAAEC,OAAO,EAAE;MAAE,CAAE;MAAAH,QAAA,GACpD9B,QAAQ,CAACkC,GAAG,CAAC,CAACC,CAAC,EAAEC,CAAC,kBACjBvC,OAAA;QAAa6B,KAAK,EAAE;UAAEW,YAAY,EAAE;QAAE,CAAE;QAAAP,QAAA,gBACtCjC,OAAA;UAAAiC,QAAA,GAAIK,CAAC,CAAC1B,IAAI,KAAK,MAAM,GAAG,KAAK,GAAG,IAAI,EAAC,IAAE;QAAA;UAAA6B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC,eAC3C5C,OAAA;UAAAiC,QAAA,EAAOK,CAAC,CAACzB;QAAO;UAAA4B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC;MAAA,GAFhBL,CAAC;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAGN,CACN,CAAC,EACDrC,OAAO,iBAAIP,OAAA;QAAAiC,QAAA,EAAK;MAAW;QAAAQ,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC/B,CAAC,eACN5C,OAAA;MAAK6B,KAAK,EAAE;QAAEC,OAAO,EAAE,MAAM;QAAEM,OAAO,EAAE,CAAC;QAAES,SAAS,EAAE;MAAiB,CAAE;MAAAZ,QAAA,gBACvEjC,OAAA;QACE6B,KAAK,EAAE;UAAEK,IAAI,EAAE,CAAC;UAAEY,WAAW,EAAE;QAAE,CAAE;QACnCC,KAAK,EAAE1C,KAAM;QACb2C,QAAQ,EAAGtB,CAAC,IAAKpB,QAAQ,CAACoB,CAAC,CAACuB,MAAM,CAACF,KAAK,CAAE;QAC1CG,SAAS,EAAGxB,CAAC,IAAK;UAChB,IAAIA,CAAC,CAACyB,GAAG,KAAK,OAAO,EAAE1C,WAAW,CAAC,CAAC;QACtC;MAAE;QAAAgC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eACF5C,OAAA;QAAQoD,OAAO,EAAE3C,WAAY;QAAAwB,QAAA,EAAC;MAAI;QAAAQ,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACxC,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV;AAAC1C,EAAA,CAnDuBD,SAAS;AAAAoD,EAAA,GAATpD,SAAS;AAAA,IAAAoD,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}