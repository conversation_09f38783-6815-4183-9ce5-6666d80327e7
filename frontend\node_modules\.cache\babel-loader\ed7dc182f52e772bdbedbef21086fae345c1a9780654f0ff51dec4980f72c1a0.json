{"ast": null, "code": "import curry from '../utils/curry.js';\nimport isObject from '../utils/isObject.js';\n\n/**\n * validates the configuration object and informs about deprecation\n * @param {Object} config - the configuration object \n * @return {Object} config - the validated configuration object\n */\n\nfunction validateConfig(config) {\n  if (!config) errorHandler('configIsRequired');\n  if (!isObject(config)) errorHandler('configType');\n  if (config.urls) {\n    informAboutDeprecation();\n    return {\n      paths: {\n        vs: config.urls.monacoBase\n      }\n    };\n  }\n  return config;\n}\n/**\n * logs deprecation message\n */\n\nfunction informAboutDeprecation() {\n  console.warn(errorMessages.deprecation);\n}\nfunction throwError(errorMessages, type) {\n  throw new Error(errorMessages[type] || errorMessages[\"default\"]);\n}\nvar errorMessages = {\n  configIsRequired: 'the configuration object is required',\n  configType: 'the configuration object should be an object',\n  \"default\": 'an unknown error accured in `@monaco-editor/loader` package',\n  deprecation: \"Deprecation warning!\\n    You are using deprecated way of configuration.\\n\\n    Instead of using\\n      monaco.config({ urls: { monacoBase: '...' } })\\n    use\\n      monaco.config({ paths: { vs: '...' } })\\n\\n    For more please check the link https://github.com/suren-atoyan/monaco-loader#config\\n  \"\n};\nvar errorHandler = curry(throwError)(errorMessages);\nvar validators = {\n  config: validateConfig\n};\nexport default validators;\nexport { errorHandler, errorMessages };", "map": {"version": 3, "names": ["curry", "isObject", "validateConfig", "config", "<PERSON><PERSON><PERSON><PERSON>", "urls", "informAboutDeprecation", "paths", "vs", "monacoBase", "console", "warn", "errorMessages", "deprecation", "throwError", "type", "Error", "configIsRequired", "configType", "validators"], "sources": ["D:/Documents_2/New Coding/CoderBuddy/CoderBuddy/frontend/node_modules/@monaco-editor/loader/lib/es/validators/index.js"], "sourcesContent": ["import curry from '../utils/curry.js';\nimport isObject from '../utils/isObject.js';\n\n/**\n * validates the configuration object and informs about deprecation\n * @param {Object} config - the configuration object \n * @return {Object} config - the validated configuration object\n */\n\nfunction validateConfig(config) {\n  if (!config) errorHandler('configIsRequired');\n  if (!isObject(config)) errorHandler('configType');\n\n  if (config.urls) {\n    informAboutDeprecation();\n    return {\n      paths: {\n        vs: config.urls.monacoBase\n      }\n    };\n  }\n\n  return config;\n}\n/**\n * logs deprecation message\n */\n\n\nfunction informAboutDeprecation() {\n  console.warn(errorMessages.deprecation);\n}\n\nfunction throwError(errorMessages, type) {\n  throw new Error(errorMessages[type] || errorMessages[\"default\"]);\n}\n\nvar errorMessages = {\n  configIsRequired: 'the configuration object is required',\n  configType: 'the configuration object should be an object',\n  \"default\": 'an unknown error accured in `@monaco-editor/loader` package',\n  deprecation: \"Deprecation warning!\\n    You are using deprecated way of configuration.\\n\\n    Instead of using\\n      monaco.config({ urls: { monacoBase: '...' } })\\n    use\\n      monaco.config({ paths: { vs: '...' } })\\n\\n    For more please check the link https://github.com/suren-atoyan/monaco-loader#config\\n  \"\n};\nvar errorHandler = curry(throwError)(errorMessages);\nvar validators = {\n  config: validateConfig\n};\n\nexport default validators;\nexport { errorHandler, errorMessages };\n"], "mappings": "AAAA,OAAOA,KAAK,MAAM,mBAAmB;AACrC,OAAOC,QAAQ,MAAM,sBAAsB;;AAE3C;AACA;AACA;AACA;AACA;;AAEA,SAASC,cAAcA,CAACC,MAAM,EAAE;EAC9B,IAAI,CAACA,MAAM,EAAEC,YAAY,CAAC,kBAAkB,CAAC;EAC7C,IAAI,CAACH,QAAQ,CAACE,MAAM,CAAC,EAAEC,YAAY,CAAC,YAAY,CAAC;EAEjD,IAAID,MAAM,CAACE,IAAI,EAAE;IACfC,sBAAsB,CAAC,CAAC;IACxB,OAAO;MACLC,KAAK,EAAE;QACLC,EAAE,EAAEL,MAAM,CAACE,IAAI,CAACI;MAClB;IACF,CAAC;EACH;EAEA,OAAON,MAAM;AACf;AACA;AACA;AACA;;AAGA,SAASG,sBAAsBA,CAAA,EAAG;EAChCI,OAAO,CAACC,IAAI,CAACC,aAAa,CAACC,WAAW,CAAC;AACzC;AAEA,SAASC,UAAUA,CAACF,aAAa,EAAEG,IAAI,EAAE;EACvC,MAAM,IAAIC,KAAK,CAACJ,aAAa,CAACG,IAAI,CAAC,IAAIH,aAAa,CAAC,SAAS,CAAC,CAAC;AAClE;AAEA,IAAIA,aAAa,GAAG;EAClBK,gBAAgB,EAAE,sCAAsC;EACxDC,UAAU,EAAE,8CAA8C;EAC1D,SAAS,EAAE,6DAA6D;EACxEL,WAAW,EAAE;AACf,CAAC;AACD,IAAIT,YAAY,GAAGJ,KAAK,CAACc,UAAU,CAAC,CAACF,aAAa,CAAC;AACnD,IAAIO,UAAU,GAAG;EACfhB,MAAM,EAAED;AACV,CAAC;AAED,eAAeiB,UAAU;AACzB,SAASf,YAAY,EAAEQ,aAAa", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}