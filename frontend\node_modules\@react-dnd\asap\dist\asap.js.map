{"version": 3, "sources": ["../src/asap.ts"], "sourcesContent": ["import { AsapQueue } from './AsapQueue.js'\nimport { TaskFactory } from './TaskFactory.js'\nimport type { TaskFn } from './types.js'\n\nconst asapQueue = new AsapQueue()\nconst taskFactory = new TaskFactory(asapQueue.registerPendingError)\n\n/**\n * Calls a task as soon as possible after returning, in its own event, with priority\n * over other events like animation, reflow, and repaint. An error thrown from an\n * event will not interrupt, nor even substantially slow down the processing of\n * other events, but will be rather postponed to a lower priority event.\n * @param {{call}} task A callable object, typically a function that takes no\n * arguments.\n */\nexport function asap(task: TaskFn) {\n\tasapQueue.enqueueTask(taskFactory.create(task))\n}\n"], "names": ["AsapQueue", "TaskFactory", "asapQueue", "taskFactory", "registerPendingError", "asap", "task", "enqueueTask", "create"], "mappings": "AAAA,SAASA,SAAS,QAAQ,gBAAgB,CAAA;AAC1C,SAASC,WAAW,QAAQ,kBAAkB,CAAA;AAG9C,MAAMC,SAAS,GAAG,IAAIF,SAAS,EAAE;AACjC,MAAMG,WAAW,GAAG,IAAIF,WAAW,CAACC,SAAS,CAACE,oBAAoB,CAAC;AAEnE;;;;;;;GAOG,CACH,OAAO,SAASC,IAAI,CAACC,IAAY,EAAE;IAClCJ,SAAS,CAACK,WAAW,CAACJ,WAAW,CAACK,MAAM,CAACF,IAAI,CAAC,CAAC;CAC/C"}