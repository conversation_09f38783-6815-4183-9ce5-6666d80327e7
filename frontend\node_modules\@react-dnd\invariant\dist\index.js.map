{"version": 3, "sources": ["../src/index.ts"], "sourcesContent": ["/**\n * Use invariant() to assert state which your program assumes to be true.\n *\n * Provide sprintf-style format (only %s is supported) and arguments\n * to provide information about what broke and what you were\n * expecting.\n *\n * The invariant message will be stripped in production, but the invariant\n * will remain to ensure logic does not differ in production.\n */\n\nexport function invariant(condition: any, format: string, ...args: any[]) {\n\tif (isProduction()) {\n\t\tif (format === undefined) {\n\t\t\tthrow new Error('invariant requires an error message argument')\n\t\t}\n\t}\n\n\tif (!condition) {\n\t\tlet error\n\t\tif (format === undefined) {\n\t\t\terror = new Error(\n\t\t\t\t'Minified exception occurred; use the non-minified dev environment ' +\n\t\t\t\t\t'for the full error message and additional helpful warnings.',\n\t\t\t)\n\t\t} else {\n\t\t\tlet argIndex = 0\n\t\t\terror = new Error(\n\t\t\t\tformat.replace(/%s/g, function () {\n\t\t\t\t\treturn args[argIndex++]\n\t\t\t\t}),\n\t\t\t)\n\t\t\terror.name = 'Invariant Violation'\n\t\t}\n\n\t\t;(error as any).framesToPop = 1 // we don't care about invariant's own frame\n\t\tthrow error\n\t}\n}\n\nfunction isProduction() {\n\treturn (\n\t\ttypeof process !== 'undefined' && process.env['NODE_ENV'] === 'production'\n\t)\n}\n"], "names": ["invariant", "condition", "format", "args", "isProduction", "undefined", "Error", "error", "argIndex", "replace", "name", "framesToPop", "process", "env"], "mappings": "AAAA;;;;;;;;;GASG,CAEH,OAAO,SAASA,SAAS,CAACC,SAAc,EAAEC,MAAc,EAAE,GAAGC,IAAI,AAAO,EAAE;IACzE,IAAIC,YAAY,EAAE,EAAE;QACnB,IAAIF,MAAM,KAAKG,SAAS,EAAE;YACzB,MAAM,IAAIC,KAAK,CAAC,8CAA8C,CAAC,CAAA;SAC/D;KACD;IAED,IAAI,CAACL,SAAS,EAAE;QACf,IAAIM,KAAK;QACT,IAAIL,MAAM,KAAKG,SAAS,EAAE;YACzBE,KAAK,GAAG,IAAID,KAAK,CAChB,oEAAoE,GACnE,6DAA6D,CAC9D;SACD,MAAM;YACN,IAAIE,QAAQ,GAAG,CAAC;YAChBD,KAAK,GAAG,IAAID,KAAK,CAChBJ,MAAM,CAACO,OAAO,QAAQ,WAAY;gBACjC,OAAON,IAAI,CAACK,QAAQ,EAAE,CAAC,CAAA;aACvB,CAAC,CACF;YACDD,KAAK,CAACG,IAAI,GAAG,qBAAqB;SAClC;QAEA,AAACH,KAAK,CAASI,WAAW,GAAG,CAAC,CAAC,4CAA4C;QAA7C;QAC/B,MAAMJ,KAAK,CAAA;KACX;CACD;AAED,SAASH,YAAY,GAAG;IACvB,OACC,OAAOQ,OAAO,KAAK,WAAW,IAAIA,OAAO,CAACC,GAAG,CAAC,UAAU,CAAC,KAAK,YAAY,CAC1E;CACD"}