{"version": 3, "sources": ["../src/index.ts"], "sourcesContent": ["export function shallowEqual<T>(\n\tobjA: T,\n\tobjB: T,\n\tcompare?: (a: T, b: T, key?: string) => boolean | void,\n\tcompareContext?: any,\n) {\n\tlet compareResult = compare\n\t\t? compare.call(compareContext, objA, objB)\n\t\t: void 0\n\tif (compareResult !== void 0) {\n\t\treturn !!compareResult\n\t}\n\n\tif (objA === objB) {\n\t\treturn true\n\t}\n\n\tif (typeof objA !== 'object' || !objA || typeof objB !== 'object' || !objB) {\n\t\treturn false\n\t}\n\n\tconst keysA = Object.keys(objA)\n\tconst keysB = Object.keys(objB)\n\n\tif (keysA.length !== keysB.length) {\n\t\treturn false\n\t}\n\n\tconst bHasOwnProperty = Object.prototype.hasOwnProperty.bind(objB)\n\n\t// Test for A's keys different from B.\n\tfor (let idx = 0; idx < keysA.length; idx++) {\n\t\tconst key = keysA[idx] as string\n\n\t\tif (!bHasOwnProperty(key)) {\n\t\t\treturn false\n\t\t}\n\n\t\tconst valueA = (objA as any)[key]\n\t\tconst valueB = (objB as any)[key]\n\n\t\tcompareResult = compare\n\t\t\t? compare.call(compareContext, valueA, valueB, key)\n\t\t\t: void 0\n\n\t\tif (\n\t\t\tcompareResult === false ||\n\t\t\t(compareResult === void 0 && valueA !== valueB)\n\t\t) {\n\t\t\treturn false\n\t\t}\n\t}\n\n\treturn true\n}\n"], "names": ["shallowEqual", "objA", "objB", "compare", "compareContext", "compareResult", "call", "keysA", "Object", "keys", "keysB", "length", "bHasOwnProperty", "prototype", "hasOwnProperty", "bind", "idx", "key", "valueA", "valueB"], "mappings": "AAAA,OAAO,SAASA,YAAY,CAC3BC,IAAO,EACPC,IAAO,EACPC,OAAsD,EACtDC,cAAoB,EACnB;IACD,IAAIC,aAAa,GAAGF,OAAO,GACxBA,OAAO,CAACG,IAAI,CAACF,cAAc,EAAEH,IAAI,EAAEC,IAAI,CAAC,GACxC,KAAK,CAAC;IACT,IAAIG,aAAa,KAAK,KAAK,CAAC,EAAE;QAC7B,OAAO,CAAC,CAACA,aAAa,CAAA;KACtB;IAED,IAAIJ,IAAI,KAAKC,IAAI,EAAE;QAClB,OAAO,IAAI,CAAA;KACX;IAED,IAAI,OAAOD,IAAI,KAAK,QAAQ,IAAI,CAACA,IAAI,IAAI,OAAOC,IAAI,KAAK,QAAQ,IAAI,CAACA,IAAI,EAAE;QAC3E,OAAO,KAAK,CAAA;KACZ;IAED,MAAMK,KAAK,GAAGC,MAAM,CAACC,IAAI,CAACR,IAAI,CAAC;IAC/B,MAAMS,KAAK,GAAGF,MAAM,CAACC,IAAI,CAACP,IAAI,CAAC;IAE/B,IAAIK,KAAK,CAACI,MAAM,KAAKD,KAAK,CAACC,MAAM,EAAE;QAClC,OAAO,KAAK,CAAA;KACZ;IAED,MAAMC,eAAe,GAAGJ,MAAM,CAACK,SAAS,CAACC,cAAc,CAACC,IAAI,CAACb,IAAI,CAAC;IAElE,sCAAsC;IACtC,IAAK,IAAIc,GAAG,GAAG,CAAC,EAAEA,GAAG,GAAGT,KAAK,CAACI,MAAM,EAAEK,GAAG,EAAE,CAAE;QAC5C,MAAMC,GAAG,GAAGV,KAAK,CAACS,GAAG,CAAC,AAAU;QAEhC,IAAI,CAACJ,eAAe,CAACK,GAAG,CAAC,EAAE;YAC1B,OAAO,KAAK,CAAA;SACZ;QAED,MAAMC,MAAM,GAAG,AAACjB,IAAI,AAAQ,CAACgB,GAAG,CAAC;QACjC,MAAME,MAAM,GAAG,AAACjB,IAAI,AAAQ,CAACe,GAAG,CAAC;QAEjCZ,aAAa,GAAGF,OAAO,GACpBA,OAAO,CAACG,IAAI,CAACF,cAAc,EAAEc,MAAM,EAAEC,MAAM,EAAEF,GAAG,CAAC,GACjD,KAAK,CAAC;QAET,IACCZ,aAAa,KAAK,KAAK,IACtBA,aAAa,KAAK,KAAK,CAAC,IAAIa,MAAM,KAAKC,MAAM,AAAC,EAC9C;YACD,OAAO,KAAK,CAAA;SACZ;KACD;IAED,OAAO,IAAI,CAAA;CACX"}