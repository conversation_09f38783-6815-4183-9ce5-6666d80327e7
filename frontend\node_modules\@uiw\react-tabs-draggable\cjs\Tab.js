"use strict";

var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault")["default"];
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.Tab = exports.ItemTypes = void 0;
var _objectSpread2 = _interopRequireDefault(require("@babel/runtime/helpers/objectSpread2"));
var _toConsumableArray2 = _interopRequireDefault(require("@babel/runtime/helpers/toConsumableArray"));
var _slicedToArray2 = _interopRequireDefault(require("@babel/runtime/helpers/slicedToArray"));
var _objectWithoutProperties2 = _interopRequireDefault(require("@babel/runtime/helpers/objectWithoutProperties"));
var _react = require("react");
var _immutabilityHelper = _interopRequireDefault(require("immutability-helper"));
var _store = require("./store");
var _reactDnd = require("react-dnd");
var _jsxRuntime = require("react/jsx-runtime");
var _excluded = ["children", "id", "index", "dragableY"];
var _this = void 0;
var ItemTypes = {
  Tab: 'wtabs'
};
exports.ItemTypes = ItemTypes;
var Tab = function Tab(_ref) {
  var children = _ref.children,
    id = _ref.id,
    index = _ref.index,
    _ref$dragableY = _ref.dragableY,
    dragableY = _ref$dragableY === void 0 ? false : _ref$dragableY,
    props = (0, _objectWithoutProperties2["default"])(_ref, _excluded);
  var _useDataContext = (0, _store.useDataContext)(),
    state = _useDataContext.state,
    onTabClick = _useDataContext.onTabClick,
    onTabDrop = _useDataContext.onTabDrop,
    dispatch = _useDataContext.dispatch;
  var ref = (0, _react.useRef)(null);
  var _useDrop = (0, _reactDnd.useDrop)({
      accept: ItemTypes.Tab,
      collect: function collect(monitor) {
        return {
          handlerId: monitor.getHandlerId()
        };
      },
      hover: function hover(item, monitor) {
        if (!ref.current || !state.data) {
          return;
        }
        var dragIndex = item.index;
        var hoverIndex = index || 0;
        // 不要用自己替换项目
        if (dragIndex === hoverIndex) {
          return;
        }
        // 确定屏幕上的矩形
        var hoverBoundingRect = ref.current.getBoundingClientRect();
        // 获取垂直中间
        var hoverMiddleX = (hoverBoundingRect.right - hoverBoundingRect.left) / 2;
        // 确定鼠标位置
        var clientOffset = monitor.getClientOffset();
        // if (!clientOffset) return;
        // 将像素移到顶部
        var hoverClientX = clientOffset.x - hoverBoundingRect.left;
        // Only perform the move when the mouse has crossed half of the items height
        // When dragging downwards, only move when the cursor is below 50%
        // When dragging upwards, only move when the cursor is above 50%
        // Dragging downwards
        if (dragIndex < hoverIndex && hoverClientX < hoverMiddleX && dragableY !== true) {
          return;
        }
        // Dragging upwards
        if (dragIndex > hoverIndex && hoverClientX > hoverMiddleX) {
          return;
        }
        var newdata = (0, _immutabilityHelper["default"])(state.data, {
          $splice: [[dragIndex, 1], [hoverIndex, 0, state.data[dragIndex]]]
        });
        dispatch({
          data: (0, _toConsumableArray2["default"])(newdata)
        });
        item.index = hoverIndex;
      }
    }),
    _useDrop2 = (0, _slicedToArray2["default"])(_useDrop, 2),
    handlerId = _useDrop2[0].handlerId,
    drop = _useDrop2[1];
  var _useDrag = (0, _reactDnd.useDrag)(function () {
      return {
        type: ItemTypes.Tab,
        item: function item() {
          return {
            id: id,
            index: index
          };
        },
        end: function end(item, monitor) {
          var clientOffset = monitor.getClientOffset();
          onTabDrop && onTabDrop(id, item.index, clientOffset);
        },
        collect: function collect(monitor) {
          return {
            data: monitor.getItem(),
            targetIds: monitor.getTargetIds(),
            isDragging: monitor.isDragging()
          };
        }
      };
    }, [id, index]),
    _useDrag2 = (0, _slicedToArray2["default"])(_useDrag, 2),
    isDragging = _useDrag2[0].isDragging,
    drag = _useDrag2[1];
  var opacity = isDragging ? 0.001 : 1;
  if (props.draggable !== false) {
    drag(drop(ref));
  }
  var handleClick = function handleClick(evn) {
    dispatch({
      activeKey: id
    });
    onTabClick && onTabClick(id, evn);
  };
  return /*#__PURE__*/(0, _jsxRuntime.jsx)("div", (0, _objectSpread2["default"])((0, _objectSpread2["default"])({}, props), {}, {
    onClick: handleClick.bind(_this),
    ref: ref,
    style: (0, _objectSpread2["default"])((0, _objectSpread2["default"])({}, props.style), {}, {
      opacity: opacity
    }),
    className: "w-tabs-draggable-item ".concat(props.className || '').concat(state.activeKey === id ? ' w-active' : ''),
    "data-handler-id": handlerId,
    children: children
  }));
};
exports.Tab = Tab;