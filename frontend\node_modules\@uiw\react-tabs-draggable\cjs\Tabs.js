"use strict";

var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault")["default"];
var _interopRequireWildcard = require("@babel/runtime/helpers/interopRequireWildcard")["default"];
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.Tabs = void 0;
var _objectSpread2 = _interopRequireDefault(require("@babel/runtime/helpers/objectSpread2"));
var _slicedToArray2 = _interopRequireDefault(require("@babel/runtime/helpers/slicedToArray"));
var _objectWithoutProperties2 = _interopRequireDefault(require("@babel/runtime/helpers/objectWithoutProperties"));
var _react = _interopRequireWildcard(require("react"));
var _reactDnd = require("react-dnd");
var _store = require("./store");
var _Tab = require("./Tab");
var _jsxRuntime = require("react/jsx-runtime");
var _excluded = ["children", "activeKey"],
  _excluded2 = ["element"];
var Tabs = function Tabs(_ref) {
  var children = _ref.children,
    activeKey = _ref.activeKey,
    props = (0, _objectWithoutProperties2["default"])(_ref, _excluded);
  var _useDataContext = (0, _store.useDataContext)(),
    state = _useDataContext.state,
    dispatch = _useDataContext.dispatch;
  var _useDrop = (0, _reactDnd.useDrop)(function () {
      return {
        accept: _Tab.ItemTypes.Tab
      };
    }),
    _useDrop2 = (0, _slicedToArray2["default"])(_useDrop, 2),
    drop = _useDrop2[1];
  (0, _react.useEffect)(function () {
    return dispatch({
      activeKey: activeKey
    });
  }, [activeKey]);
  (0, _react.useLayoutEffect)(function () {
    if (children) {
      var data = [];
      _react["default"].Children.toArray(children).forEach(function (item) {
        if ( /*#__PURE__*/(0, _react.isValidElement)(item)) {
          data.push((0, _objectSpread2["default"])((0, _objectSpread2["default"])({}, item.props), {}, {
            element: item
          }));
        }
      });
      dispatch({
        data: data
      });
    }
  }, [children]);
  return /*#__PURE__*/(0, _jsxRuntime.jsx)("div", (0, _objectSpread2["default"])((0, _objectSpread2["default"])({}, props), {}, {
    ref: drop,
    className: "w-tabs-draggable ".concat(props.className || ''),
    style: (0, _objectSpread2["default"])({
      display: 'flex'
    }, props.style),
    children: state.data && state.data.length > 0 && state.data.map(function (_ref2, idx) {
      var element = _ref2.element,
        child = (0, _objectWithoutProperties2["default"])(_ref2, _excluded2);
      if ( /*#__PURE__*/(0, _react.isValidElement)(element)) {
        return /*#__PURE__*/_react["default"].cloneElement(element, (0, _objectSpread2["default"])((0, _objectSpread2["default"])({}, child), {}, {
          index: idx
        }));
      }
    })
  }));
};
exports.Tabs = Tabs;