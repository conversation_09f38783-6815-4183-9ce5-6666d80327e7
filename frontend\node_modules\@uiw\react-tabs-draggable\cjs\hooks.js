"use strict";

Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.useEventCallback = void 0;
var _react = require("react");
var useEventCallback = function useEventCallback(fn) {
  var ref = (0, _react.useRef)(fn);
  (0, _react.useLayoutEffect)(function () {
    ref.current = fn;
  });
  return (0, _react.useMemo)(function () {
    return function () {
      var current = ref.current;
      return current && current.apply(void 0, arguments);
    };
  }, []);
};
exports.useEventCallback = useEventCallback;