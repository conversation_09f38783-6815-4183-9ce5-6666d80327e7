import { FC, PropsWithChildren } from 'react';
import type { XYCoord } from 'dnd-core';
export * from './Tab';
export * from './hooks';
export interface TabsProps extends React.DetailedHTMLProps<React.HTMLAttributes<HTMLDivElement>, HTMLDivElement> {
    activeKey?: string;
    onTabClick?: (id: string, evn: React.MouseEvent<HTMLDivElement>) => void;
    /**
     * Optional. Called when a compatible item is dropped on the target.
     */
    onTabDrop?: (id: string, index?: number, offset?: XYCoord | null) => void;
}
declare const TabContainer: FC<PropsWithChildren<TabsProps>>;
export default TabContainer;
