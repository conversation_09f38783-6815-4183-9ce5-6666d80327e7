"use strict";

var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault")["default"];
Object.defineProperty(exports, "__esModule", {
  value: true
});
var _exportNames = {};
exports["default"] = void 0;
var _objectSpread2 = _interopRequireDefault(require("@babel/runtime/helpers/objectSpread2"));
var _objectWithoutProperties2 = _interopRequireDefault(require("@babel/runtime/helpers/objectWithoutProperties"));
var _reactDnd = require("react-dnd");
var _reactDndHtml5Backend = require("react-dnd-html5-backend");
var _Tabs = require("./Tabs");
var _store = require("./store");
var _hooks = require("./hooks");
Object.keys(_hooks).forEach(function (key) {
  if (key === "default" || key === "__esModule") return;
  if (Object.prototype.hasOwnProperty.call(_exportNames, key)) return;
  if (key in exports && exports[key] === _hooks[key]) return;
  Object.defineProperty(exports, key, {
    enumerable: true,
    get: function get() {
      return _hooks[key];
    }
  });
});
var _jsxRuntime = require("react/jsx-runtime");
var _Tab = require("./Tab");
Object.keys(_Tab).forEach(function (key) {
  if (key === "default" || key === "__esModule") return;
  if (Object.prototype.hasOwnProperty.call(_exportNames, key)) return;
  if (key in exports && exports[key] === _Tab[key]) return;
  Object.defineProperty(exports, key, {
    enumerable: true,
    get: function get() {
      return _Tab[key];
    }
  });
});
var _excluded = ["activeKey", "onTabClick", "onTabDrop"];
var TabContainer = function TabContainer(_ref) {
  var activeKey = _ref.activeKey,
    onTabClick = _ref.onTabClick,
    onTabDrop = _ref.onTabDrop,
    props = (0, _objectWithoutProperties2["default"])(_ref, _excluded);
  var tabClick = (0, _hooks.useEventCallback)(onTabClick);
  var tabDrop = (0, _hooks.useEventCallback)(onTabDrop);
  return /*#__PURE__*/(0, _jsxRuntime.jsx)(_reactDnd.DndProvider, {
    backend: _reactDndHtml5Backend.HTML5Backend,
    context: window,
    children: /*#__PURE__*/(0, _jsxRuntime.jsx)(_store.Provider, {
      init: {
        data: [],
        activeKey: activeKey,
        onTabClick: tabClick,
        onTabDrop: tabDrop
      },
      children: /*#__PURE__*/(0, _jsxRuntime.jsx)(_Tabs.Tabs, (0, _objectSpread2["default"])((0, _objectSpread2["default"])({}, props), {}, {
        activeKey: activeKey
      }))
    })
  });
};
var _default = TabContainer;
exports["default"] = _default;