"use strict";

var _interopRequireWildcard = require("@babel/runtime/helpers/interopRequireWildcard")["default"];
var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault")["default"];
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.reducer = exports.initialState = exports.Provider = exports.Context = void 0;
exports.useDataContext = useDataContext;
var _slicedToArray2 = _interopRequireDefault(require("@babel/runtime/helpers/slicedToArray"));
var _objectSpread2 = _interopRequireDefault(require("@babel/runtime/helpers/objectSpread2"));
var _react = _interopRequireWildcard(require("react"));
var _jsxRuntime = require("react/jsx-runtime");
var initialState = {
  activeKey: '',
  data: []
};
exports.initialState = initialState;
var reducer = function reducer(state, action) {
  return (0, _objectSpread2["default"])((0, _objectSpread2["default"])({}, state), action);
};
exports.reducer = reducer;
var Context = /*#__PURE__*/(0, _react.createContext)({
  state: initialState,
  dispatch: function dispatch() {
    return null;
  }
});
exports.Context = Context;
var Provider = function Provider(_ref) {
  var children = _ref.children,
    init = _ref.init;
  var _useReducer = (0, _react.useReducer)(reducer, init || initialState),
    _useReducer2 = (0, _slicedToArray2["default"])(_useReducer, 2),
    state = _useReducer2[0],
    dispatch = _useReducer2[1];
  return /*#__PURE__*/(0, _jsxRuntime.jsx)(Context.Provider, {
    value: {
      state: state,
      dispatch: dispatch
    },
    children: children
  });
};
exports.Provider = Provider;
function useDataContext() {
  var _useContext = (0, _react.useContext)(Context),
    state = _useContext.state,
    dispatch = _useContext.dispatch;
  return (0, _objectSpread2["default"])((0, _objectSpread2["default"])({}, state), {}, {
    state: state,
    dispatch: dispatch
  });
}