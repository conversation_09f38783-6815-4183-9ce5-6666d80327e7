import { FC, PropsWithChildren } from 'react';
export declare const ItemTypes: {
    Tab: string;
};
export interface TabProps extends React.DetailedHTMLProps<React.HTMLAttributes<HTMLDivElement>, HTMLDivElement> {
    id: string;
    index?: number;
    /** Whether the Y axis can be dragged */
    dragableY?: boolean;
}
export interface DragItem {
    index: number;
    id: string;
    type: string;
}
export declare const Tab: FC<PropsWithChildren<TabProps>>;
