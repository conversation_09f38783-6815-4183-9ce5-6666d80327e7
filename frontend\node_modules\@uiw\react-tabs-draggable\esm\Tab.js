import _extends from "@babel/runtime/helpers/extends";
import _objectWithoutPropertiesLoose from "@babel/runtime/helpers/objectWithoutPropertiesLoose";
var _excluded = ["children", "id", "index", "dragableY"];
import { useRef } from 'react';
import update from 'immutability-helper';
import { useDataContext } from './store';
import { useDrag, useDrop } from 'react-dnd';
import { jsx as _jsx } from "react/jsx-runtime";
export var ItemTypes = {
  Tab: 'wtabs'
};
export var Tab = _ref => {
  var {
      children,
      id,
      index,
      dragableY = false
    } = _ref,
    props = _objectWithoutPropertiesLoose(_ref, _excluded);
  var {
    state,
    onTabClick,
    onTabDrop,
    dispatch
  } = useDataContext();
  var ref = useRef(null);
  var [{
    handlerId
  }, drop] = useDrop({
    accept: ItemTypes.Tab,
    collect(monitor) {
      return {
        handlerId: monitor.getHandlerId()
      };
    },
    hover(item, monitor) {
      if (!ref.current || !state.data) {
        return;
      }
      var dragIndex = item.index;
      var hoverIndex = index || 0;
      // 不要用自己替换项目
      if (dragIndex === hoverIndex) {
        return;
      }
      // 确定屏幕上的矩形
      var hoverBoundingRect = ref.current.getBoundingClientRect();
      // 获取垂直中间
      var hoverMiddleX = (hoverBoundingRect.right - hoverBoundingRect.left) / 2;
      // 确定鼠标位置
      var clientOffset = monitor.getClientOffset();
      // if (!clientOffset) return;
      // 将像素移到顶部
      var hoverClientX = clientOffset.x - hoverBoundingRect.left;
      // Only perform the move when the mouse has crossed half of the items height
      // When dragging downwards, only move when the cursor is below 50%
      // When dragging upwards, only move when the cursor is above 50%
      // Dragging downwards
      if (dragIndex < hoverIndex && hoverClientX < hoverMiddleX && dragableY !== true) {
        return;
      }
      // Dragging upwards
      if (dragIndex > hoverIndex && hoverClientX > hoverMiddleX) {
        return;
      }
      var newdata = update(state.data, {
        $splice: [[dragIndex, 1], [hoverIndex, 0, state.data[dragIndex]]]
      });
      dispatch({
        data: [...newdata]
      });
      item.index = hoverIndex;
    }
  });
  var [{
    isDragging
  }, drag] = useDrag(() => ({
    type: ItemTypes.Tab,
    item: () => {
      return {
        id,
        index
      };
    },
    end: (item, monitor) => {
      var clientOffset = monitor.getClientOffset();
      onTabDrop && onTabDrop(id, item.index, clientOffset);
    },
    collect: monitor => {
      return {
        data: monitor.getItem(),
        targetIds: monitor.getTargetIds(),
        isDragging: monitor.isDragging()
      };
    }
  }), [id, index]);
  var opacity = isDragging ? 0.001 : 1;
  if (props.draggable !== false) {
    drag(drop(ref));
  }
  var handleClick = evn => {
    dispatch({
      activeKey: id
    });
    onTabClick && onTabClick(id, evn);
  };
  return /*#__PURE__*/_jsx("div", _extends({}, props, {
    onClick: handleClick.bind(this),
    ref: ref,
    style: _extends({}, props.style, {
      opacity
    }),
    className: "w-tabs-draggable-item " + (props.className || '') + (state.activeKey === id ? ' w-active' : ''),
    "data-handler-id": handlerId,
    children: children
  }));
};