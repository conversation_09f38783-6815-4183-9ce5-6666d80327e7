import _extends from "@babel/runtime/helpers/extends";
import _objectWithoutPropertiesLoose from "@babel/runtime/helpers/objectWithoutPropertiesLoose";
var _excluded = ["children", "activeKey"],
  _excluded2 = ["element"];
import React, { isValidElement, useEffect, useLayoutEffect } from 'react';
import { useDrop } from 'react-dnd';
import { useDataContext } from './store';
import { ItemTypes } from './Tab';
import { jsx as _jsx } from "react/jsx-runtime";
export var Tabs = _ref => {
  var {
      children,
      activeKey
    } = _ref,
    props = _objectWithoutPropertiesLoose(_ref, _excluded);
  var {
    state,
    dispatch
  } = useDataContext();
  var [, drop] = useDrop(() => ({
    accept: ItemTypes.Tab
  }));
  useEffect(() => dispatch({
    activeKey
  }), [activeKey]);
  useLayoutEffect(() => {
    if (children) {
      var data = [];
      React.Children.toArray(children).forEach(item => {
        if ( /*#__PURE__*/isValidElement(item)) {
          data.push(_extends({}, item.props, {
            element: item
          }));
        }
      });
      dispatch({
        data
      });
    }
  }, [children]);
  return /*#__PURE__*/_jsx("div", _extends({}, props, {
    ref: drop,
    className: "w-tabs-draggable " + (props.className || ''),
    style: _extends({
      display: 'flex'
    }, props.style),
    children: state.data && state.data.length > 0 && state.data.map((_ref2, idx) => {
      var {
          element
        } = _ref2,
        child = _objectWithoutPropertiesLoose(_ref2, _excluded2);
      if ( /*#__PURE__*/isValidElement(element)) {
        return /*#__PURE__*/React.cloneElement(element, _extends({}, child, {
          index: idx
        }));
      }
    })
  }));
};