import _extends from "@babel/runtime/helpers/extends";
import _objectWithoutPropertiesLoose from "@babel/runtime/helpers/objectWithoutPropertiesLoose";
var _excluded = ["activeKey", "onTabClick", "onTabDrop"];
import { DndProvider } from 'react-dnd';
import { HTML5Backend } from 'react-dnd-html5-backend';
import { Tabs } from './Tabs';
import { Provider } from './store';
import { useEventCallback } from './hooks';
import { jsx as _jsx } from "react/jsx-runtime";
export * from './Tab';
export * from './hooks';
var TabContainer = _ref => {
  var {
      activeKey,
      onTabClick,
      onTabDrop
    } = _ref,
    props = _objectWithoutPropertiesLoose(_ref, _excluded);
  var tabClick = useEventCallback(onTabClick);
  var tabDrop = useEventCallback(onTabDrop);
  return /*#__PURE__*/_jsx(DndProvider, {
    backend: HTML5Backend,
    context: window,
    children: /*#__PURE__*/_jsx(Provider, {
      init: {
        data: [],
        activeKey,
        onTabClick: tabClick,
        onTabDrop: tabDrop
      },
      children: /*#__PURE__*/_jsx(Tabs, _extends({}, props, {
        activeKey: activeKey
      }))
    })
  });
};
export default TabContainer;