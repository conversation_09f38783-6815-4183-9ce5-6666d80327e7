import React, { FC, PropsWithChildren } from 'react';
import { TabsProps } from './';
export interface InitialState extends Pick<TabsProps, 'onTabClick' | 'onTabDrop'> {
    activeKey?: string;
    data?: Array<{
        id: string;
        children: React.ReactElement;
        element: HTMLElement;
    }>;
}
export declare const initialState: InitialState;
export declare const reducer: (state: Partial<InitialState>, action: Partial<InitialState>) => {
    activeKey?: string | undefined;
    data?: {
        id: string;
        children: React.ReactElement;
        element: HTMLElement;
    }[] | undefined;
    onTabClick?: ((id: string, evn: React.MouseEvent<HTMLDivElement, MouseEvent>) => void) | undefined;
    onTabDrop?: ((id: string, index?: number | undefined, offset?: import("dnd-core").XYCoord | null | undefined) => void) | undefined;
};
export interface CreateContext {
    state: Partial<InitialState>;
    dispatch?: React.Dispatch<InitialState>;
}
export declare const Context: React.Context<CreateContext>;
export declare const Provider: FC<PropsWithChildren<{
    init: InitialState;
}>>;
export declare function useDataContext(): {
    state: Partial<InitialState>;
    dispatch: React.Dispatch<InitialState> | undefined;
    activeKey?: string | undefined;
    data?: {
        id: string;
        children: React.ReactElement<any, string | React.JSXElementConstructor<any>>;
        element: HTMLElement;
    }[] | undefined;
    onTabClick?: ((id: string, evn: React.MouseEvent<HTMLDivElement, MouseEvent>) => void) | undefined;
    onTabDrop?: ((id: string, index?: number | undefined, offset?: import("dnd-core").XYCoord | null | undefined) => void) | undefined;
};
