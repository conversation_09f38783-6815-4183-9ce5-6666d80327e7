import _extends from "@babel/runtime/helpers/extends";
import React, { createContext, useContext, useReducer } from 'react';
import { jsx as _jsx } from "react/jsx-runtime";
export var initialState = {
  activeKey: '',
  data: []
};
export var reducer = (state, action) => {
  return _extends({}, state, action);
};
export var Context = /*#__PURE__*/createContext({
  state: initialState,
  dispatch: () => null
});
export var Provider = _ref => {
  var {
    children,
    init
  } = _ref;
  var [state, dispatch] = useReducer(reducer, init || initialState);
  return /*#__PURE__*/_jsx(Context.Provider, {
    value: {
      state,
      dispatch
    },
    children: children
  });
};
export function useDataContext() {
  var {
    state,
    dispatch
  } = useContext(Context);
  return _extends({}, state, {
    state,
    dispatch
  });
}