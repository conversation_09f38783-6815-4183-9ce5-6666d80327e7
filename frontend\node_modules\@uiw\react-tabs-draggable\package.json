{"name": "@uiw/react-tabs-draggable", "version": "1.0.1", "description": "Draggable tabs for React.", "homepage": "https://uiwjs.github.io/react-tabs-draggable", "author": "kenny wong <<EMAIL>>", "license": "MIT", "main": "./cjs/index.js", "module": "./esm/index.js", "types": "./esm/index.d.ts", "scripts": {"watch": "tsbb watch src/*.tsx --use-babel", "build": "tsbb build src/*.tsx --use-babel", "test": "tsbb test --env=jsdom", "coverage": "tsbb test --env=jsdom --coverage --bail"}, "repository": {"type": "git", "url": "https://github.com/uiwjs/react-tabs-draggable.git"}, "files": ["README.md", "dist", "src", "esm", "cjs"], "peerDependencies": {"@babel/runtime": ">=7.11.0", "react": ">=16.8.0", "react-dom": ">=16.8.0"}, "dependencies": {"@babel/runtime": ">=7.11.0", "immutability-helper": "^3.1.1", "react-dnd": "^16.0.1", "react-dnd-html5-backend": "^16.0.1"}, "keywords": ["react", "draggable", "tabs", "react-tabs", "react-tabs-draggable"]}